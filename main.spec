# -*- mode: python ; coding: utf-8 -*-

import os

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('assets', 'assets'),
        ('gui', 'gui'),
        ('utils', 'utils'),
        ('database', 'database'),
        ('web', 'web'),
        ('fonts', 'fonts'),
        ('sample_data', 'sample_data'),
        ('company_settings.json', '.'),
        ('user_settings.json', '.'),
        ('theme_settings.json', '.'),
        ('modern_theme_settings.json', '.'),
        ('brand_settings.json', '.'),
        ('accounting.db', '.'),
        ('license.dat', '.') if os.path.exists('license.dat') else None,
        ('issued_licenses.json', '.') if os.path.exists('issued_licenses.json') else None,
        ('used_codes.dat', '.') if os.path.exists('used_codes.dat') else None,
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.QtChart',
        'PyQt5.QtPrintSupport',
        'sqlalchemy',
        'reportlab',
        'arabic_reshaper',
        'bidi',
        'xlsxwriter',
        'openpyxl',
        'cv2',
        'pyzbar',
        'werkzeug',
        'psutil',
        'cryptography',
        'flask',
        'schedule',
        'license_manager',
        'license_ui'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

# Filter out None values from datas
a.datas = [item for item in a.datas if item is not None]

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='نظام_المحاسبة',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    icon='assets\\icons.ico' if os.path.exists('assets\\icons.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='نظام_المحاسبة'
)
