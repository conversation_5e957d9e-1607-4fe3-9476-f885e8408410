/* 
تنسيقات متجاوبة للشاشات الصغيرة
Responsive styles for small screens
*/

/* تنسيقات عامة للشاشات الصغيرة */
QWidget {
    font-size: 11px;
}

/* تحسين الأزرار للشاشات الصغيرة */
QPushButton {
    min-height: 28px;
    max-height: 35px;
    padding: 4px 8px;
    font-size: 10px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    transform: scale(1.02);
}

/* تحسين حقول الإدخال */
QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
    min-height: 25px;
    max-height: 30px;
    padding: 3px 6px;
    font-size: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* تحسين الجداول للشاشات الصغيرة */
QTableWidget {
    font-size: 9px;
    gridline-color: #e0e0e0;
    border: 1px solid #ddd;
    border-radius: 6px;
}

QTableWidget::item {
    padding: 4px;
    min-height: 20px;
    border-bottom: 1px solid #f0f0f0;
}

QTableWidget QHeaderView::section {
    background-color: #f8f9fa;
    padding: 4px;
    border: none;
    border-bottom: 2px solid #dee2e6;
    font-weight: bold;
    font-size: 9px;
    min-height: 25px;
}

/* تحسين التسميات */
QLabel {
    font-size: 10px;
    color: #333;
}

/* تحسين الإطارات */
QFrame {
    border-radius: 6px;
    margin: 2px;
    padding: 3px;
}

/* تحسين مربعات التحديد */
QCheckBox, QRadioButton {
    font-size: 10px;
    spacing: 3px;
}

/* تحسين القوائم المنسدلة */
QComboBox::drop-down {
    width: 20px;
    border: none;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
}

/* تحسين أشرطة التمرير */
QScrollBar:vertical {
    width: 12px;
    background: #f1f1f1;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #c1c1c1;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #a8a8a8;
}

QScrollBar:horizontal {
    height: 12px;
    background: #f1f1f1;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background: #c1c1c1;
    border-radius: 6px;
    min-width: 20px;
}

/* تحسين علامات التبويب */
QTabWidget::pane {
    border: 1px solid #ddd;
    border-radius: 4px;
}

QTabBar::tab {
    padding: 4px 8px;
    margin: 1px;
    font-size: 9px;
    min-width: 60px;
    border-radius: 3px;
}

QTabBar::tab:selected {
    background-color: #007bff;
    color: white;
}

/* تحسين مربعات الحوار */
QDialog {
    border-radius: 8px;
}

/* تحسين القوائم */
QListWidget {
    font-size: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

QListWidget::item {
    padding: 3px;
    border-bottom: 1px solid #f0f0f0;
}

/* تحسين أشرطة التقدم */
QProgressBar {
    height: 15px;
    border-radius: 7px;
    background-color: #f0f0f0;
    text-align: center;
    font-size: 9px;
}

QProgressBar::chunk {
    border-radius: 7px;
    background-color: #007bff;
}

/* تحسين القوائم الرئيسية */
QMenuBar {
    font-size: 10px;
    padding: 2px;
}

QMenuBar::item {
    padding: 3px 6px;
    border-radius: 3px;
}

QMenu {
    font-size: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

QMenu::item {
    padding: 4px 8px;
}

/* تحسين أشرطة الأدوات */
QToolBar {
    spacing: 2px;
    padding: 2px;
}

QToolButton {
    padding: 3px;
    border-radius: 3px;
    font-size: 9px;
}

/* تحسين مربعات النص الكبيرة */
QTextEdit, QPlainTextEdit {
    font-size: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px;
}

/* تحسين مؤشرات التحميل */
QProgressBar {
    border: none;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    text-align: center;
    font-size: 9px;
}

/* تحسين الرسائل والتنبيهات */
QMessageBox {
    font-size: 10px;
}

QMessageBox QPushButton {
    min-width: 60px;
    padding: 4px 8px;
}

/* تحسين مربعات التجميع */
QGroupBox {
    font-size: 10px;
    font-weight: bold;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 4px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 4px 0 4px;
}

/* تحسين مربعات الاختيار المتعددة */
QListView {
    font-size: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

QListView::item {
    padding: 2px;
    border-bottom: 1px solid #f5f5f5;
}

/* تحسين التقويم */
QCalendarWidget {
    font-size: 9px;
}

QCalendarWidget QTableView {
    selection-background-color: #007bff;
}

/* تحسين مربعات التاريخ والوقت */
QDateEdit, QTimeEdit, QDateTimeEdit {
    min-height: 25px;
    padding: 3px;
    font-size: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* تحسين الفواصل */
QSplitter::handle {
    background-color: #ddd;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* تحسين مربعات الحالة */
QStatusBar {
    font-size: 9px;
    border-top: 1px solid #ddd;
}

/* تحسين الأيقونات */
QToolButton[popupMode="1"] {
    padding-right: 15px;
}

QToolButton::menu-button {
    border: none;
    width: 12px;
}

/* تحسين مربعات الألوان */
QColorDialog {
    font-size: 10px;
}

/* تحسين مربعات الخطوط */
QFontDialog {
    font-size: 10px;
}

/* تحسين مربعات الملفات */
QFileDialog {
    font-size: 10px;
}

QFileDialog QListView {
    font-size: 9px;
}

/* تحسين الشاشات الصغيرة جداً */
@media screen and (max-width: 1200px) {
    QWidget {
        font-size: 9px;
    }
    
    QPushButton {
        min-height: 25px;
        max-height: 30px;
        font-size: 9px;
        padding: 2px 6px;
    }
    
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
        min-height: 22px;
        max-height: 25px;
        font-size: 9px;
    }
    
    QTableWidget {
        font-size: 8px;
    }
    
    QTableWidget::item {
        padding: 2px;
        min-height: 18px;
    }
}
