#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التقارير المخصصة - إنشاء تقارير حسب الطلب
Custom Reports - Create reports on demand
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QComboBox, QLabel, QDateEdit, QTableWidget,
                             QTableWidgetItem, QFileDialog, QMessageBox, QFrame,
                             QHeaderView, QGridLayout, QDialog, QGroupBox, QFormLayout,
                             QCheckBox, QSpinBox, QTextEdit, QTabWidget, QListWidget,
                             QListWidgetItem, QSplitter)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QColor, QPainter, QFont
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QBarSeries, QBarSet, 
                          QValueAxis, QBarCategoryAxis, QPieSeries)
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, text
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from utils.currency_formatter import format_currency, format_number
from utils.performance_optimizer import DatabaseOptimizer, QueryOptimizer, cache_manager
import xlsxwriter
from datetime import datetime, timedelta
import json
import os


class CustomReportBuilder(QDialog):
    """منشئ التقارير المخصصة"""
    
    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.db_optimizer = DatabaseOptimizer(engine)
        self.query_optimizer = QueryOptimizer(engine)
        self.report_config = {}
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔧 منشئ التقارير المخصصة")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # تبويبات الإعداد
        tabs = QTabWidget()
        
        # تبويب البيانات
        data_tab = self.create_data_tab()
        tabs.addTab(data_tab, "📊 البيانات")
        
        # تبويب المرشحات
        filters_tab = self.create_filters_tab()
        tabs.addTab(filters_tab, "🔍 المرشحات")
        
        # تبويب التنسيق
        format_tab = self.create_format_tab()
        tabs.addTab(format_tab, "🎨 التنسيق")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        preview_btn = QPushButton("👁️ معاينة")
        preview_btn.clicked.connect(self.preview_report)
        
        save_btn = QPushButton("💾 حفظ القالب")
        save_btn.clicked.connect(self.save_template)
        
        generate_btn = QPushButton("📄 إنشاء التقرير")
        generate_btn.clicked.connect(self.generate_report)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(preview_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(generate_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def create_data_tab(self):
        """إنشاء تبويب البيانات"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QVBoxLayout()
        
        self.report_type = QComboBox()
        self.report_type.addItems([
            "تقرير المبيعات",
            "تقرير المشتريات", 
            "تقرير المخزون",
            "تقرير العملاء",
            "تقرير الموردين",
            "تقرير الأرباح",
            "تقرير مخصص"
        ])
        report_type_layout.addWidget(self.report_type)
        report_type_group.setLayout(report_type_layout)
        layout.addWidget(report_type_group)
        
        # الحقول المطلوبة
        fields_group = QGroupBox("الحقول المطلوبة")
        fields_layout = QVBoxLayout()
        
        self.fields_list = QListWidget()
        self.fields_list.setSelectionMode(QListWidget.MultiSelection)
        
        # إضافة الحقول المتاحة
        fields = [
            "التاريخ", "رقم الفاتورة", "النوع", "العميل/المورد",
            "المنتج", "الكمية", "السعر", "الإجمالي", "الخصم",
            "الضريبة", "صافي المبلغ", "الربح", "الفئة"
        ]
        
        for field in fields:
            item = QListWidgetItem(field)
            item.setCheckState(Qt.Unchecked)
            self.fields_list.addItem(item)
        
        fields_layout.addWidget(self.fields_list)
        fields_group.setLayout(fields_layout)
        layout.addWidget(fields_group)
        
        return widget
    
    def create_filters_tab(self):
        """إنشاء تبويب المرشحات"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # الفترة الزمنية
        date_group = QGroupBox("الفترة الزمنية")
        date_layout = QFormLayout()
        
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.end_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.end_date.setCalendarPopup(True)
        
        date_layout.addRow("من:", self.start_date)
        date_layout.addRow("إلى:", self.end_date)
        date_group.setLayout(date_layout)
        layout.addWidget(date_group)
        
        # مرشحات إضافية
        filters_group = QGroupBox("مرشحات إضافية")
        filters_layout = QFormLayout()
        
        # العميل/المورد
        self.customer_filter = QComboBox()
        self.customer_filter.addItem("جميع العملاء")
        filters_layout.addRow("العميل:", self.customer_filter)
        
        # المنتج
        self.product_filter = QComboBox()
        self.product_filter.addItem("جميع المنتجات")
        filters_layout.addRow("المنتج:", self.product_filter)
        
        # الفئة
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع الفئات")
        filters_layout.addRow("الفئة:", self.category_filter)
        
        # الحد الأدنى للمبلغ
        self.min_amount = QSpinBox()
        self.min_amount.setMaximum(999999)
        filters_layout.addRow("الحد الأدنى للمبلغ:", self.min_amount)
        
        # الحد الأقصى للمبلغ
        self.max_amount = QSpinBox()
        self.max_amount.setMaximum(999999)
        self.max_amount.setValue(999999)
        filters_layout.addRow("الحد الأقصى للمبلغ:", self.max_amount)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # تحميل البيانات للمرشحات
        self.load_filter_data()
        
        return widget
    
    def create_format_tab(self):
        """إنشاء تبويب التنسيق"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # تنسيق الإخراج
        output_group = QGroupBox("تنسيق الإخراج")
        output_layout = QFormLayout()
        
        self.output_format = QComboBox()
        self.output_format.addItems(["Excel", "PDF", "CSV"])
        output_layout.addRow("التنسيق:", self.output_format)
        
        # عنوان التقرير
        self.report_title = QTextEdit()
        self.report_title.setMaximumHeight(60)
        self.report_title.setPlainText("تقرير مخصص")
        output_layout.addRow("عنوان التقرير:", self.report_title)
        
        # خيارات إضافية
        self.include_summary = QCheckBox("تضمين ملخص")
        self.include_summary.setChecked(True)
        output_layout.addRow("", self.include_summary)
        
        self.include_charts = QCheckBox("تضمين رسوم بيانية")
        self.include_charts.setChecked(True)
        output_layout.addRow("", self.include_charts)
        
        output_group.setLayout(output_layout)
        layout.addWidget(output_group)
        
        # ترتيب البيانات
        sort_group = QGroupBox("ترتيب البيانات")
        sort_layout = QFormLayout()
        
        self.sort_field = QComboBox()
        self.sort_field.addItems([
            "التاريخ", "المبلغ", "الكمية", "العميل", "المنتج"
        ])
        sort_layout.addRow("ترتيب حسب:", self.sort_field)
        
        self.sort_order = QComboBox()
        self.sort_order.addItems(["تنازلي", "تصاعدي"])
        sort_layout.addRow("الترتيب:", self.sort_order)
        
        # عدد السجلات
        self.record_limit = QSpinBox()
        self.record_limit.setMaximum(10000)
        self.record_limit.setValue(1000)
        sort_layout.addRow("عدد السجلات:", self.record_limit)
        
        sort_group.setLayout(sort_layout)
        layout.addWidget(sort_group)
        
        return widget
    
    def load_filter_data(self):
        """تحميل بيانات المرشحات"""
        try:
            with Session(self.engine) as session:
                # تحميل العملاء
                customers = session.query(Customer.name).filter(
                    Customer.is_active == True
                ).distinct().all()
                
                for customer in customers:
                    self.customer_filter.addItem(customer.name)
                
                # تحميل المنتجات
                products = session.query(Product.name).filter(
                    Product.is_active == True
                ).distinct().limit(100).all()
                
                for product in products:
                    self.product_filter.addItem(product.name)
                
                # تحميل الفئات
                categories = session.query(Product.category).filter(
                    Product.category.isnot(None),
                    Product.is_active == True
                ).distinct().all()
                
                for category in categories:
                    if category.category:
                        self.category_filter.addItem(category.category)
                        
        except Exception as e:
            print(f"خطأ في تحميل بيانات المرشحات: {e}")
    
    def preview_report(self):
        """معاينة التقرير"""
        try:
            # جمع إعدادات التقرير
            config = self.get_report_config()
            
            # إنشاء نافذة المعاينة
            preview_dialog = ReportPreviewDialog(self.engine, config, self)
            preview_dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في المعاينة:\n{str(e)}")
    
    def save_template(self):
        """حفظ قالب التقرير"""
        try:
            config = self.get_report_config()
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ قالب التقرير",
                f"قالب_تقرير_{datetime.now().strftime('%Y%m%d')}.json",
                "JSON Files (*.json)"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "نجح", "تم حفظ القالب بنجاح!")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الحفظ:\n{str(e)}")
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            config = self.get_report_config()
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير",
                f"تقرير_مخصص_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx);;PDF Files (*.pdf);;CSV Files (*.csv)"
            )
            
            if file_path:
                # إنشاء التقرير
                generator = CustomReportGenerator(self.engine, config)
                success = generator.generate_report(file_path)
                
                if success:
                    QMessageBox.information(self, "نجح", f"تم إنشاء التقرير بنجاح:\n{file_path}")
                    self.accept()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في إنشاء التقرير")
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
    
    def get_report_config(self):
        """الحصول على إعدادات التقرير"""
        # جمع الحقول المحددة
        selected_fields = []
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            if item.checkState() == Qt.Checked:
                selected_fields.append(item.text())
        
        config = {
            'report_type': self.report_type.currentText(),
            'fields': selected_fields,
            'start_date': self.start_date.date().toString('yyyy-MM-dd'),
            'end_date': self.end_date.date().toString('yyyy-MM-dd'),
            'customer_filter': self.customer_filter.currentText(),
            'product_filter': self.product_filter.currentText(),
            'category_filter': self.category_filter.currentText(),
            'min_amount': self.min_amount.value(),
            'max_amount': self.max_amount.value(),
            'output_format': self.output_format.currentText(),
            'title': self.report_title.toPlainText(),
            'include_summary': self.include_summary.isChecked(),
            'include_charts': self.include_charts.isChecked(),
            'sort_field': self.sort_field.currentText(),
            'sort_order': self.sort_order.currentText(),
            'record_limit': self.record_limit.value()
        }
        
        return config


class ReportPreviewDialog(QDialog):
    """نافذة معاينة التقرير"""
    
    def __init__(self, engine, config, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.config = config
        self.setup_ui()
        self.load_preview_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("👁️ معاينة التقرير")
        self.setFixedSize(900, 600)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان التقرير
        title_label = QLabel(self.config.get('title', 'تقرير مخصص'))
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # جدول البيانات
        self.preview_table = QTableWidget()
        layout.addWidget(self.preview_table)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
    
    def load_preview_data(self):
        """تحميل بيانات المعاينة"""
        try:
            generator = CustomReportGenerator(self.engine, self.config)
            data = generator.get_report_data(limit=50)  # معاينة 50 سجل فقط
            
            if data:
                # إعداد الجدول
                self.preview_table.setRowCount(len(data))
                self.preview_table.setColumnCount(len(self.config['fields']))
                self.preview_table.setHorizontalHeaderLabels(self.config['fields'])
                
                # ملء البيانات
                for row, record in enumerate(data):
                    for col, field in enumerate(self.config['fields']):
                        value = getattr(record, field, '') if hasattr(record, field) else ''
                        self.preview_table.setItem(row, col, QTableWidgetItem(str(value)))
                
                # تنسيق الجدول
                self.preview_table.horizontalHeader().setStretchLastSection(True)
                self.preview_table.setAlternatingRowColors(True)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المعاينة:\n{str(e)}")


class CustomReportGenerator:
    """مولد التقارير المخصصة"""
    
    def __init__(self, engine, config):
        self.engine = engine
        self.config = config
        self.db_optimizer = DatabaseOptimizer(engine)
    
    def generate_report(self, file_path):
        """إنشاء التقرير"""
        try:
            # الحصول على البيانات
            data = self.get_report_data()
            
            if not data:
                return False
            
            # تحديد نوع الملف وإنشاء التقرير
            if file_path.endswith('.xlsx'):
                return self.generate_excel_report(data, file_path)
            elif file_path.endswith('.pdf'):
                return self.generate_pdf_report(data, file_path)
            elif file_path.endswith('.csv'):
                return self.generate_csv_report(data, file_path)
            
            return False
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
            return False
    
    def get_report_data(self, limit=None):
        """الحصول على بيانات التقرير"""
        # هذه دالة مبسطة - يمكن توسيعها حسب نوع التقرير
        with Session(self.engine) as session:
            query = session.query(Transaction)
            
            # تطبيق المرشحات
            start_date = datetime.strptime(self.config['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(self.config['end_date'], '%Y-%m-%d').date()
            
            query = query.filter(Transaction.date.between(start_date, end_date))
            
            # تطبيق الحد
            if limit:
                query = query.limit(limit)
            else:
                query = query.limit(self.config.get('record_limit', 1000))
            
            return query.all()
    
    def generate_excel_report(self, data, file_path):
        """إنشاء تقرير Excel"""
        try:
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet('التقرير')
            
            # تنسيقات
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#0d6efd',
                'font_color': 'white',
                'align': 'center'
            })
            
            # كتابة العناوين
            for col, field in enumerate(self.config['fields']):
                worksheet.write(0, col, field, header_format)
            
            # كتابة البيانات
            for row, record in enumerate(data, 1):
                for col, field in enumerate(self.config['fields']):
                    value = getattr(record, field.lower().replace(' ', '_'), '')
                    worksheet.write(row, col, str(value))
            
            workbook.close()
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير Excel: {e}")
            return False
    
    def generate_pdf_report(self, data, file_path):
        """إنشاء تقرير PDF"""
        # يمكن تطوير هذه الدالة لاحقاً
        return False
    
    def generate_csv_report(self, data, file_path):
        """إنشاء تقرير CSV"""
        # يمكن تطوير هذه الدالة لاحقاً
        return False
