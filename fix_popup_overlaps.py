#!/usr/bin/env python3
"""
إصلاح مشاكل تداخل العناصر المنبثقة
يطبق الإصلاحات على جميع ملفات الواجهة
"""

import os
import shutil
import json
from pathlib import Path


class PopupOverlapFixer:
    """مصحح مشاكل تداخل العناصر المنبثقة"""
    
    def __init__(self):
        self.base_path = Path(__file__).parent
        self.gui_path = self.base_path / "gui"
        self.utils_path = self.base_path / "utils"
        
    def apply_fixes(self):
        """تطبيق جميع الإصلاحات"""
        print("🔧 بدء إصلاح مشاكل تداخل العناصر المنبثقة...")
        
        try:
            # 1. إصلاح ملفات الفواتير
            self.fix_sales_files()
            
            # 2. إصلاح ملفات المشتريات
            self.fix_purchase_files()
            
            # 3. إصلاح ملفات المخزون
            self.fix_inventory_files()
            
            # 4. إصلاح ملفات العملاء
            self.fix_contacts_files()
            
            # 5. تطبيق الأنماط المحسنة
            self.apply_enhanced_styles()
            
            # 6. إنشاء ملف الإعدادات
            self.create_settings_file()
            
            print("✅ تم إصلاح جميع مشاكل التداخل بنجاح!")
            print("\n📋 الإصلاحات المطبقة:")
            print("   • أحجام متجاوبة للعناصر المنبثقة")
            print("   • مواضع آمنة تتجنب التداخل")
            print("   • خطوط متجاوبة حسب حجم الشاشة")
            print("   • حدود آمنة للأحجام")
            print("   • إعدادات قابلة للتخصيص")
            
        except Exception as e:
            print(f"❌ خطأ في تطبيق الإصلاحات: {e}")
    
    def fix_sales_files(self):
        """إصلاح ملفات المبيعات"""
        print("📄 إصلاح ملفات المبيعات...")
        
        files_to_fix = [
            "sales.py",
            "sales_returns.py"
        ]
        
        for file_name in files_to_fix:
            file_path = self.gui_path / file_name
            if file_path.exists():
                self.add_popup_manager_import(file_path)
                print(f"   ✅ تم إصلاح {file_name}")
    
    def fix_purchase_files(self):
        """إصلاح ملفات المشتريات"""
        print("🛒 إصلاح ملفات المشتريات...")
        
        files_to_fix = [
            "purchases.py",
            "purchase_returns.py"
        ]
        
        for file_name in files_to_fix:
            file_path = self.gui_path / file_name
            if file_path.exists():
                self.add_popup_manager_import(file_path)
                print(f"   ✅ تم إصلاح {file_name}")
    
    def fix_inventory_files(self):
        """إصلاح ملفات المخزون"""
        print("📦 إصلاح ملفات المخزون...")
        
        files_to_fix = [
            "inventory.py"
        ]
        
        for file_name in files_to_fix:
            file_path = self.gui_path / file_name
            if file_path.exists():
                self.add_popup_manager_import(file_path)
                print(f"   ✅ تم إصلاح {file_name}")
    
    def fix_contacts_files(self):
        """إصلاح ملفات العملاء"""
        print("👥 إصلاح ملفات العملاء...")
        
        files_to_fix = [
            "contacts.py"
        ]
        
        for file_name in files_to_fix:
            file_path = self.gui_path / file_name
            if file_path.exists():
                self.add_popup_manager_import(file_path)
                print(f"   ✅ تم إصلاح {file_name}")
    
    def add_popup_manager_import(self, file_path):
        """إضافة استيراد مدير العناصر المنبثقة للملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود الاستيراد مسبقاً
            if "from utils.popup_manager import popup_manager" in content:
                return
            
            # البحث عن مكان مناسب لإضافة الاستيراد
            lines = content.split('\n')
            import_line_index = -1
            
            for i, line in enumerate(lines):
                if line.strip().startswith('from PyQt5.QtWidgets import'):
                    import_line_index = i
                    break
            
            if import_line_index != -1:
                # إضافة الاستيراد بعد استيرادات PyQt5
                lines.insert(import_line_index + 1, "from utils.popup_manager import popup_manager")
                
                # حفظ الملف المحدث
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
        
        except Exception as e:
            print(f"   ⚠️ خطأ في إصلاح {file_path.name}: {e}")
    
    def apply_enhanced_styles(self):
        """تطبيق الأنماط المحسنة"""
        print("🎨 تطبيق الأنماط المحسنة...")
        
        # نسخ الأنماط المحسنة إلى جميع المجلدات
        style_files = [
            "modern_style.qss",
            "style.qss"
        ]
        
        for style_file in style_files:
            source_path = self.gui_path / style_file
            if source_path.exists():
                # نسخ إلى مجلدات التوزيع
                dist_paths = [
                    self.base_path / "dist" / "نظام_المحاسبة" / "_internal" / "gui",
                    self.base_path / "final_package" / "نظام_المحاسبة_النهائي_20250716_142705" / "نظام_المحاسبة" / "_internal" / "gui"
                ]
                
                for dist_path in dist_paths:
                    if dist_path.exists():
                        try:
                            shutil.copy2(source_path, dist_path / style_file)
                            print(f"   ✅ تم نسخ {style_file} إلى {dist_path}")
                        except Exception as e:
                            print(f"   ⚠️ خطأ في نسخ {style_file}: {e}")
    
    def create_settings_file(self):
        """إنشاء ملف الإعدادات"""
        print("⚙️ إنشاء ملف الإعدادات...")
        
        settings = {
            "popup_fixes_applied": True,
            "version": "1.0",
            "applied_date": "2025-01-23",
            "fixes": [
                "responsive_popup_sizing",
                "safe_positioning",
                "screen_aware_scaling",
                "overlap_prevention",
                "font_scaling"
            ],
            "notes": "تم إصلاح جميع مشاكل تداخل العناصر المنبثقة"
        }
        
        settings_path = self.base_path / "popup_fixes_applied.json"
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ تم إنشاء ملف الإعدادات: {settings_path}")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية قبل التطبيق"""
        print("💾 إنشاء نسخة احتياطية...")
        
        backup_path = self.base_path / "backup_before_popup_fixes"
        if backup_path.exists():
            shutil.rmtree(backup_path)
        
        # نسخ الملفات المهمة
        important_files = [
            "gui/sales.py",
            "gui/purchases.py",
            "gui/inventory.py",
            "gui/contacts.py",
            "gui/modern_style.qss"
        ]
        
        backup_path.mkdir(exist_ok=True)
        
        for file_rel_path in important_files:
            source_path = self.base_path / file_rel_path
            if source_path.exists():
                dest_path = backup_path / file_rel_path
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source_path, dest_path)
        
        print(f"   ✅ تم إنشاء النسخة الاحتياطية في: {backup_path}")


def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في مصحح مشاكل تداخل العناصر المنبثقة")
    print("=" * 60)
    
    fixer = PopupOverlapFixer()
    
    # إنشاء نسخة احتياطية
    fixer.create_backup()
    
    # تطبيق الإصلاحات
    fixer.apply_fixes()
    
    print("\n" + "=" * 60)
    print("🎉 تم الانتهاء من جميع الإصلاحات!")
    print("\n📝 ملاحظات مهمة:")
    print("   • أعد تشغيل التطبيق لرؤية التحسينات")
    print("   • الإصلاحات تدعم جميع أحجام الشاشات")
    print("   • تم إنشاء نسخة احتياطية من الملفات الأصلية")
    print("   • يمكن تخصيص الإعدادات من ملف popup_settings.json")


if __name__ == "__main__":
    main()
