#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع المتكاملة لنظام المحاسبة
Complete Package Creator for Accounting System
"""

import os
import shutil
import json
from datetime import datetime

class CompletePackageCreator:
    def __init__(self):
        self.package_name = "نظام_المحاسبة_المتكامل"
        self.version = "1.0"
        self.build_date = datetime.now().strftime("%Y%m%d")
        
        # مسارات المصدر
        self.source_paths = {
            'main_exe': 'dist/نظام المحاسبة/نظام المحاسبة.exe',
            'activation_exe': 'dist/برنامج_تفعيل_التراخيص.exe',
            'database': 'accounting.db',
            'assets': 'assets/',
            'fonts': 'fonts/',
            'docs': 'docs/'
        }
        
        # مسار الحزمة النهائية
        self.package_path = f"dist/{self.package_name}_v{self.version}_{self.build_date}"
        
    def create_package_structure(self):
        """إنشاء هيكل الحزمة"""
        print("📁 إنشاء هيكل الحزمة...")
        
        # إنشاء المجلدات الرئيسية
        folders = [
            self.package_path,
            f"{self.package_path}/النظام",
            f"{self.package_path}/الأصول",
            f"{self.package_path}/الأصول/الشعارات",
            f"{self.package_path}/الأصول/الخطوط",
            f"{self.package_path}/الأصول/الأيقونات",
            f"{self.package_path}/التوثيق",
            f"{self.package_path}/الإعدادات",
            f"{self.package_path}/النسخ_الاحتياطية"
        ]
        
        for folder in folders:
            os.makedirs(folder, exist_ok=True)
            print(f"✅ تم إنشاء: {folder}")
    
    def copy_main_system(self):
        """نسخ النظام الرئيسي"""
        print("🔧 نسخ النظام الرئيسي...")
        
        # نسخ مجلد النظام كاملاً
        source_system = "dist/نظام المحاسبة"
        target_system = f"{self.package_path}/النظام"
        
        if os.path.exists(source_system):
            # نسخ جميع ملفات النظام
            for item in os.listdir(source_system):
                source_item = os.path.join(source_system, item)
                target_item = os.path.join(target_system, item)
                
                if os.path.isdir(source_item):
                    shutil.copytree(source_item, target_item, dirs_exist_ok=True)
                else:
                    shutil.copy2(source_item, target_item)
            
            print("✅ تم نسخ النظام الرئيسي")
        else:
            print("❌ النظام الرئيسي غير موجود")
    
    def copy_activation_program(self):
        """نسخ برنامج التفعيل"""
        print("🔑 نسخ برنامج التفعيل...")
        
        source_activation = self.source_paths['activation_exe']
        target_activation = f"{self.package_path}/برنامج_تفعيل_التراخيص.exe"
        
        if os.path.exists(source_activation):
            shutil.copy2(source_activation, target_activation)
            print("✅ تم نسخ برنامج التفعيل")
        else:
            print("❌ برنامج التفعيل غير موجود")
    
    def create_clean_database(self):
        """إنشاء قاعدة بيانات نظيفة"""
        print("🗄️ إنشاء قاعدة بيانات نظيفة...")
        
        # نسخ قاعدة البيانات الحالية كنموذج
        if os.path.exists('accounting.db'):
            target_db = f"{self.package_path}/النظام/accounting_template.db"
            shutil.copy2('accounting.db', target_db)
            print("✅ تم إنشاء قالب قاعدة البيانات")
    
    def copy_assets(self):
        """نسخ الأصول"""
        print("🎨 نسخ الأصول...")
        
        # نسخ الشعارات
        if os.path.exists('assets'):
            target_assets = f"{self.package_path}/الأصول/الشعارات"
            for file in os.listdir('assets'):
                if file.endswith(('.png', '.jpg', '.jpeg', '.ico')):
                    shutil.copy2(f"assets/{file}", f"{target_assets}/{file}")
            print("✅ تم نسخ الشعارات")
        
        # نسخ الخطوط
        if os.path.exists('fonts'):
            target_fonts = f"{self.package_path}/الأصول/الخطوط"
            shutil.copytree('fonts', target_fonts, dirs_exist_ok=True)
            print("✅ تم نسخ الخطوط")
    
    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        print("⚙️ إنشاء الإعدادات الافتراضية...")
        
        # إعدادات الشركة الافتراضية
        company_settings = {
            "company_name": "اسم الشركة",
            "company_address": "عنوان الشركة",
            "company_phone": "رقم الهاتف",
            "company_email": "البريد الإلكتروني",
            "tax_number": "الرقم الضريبي",
            "currency": "جنيه مصري",
            "currency_symbol": "ج.م"
        }
        
        # إعدادات النظام الافتراضية
        system_settings = {
            "theme": "modern",
            "language": "ar",
            "auto_backup": True,
            "backup_interval": 7,
            "print_format": "A4",
            "date_format": "dd/mm/yyyy"
        }
        
        # حفظ الإعدادات
        settings_path = f"{self.package_path}/الإعدادات"
        
        with open(f"{settings_path}/company_settings_default.json", 'w', encoding='utf-8') as f:
            json.dump(company_settings, f, ensure_ascii=False, indent=2)
        
        with open(f"{settings_path}/system_settings_default.json", 'w', encoding='utf-8') as f:
            json.dump(system_settings, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء الإعدادات الافتراضية")
    
    def create_documentation(self):
        """إنشاء التوثيق"""
        print("📚 إنشاء التوثيق...")
        
        # دليل التثبيت
        installation_guide = """
🔧 دليل تثبيت نظام المحاسبة المتكامل
=====================================

📋 متطلبات النظام:
- Windows 7 أو أحدث
- مساحة قرص: 500 ميجابايت
- ذاكرة: 2 جيجابايت RAM

🚀 خطوات التثبيت:

1️⃣ تفعيل البرنامج:
   - شغل "برنامج_تفعيل_التراخيص.exe"
   - انسخ بيانات الجهاز وأرسلها للمطور
   - أدخل كود التفعيل المُستلم
   - انسخ ملف "license.dat" لمجلد النظام

2️⃣ تشغيل النظام:
   - ادخل لمجلد "النظام"
   - شغل "نظام_المحاسبة.exe"
   - أدخل بيانات الشركة في الإعداد الأولي

3️⃣ الإعداد الأولي:
   - أدخل بيانات الشركة
   - اختر الإعدادات المناسبة
   - ابدأ استخدام النظام

📞 للدعم الفني:
البريد الإلكتروني: <EMAIL>
        """
        
        # دليل المستخدم المختصر
        user_guide = """
📖 دليل المستخدم السريع
====================

🏠 الصفحة الرئيسية:
- عرض الإحصائيات اليومية
- الوصول السريع للوظائف

💰 المبيعات:
- إنشاء فواتير البيع
- إدارة العملاء
- تتبع المدفوعات

📦 المخزون:
- إدارة المنتجات
- تتبع الكميات
- تقارير المخزون

📊 التقارير:
- تقارير المبيعات
- تقارير المخزون
- التقارير المالية

⚙️ الإعدادات:
- بيانات الشركة
- إعدادات النظام
- النسخ الاحتياطية
        """
        
        # حفظ التوثيق
        docs_path = f"{self.package_path}/التوثيق"
        
        with open(f"{docs_path}/دليل_التثبيت.txt", 'w', encoding='utf-8') as f:
            f.write(installation_guide)
        
        with open(f"{docs_path}/دليل_المستخدم_السريع.txt", 'w', encoding='utf-8') as f:
            f.write(user_guide)
        
        print("✅ تم إنشاء التوثيق")
    
    def create_batch_files(self):
        """إنشاء ملفات التشغيل"""
        print("⚡ إنشاء ملفات التشغيل...")
        
        # ملف تشغيل النظام
        run_system_bat = """@echo off
echo 🚀 تشغيل نظام المحاسبة...
echo.

cd /d "%~dp0النظام"

if not exist "نظام_المحاسبة.exe" (
    echo ❌ ملف النظام غير موجود
    pause
    exit /b 1
)

if not exist "license.dat" (
    echo ⚠️ لم يتم تفعيل البرنامج بعد
    echo يرجى تشغيل برنامج التفعيل أولاً
    pause
    exit /b 1
)

echo ✅ تشغيل النظام...
start "" "نظام_المحاسبة.exe"
"""
        
        # ملف التفعيل
        activation_bat = """@echo off
echo 🔑 تفعيل نظام المحاسبة...
echo.

if not exist "برنامج_تفعيل_التراخيص.exe" (
    echo ❌ برنامج التفعيل غير موجود
    pause
    exit /b 1
)

echo ✅ تشغيل برنامج التفعيل...
start "" "برنامج_تفعيل_التراخيص.exe"
"""
        
        # حفظ ملفات التشغيل
        with open(f"{self.package_path}/تشغيل_النظام.bat", 'w', encoding='utf-8') as f:
            f.write(run_system_bat)
        
        with open(f"{self.package_path}/تفعيل_البرنامج.bat", 'w', encoding='utf-8') as f:
            f.write(activation_bat)
        
        print("✅ تم إنشاء ملفات التشغيل")
    
    def create_package_info(self):
        """إنشاء معلومات الحزمة"""
        print("📋 إنشاء معلومات الحزمة...")
        
        package_info = {
            "name": "نظام المحاسبة المتكامل",
            "version": self.version,
            "build_date": self.build_date,
            "developer": "SICOO Company",
            "email": "<EMAIL>",
            "description": "نظام محاسبة متكامل للشركات الصغيرة والمتوسطة",
            "features": [
                "إدارة المبيعات والمشتريات",
                "إدارة المخزون",
                "التقارير المالية",
                "نظام تراخيص آمن",
                "واجهة عربية سهلة"
            ],
            "requirements": {
                "os": "Windows 7+",
                "ram": "2GB",
                "disk": "500MB",
                "screen": "1024x768"
            }
        }
        
        with open(f"{self.package_path}/معلومات_الحزمة.json", 'w', encoding='utf-8') as f:
            json.dump(package_info, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء معلومات الحزمة")
    
    def create_complete_package(self):
        """إنشاء الحزمة المتكاملة"""
        print("🎯 بدء إنشاء الحزمة المتكاملة...")
        print("=" * 50)
        
        try:
            self.create_package_structure()
            self.copy_main_system()
            self.copy_activation_program()
            self.create_clean_database()
            self.copy_assets()
            self.create_default_settings()
            self.create_documentation()
            self.create_batch_files()
            self.create_package_info()
            
            print("=" * 50)
            print("🎉 تم إنشاء الحزمة المتكاملة بنجاح!")
            print(f"📁 المسار: {self.package_path}")
            print("=" * 50)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الحزمة: {e}")
            return False

if __name__ == "__main__":
    creator = CompletePackageCreator()
    creator.create_complete_package()
