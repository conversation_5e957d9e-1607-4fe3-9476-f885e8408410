from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
from utils.dpi_manager import dpi_manager
                           <PERSON><PERSON>abe<PERSON>, QFrame, QComboBox, QDateEdit, QSpinBox,
                           QTabWidget, QScrollArea, QGridLayout)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QBarSeries,
                          QBarSet, QValueAxis, QBarCategoryAxis, QPieSeries,
                          QScatterSeries)
from PyQt5.QtGui import QPainter, QPen, QColor, QFont
# تم تعطيل matplotlib و seaborn لتجنب مشاكل الاستقرار
# import matplotlib.pyplot as plt
# from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
# import seaborn as sns
import pandas as pd
import numpy as np
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from datetime import datetime, timedelta

# للتوافق مع الكود الموجود - توجيه إلى النسخة الآمنة
try:
    from gui.safe_advanced_reports import SafeAdvancedReportsWidget
    AdvancedReportsWidget = SafeAdvancedReportsWidget
except ImportError:
    # في حالة عدم وجود النسخة الآمنة، استخدم النسخة المعطلة
    pass

class AdvancedAnalyticsWidget(QWidget):
    """
    تحذير: هذا الكلاس معطل لأنه يستخدم matplotlib الذي يسبب مشاكل
    استخدم SafeAdvancedReportsWidget من gui.safe_advanced_reports بدلاً منه
    """
    def __init__(self, engine):
        super().__init__()
        # إعداد DPI للواجهة
        try:
            dpi_manager.setup_widget_dpi(self)
        except:
            pass
        self.engine = engine
        # عرض رسالة تحذيرية بدلاً من إعداد الواجهة
        self.show_deprecation_warning()

    def show_deprecation_warning(self):
        """عرض رسالة تحذيرية"""
        from PyQt5.QtWidgets import QVBoxLayout, QLabel
        layout = QVBoxLayout()
        self.setLayout(layout)

        warning_label = QLabel("""
        <div style='text-align: center; padding: 50px; color: #dc3545;'>
            <h2>⚠️ هذه النسخة معطلة</h2>
            <p style='font-size: 16px;'>
                تم تعطيل هذه النسخة من التقارير التحليلية لأنها تسبب مشاكل في الاستقرار
            </p>
            <p style='font-size: 14px; color: #666;'>
                يرجى استخدام "التقارير التحليلية" من القائمة الرئيسية للحصول على النسخة المحسنة والآمنة
            </p>
        </div>
        """)
        layout.addWidget(warning_label)

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # شريط التحكم
        control_frame = self.create_control_frame()
        layout.addWidget(control_frame)
        
        # تبويبات التحليل
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                padding: 10px;
            }
            QTabBar::tab {
                padding: 10px 20px;
                margin-right: 5px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #0D6EFD;
                color: white;
            }
        """)
        
        # إضافة صفحات التحليل
        tabs.addTab(self.create_sales_analysis_tab(), "تحليل المبيعات")
        tabs.addTab(self.create_customer_analysis_tab(), "تحليل العملاء")
        tabs.addTab(self.create_product_analysis_tab(), "تحليل المنتجات")
        tabs.addTab(self.create_financial_analysis_tab(), "التحليل المالي")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("تحديث التحليل")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #0D6EFD;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_analysis)
        
        export_btn = QPushButton("تصدير التقرير")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
            }
        """)
        export_btn.clicked.connect(self.export_report)
        
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(export_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)

    def create_control_frame(self):
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        layout = QGridLayout()
        
        # اختيار الفترة
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم", "الأسبوع", "الشهر", "الربع", "السنة", "فترة محددة"
        ])
        self.period_combo.currentIndexChanged.connect(self.on_period_changed)
        
        # التواريخ
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.end_date.setDate(QDate.currentDate())
        
        # مستوى التفاصيل
        self.detail_level = QComboBox()
        self.detail_level.addItems([
            "يومي", "أسبوعي", "شهري", "ربع سنوي", "سنوي"
        ])
        
        layout.addWidget(QLabel("الفترة:"), 0, 0)
        layout.addWidget(self.period_combo, 0, 1)
        layout.addWidget(QLabel("من:"), 0, 2)
        layout.addWidget(self.start_date, 0, 3)
        layout.addWidget(QLabel("إلى:"), 0, 4)
        layout.addWidget(self.end_date, 0, 5)
        layout.addWidget(QLabel("مستوى التفاصيل:"), 0, 6)
        layout.addWidget(self.detail_level, 0, 7)
        
        frame.setLayout(layout)
        return frame

    def create_sales_analysis_tab(self):
        widget = QWidget()
        layout = QGridLayout()
        
        # مخطط المبيعات الزمني
        sales_chart = QChart()
        sales_chart.setTitle("تحليل المبيعات")
        sales_view = QChartView(sales_chart)
        sales_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(sales_view, 0, 0)
        
        # مخطط توزيع المبيعات حسب المنتجات
        products_chart = QChart()
        products_chart.setTitle("توزيع المبيعات حسب المنتجات")
        products_view = QChartView(products_chart)
        products_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(products_view, 0, 1)
        
        # مخطط معدل النمو
        growth_chart = QChart()
        growth_chart.setTitle("معدل نمو المبيعات")
        growth_view = QChartView(growth_chart)
        growth_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(growth_view, 1, 0)
        
        # مخطط المقارنة مع الفترات السابقة
        comparison_chart = QChart()
        comparison_chart.setTitle("مقارنة المبيعات")
        comparison_view = QChartView(comparison_chart)
        comparison_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(comparison_view, 1, 1)
        
        widget.setLayout(layout)
        return widget

    def create_customer_analysis_tab(self):
        widget = QWidget()
        layout = QGridLayout()
        
        # مخطط تصنيف العملاء
        segments_chart = QChart()
        segments_chart.setTitle("تصنيف العملاء")
        segments_view = QChartView(segments_chart)
        segments_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(segments_view, 0, 0)
        
        # مخطط ولاء العملاء
        loyalty_chart = QChart()
        loyalty_chart.setTitle("ولاء العملاء")
        loyalty_view = QChartView(loyalty_chart)
        loyalty_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(loyalty_view, 0, 1)
        
        # تحليل RFM
        rfm_chart = QChart()
        rfm_chart.setTitle("تحليل RFM")
        rfm_view = QChartView(rfm_chart)
        rfm_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(rfm_view, 1, 0)
        
        # مخطط قيمة العميل
        clv_chart = QChart()
        clv_chart.setTitle("قيمة العميل")
        clv_view = QChartView(clv_chart)
        clv_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(clv_view, 1, 1)
        
        widget.setLayout(layout)
        return widget

    def create_product_analysis_tab(self):
        widget = QWidget()
        layout = QGridLayout()
        
        # مصفوفة BCG
        bcg_chart = self.create_bcg_matrix()
        layout.addWidget(bcg_chart, 0, 0)
        
        # تحليل السلة
        basket_chart = QChart()
        basket_chart.setTitle("تحليل سلة المشتريات")
        basket_view = QChartView(basket_chart)
        basket_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(basket_view, 0, 1)
        
        # تحليل الموسمية
        seasonality_chart = QChart()
        seasonality_chart.setTitle("التحليل الموسمي")
        seasonality_view = QChartView(seasonality_chart)
        seasonality_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(seasonality_view, 1, 0)
        
        # تحليل المخزون
        inventory_chart = QChart()
        inventory_chart.setTitle("تحليل المخزون")
        inventory_view = QChartView(inventory_chart)
        inventory_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(inventory_view, 1, 1)
        
        widget.setLayout(layout)
        return widget

    def create_financial_analysis_tab(self):
        widget = QWidget()
        layout = QGridLayout()
        
        # مؤشرات الأداء الرئيسية
        kpi_frame = self.create_kpi_frame()
        layout.addWidget(kpi_frame, 0, 0, 1, 2)
        
        # تحليل الربحية
        profitability_chart = QChart()
        profitability_chart.setTitle("تحليل الربحية")
        profitability_view = QChartView(profitability_chart)
        profitability_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(profitability_view, 1, 0)
        
        # التدفقات النقدية
        cashflow_chart = QChart()
        cashflow_chart.setTitle("التدفقات النقدية")
        cashflow_view = QChartView(cashflow_chart)
        cashflow_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(cashflow_view, 1, 1)
        
        widget.setLayout(layout)
        return widget

    def create_bcg_matrix(self):
        figure = plt.figure(figsize=(6, 6))
        canvas = FigureCanvas(figure)
        ax = figure.add_subplot(111)
        
        # جمع البيانات
        with Session(self.engine) as session:
            products_data = []
            total_revenue = 0
            
            products = session.query(Product).all()
            for product in products:
                sales = session.query(func.sum(TransactionItem.quantity)).filter(
                    TransactionItem.product_id == product.id
                ).scalar() or 0
                
                revenue = sales * product.sale_price
                total_revenue += revenue
                
                growth_rate = 0  # يمكن حساب معدل النمو من البيانات التاريخية
                
                products_data.append({
                    'name': product.name,
                    'market_share': revenue,
                    'growth_rate': growth_rate
                })
            
            # تحويل المبيعات إلى حصة سوقية نسبية
            for product in products_data:
                product['market_share'] = product['market_share'] / total_revenue * 100
        
        # رسم المصفوفة
        x = [p['market_share'] for p in products_data]
        y = [p['growth_rate'] for p in products_data]
        names = [p['name'] for p in products_data]
        
        ax.scatter(x, y)
        
        # إضافة أسماء المنتجات
        for i, name in enumerate(names):
            ax.annotate(name, (x[i], y[i]))
        
        # إضافة الخطوط المقسمة
        ax.axhline(y=0, color='gray', linestyle='--')
        ax.axvline(x=50, color='gray', linestyle='--')
        
        # إضافة العناوين
        ax.set_xlabel('الحصة السوقية النسبية')
        ax.set_ylabel('معدل النمو')
        ax.set_title('مصفوفة BCG')
        
        # إضافة مربعات التصنيف
        ax.text(75, 75, 'النجوم', ha='center', va='center')
        ax.text(25, 75, 'علامات الاستفهام', ha='center', va='center')
        ax.text(75, 25, 'البقرات النقدية', ha='center', va='center')
        ax.text(25, 25, 'الكلاب', ha='center', va='center')
        
        canvas.draw()
        return canvas

    def create_kpi_frame(self):
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
            }
            QLabel.kpi-title {
                font-size: 14px;
                color: #6C757D;
            }
            QLabel.kpi-value {
                font-size: 24px;
                font-weight: bold;
                color: #0D6EFD;
            }
        """)
        
        layout = QGridLayout()
        
        # حساب المؤشرات
        with Session(self.engine) as session:
            # إجمالي المبيعات
            total_sales = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            # إجمالي المشتريات
            total_purchases = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.PURCHASE
            ).scalar() or 0
            
            # إجمالي الأرباح
            total_profit = total_sales - total_purchases
            
            # معدل دوران المخزون
            inventory_turnover = self.calculate_inventory_turnover(session)
            
            # نسبة التكلفة إلى المبيعات
            cost_to_sales = (total_purchases / total_sales * 100) if total_sales > 0 else 0
        
        # إضافة المؤشرات
        self.add_kpi(layout, 0, 0, "إجمالي المبيعات", f"{total_sales:,.0f}")
        self.add_kpi(layout, 0, 1, "إجمالي المشتريات", f"{total_purchases:,.0f}")
        self.add_kpi(layout, 0, 2, "إجمالي الأرباح", f"{total_profit:,.0f}")
        self.add_kpi(layout, 1, 0, "معدل دوران المخزون", f"{inventory_turnover:.2f}")
        self.add_kpi(layout, 1, 1, "نسبة التكلفة إلى المبيعات", f"{cost_to_sales:.1f}%")
        
        frame.setLayout(layout)
        return frame

    def add_kpi(self, layout, row, col, title, value):
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        kpi_layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setProperty("class", "kpi-title")
        
        value_label = QLabel(value)
        value_label.setProperty("class", "kpi-value")
        value_label.setAlignment(Qt.AlignRight)
        
        kpi_layout.addWidget(title_label)
        kpi_layout.addWidget(value_label)
        
        frame.setLayout(kpi_layout)
        layout.addWidget(frame, row, col)

    def calculate_inventory_turnover(self, session):
        # حساب متوسط المخزون
        total_inventory = session.query(func.sum(Product.quantity * Product.purchase_price)).scalar() or 0
        average_inventory = total_inventory / 2
        
        # حساب تكلفة البضاعة المباعة
        cogs = session.query(func.sum(TransactionItem.quantity * TransactionItem.price)).join(
            Transaction
        ).filter(
            Transaction.type == TransactionType.SALE
        ).scalar() or 0
        
        return cogs / average_inventory if average_inventory > 0 else 0

    def on_period_changed(self):
        period = self.period_combo.currentText()
        show_dates = period == "فترة محددة"
        self.start_date.setVisible(show_dates)
        self.end_date.setVisible(show_dates)
        
        if not show_dates:
            today = QDate.currentDate()
            if period == "اليوم":
                self.start_date.setDate(today)
                self.end_date.setDate(today)
            elif period == "الأسبوع":
                self.start_date.setDate(today.addDays(-7))
                self.end_date.setDate(today)
            elif period == "الشهر":
                self.start_date.setDate(today.addMonths(-1))
                self.end_date.setDate(today)
            elif period == "الربع":
                self.start_date.setDate(today.addMonths(-3))
                self.end_date.setDate(today)
            elif period == "السنة":
                self.start_date.setDate(today.addYears(-1))
                self.end_date.setDate(today)

    def refresh_analysis(self):
        # تحديث جميع التحليلات والرسوم البيانية
        pass

    def export_report(self):
        # تصدير التقرير بتنسيق PDF أو Excel
        pass