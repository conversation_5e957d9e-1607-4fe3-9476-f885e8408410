#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام التحكم في البار الجانبي على جميع الواجهات
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, QTimer

def test_all_sidebars():
    """اختبار شامل لجميع البارات الجانبية"""
    print("🧪 بدء اختبار شامل لنظام البار الجانبي على جميع الواجهات...")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        # محاكاة قاعدة البيانات
        from sqlalchemy import create_engine
        engine = create_engine('sqlite:///test_all_sidebars.db', echo=False)
        
        # إنشاء مستخدم تجريبي
        class TestUser:
            def __init__(self):
                self.full_name = "مستخدم تجريبي"
                self.username = "test"
                self.role = "admin"
        
        test_user = TestUser()
        
        # إنشاء النافذة الرئيسية
        from gui.main_window import MainWindow
        main_window = MainWindow(engine=engine, user=test_user)
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار 1: التحقق من إعدادات البار الجانبي
        print("\n1️⃣ اختبار إعدادات البار الجانبي...")
        if hasattr(main_window, 'sidebar_enabled'):
            print(f"✅ إعداد البار الجانبي: {'مفعل' if main_window.sidebar_enabled else 'مطفي'}")
        
        if hasattr(main_window, 'sidebar_shortcut'):
            print("✅ اختصار Ctrl+B متوفر")
        
        # اختبار 2: اختبار البار الجانبي الرئيسي
        print("\n2️⃣ اختبار البار الجانبي الرئيسي...")
        if hasattr(main_window, 'sidebar'):
            print("✅ البار الجانبي الرئيسي موجود")
            if main_window.sidebar_enabled:
                print("✅ البار الجانبي مفعل ومرئي")
            else:
                print("⚠️ البار الجانبي مطفي (كما هو متوقع)")
        
        # اختبار 3: اختبار تبويب المبيعات
        print("\n3️⃣ اختبار تبويب المبيعات...")
        main_window.show_sales()
        
        # انتظار قصير لتحميل التبويب
        QTimer.singleShot(500, lambda: test_sales_tab(main_window))
        
        # اختبار 4: اختبار التبويبات الأخرى
        print("\n4️⃣ اختبار التبويبات الأخرى...")
        
        # قائمة التبويبات للاختبار
        test_tabs = [
            ("📦", "المخزون", main_window.show_inventory),
            ("👥", "العملاء والموردين", main_window.show_contacts),
            ("📊", "التقارير", main_window.show_reports),
            ("🛒", "المشتريات", main_window.show_purchases),
        ]
        
        def test_next_tab(index=0):
            if index < len(test_tabs):
                icon, name, func = test_tabs[index]
                print(f"   اختبار تبويب: {icon} {name}")
                try:
                    func()
                    print(f"   ✅ تم فتح تبويب {name} بنجاح")
                    
                    # التحقق من وجود البار الجانبي المخفي
                    if hasattr(main_window, 'main_tabs'):
                        current_tab = main_window.main_tabs.currentWidget()
                        if current_tab:
                            hidden_sidebars = current_tab.findChildren(main_window.QFrame, "hiddenSidebar")
                            if hidden_sidebars:
                                print(f"   ✅ البار الجانبي المخفي موجود في تبويب {name}")
                                sidebar = hidden_sidebars[0]
                                if main_window.sidebar_enabled:
                                    if sidebar.isVisible():
                                        print(f"   ✅ البار الجانبي مرئي في تبويب {name}")
                                    else:
                                        print(f"   ⚠️ البار الجانبي مخفي في تبويب {name}")
                                else:
                                    print(f"   ✅ البار الجانبي مطفي في تبويب {name} (كما هو متوقع)")
                            else:
                                print(f"   ⚠️ لم يتم العثور على البار الجانبي في تبويب {name}")
                    
                    # الانتقال للتبويب التالي
                    QTimer.singleShot(300, lambda: test_next_tab(index + 1))
                    
                except Exception as e:
                    print(f"   ❌ خطأ في فتح تبويب {name}: {e}")
                    test_next_tab(index + 1)
            else:
                # انتهاء الاختبارات
                finish_tests(main_window)
        
        # بدء اختبار التبويبات
        QTimer.singleShot(1000, test_next_tab)
        
        # عرض النافذة
        main_window.show()
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        QMessageBox.critical(None, "خطأ في الاختبار", f"حدث خطأ:\n{str(e)}")
        return 1

def test_sales_tab(main_window):
    """اختبار تبويب المبيعات"""
    try:
        if hasattr(main_window, 'main_tabs'):
            # التحقق من التبويب الحالي
            current_index = main_window.main_tabs.currentIndex()
            if current_index == 1:  # تبويب المبيعات
                print("✅ تم الانتقال لتبويب المبيعات")
                
                # التحقق من وجود البار الجانبي المخفي
                current_tab = main_window.main_tabs.currentWidget()
                if current_tab:
                    from PyQt5.QtWidgets import QFrame
                    hidden_sidebars = current_tab.findChildren(QFrame, "hiddenSidebar")
                    if hidden_sidebars:
                        print("✅ البار الجانبي المخفي موجود في تبويب المبيعات")
                        sidebar = hidden_sidebars[0]
                        if main_window.sidebar_enabled:
                            if sidebar.isVisible():
                                print("✅ البار الجانبي مرئي في تبويب المبيعات")
                            else:
                                print("⚠️ البار الجانبي مخفي في تبويب المبيعات")
                        else:
                            print("✅ البار الجانبي مطفي في تبويب المبيعات (كما هو متوقع)")
                    else:
                        print("⚠️ لم يتم العثور على البار الجانبي في تبويب المبيعات")
            else:
                print("⚠️ لم يتم الانتقال لتبويب المبيعات")
    except Exception as e:
        print(f"❌ خطأ في اختبار تبويب المبيعات: {e}")

def finish_tests(main_window):
    """إنهاء الاختبارات وعرض النتائج"""
    print("\n🎉 تم الانتهاء من جميع الاختبارات!")
    
    # اختبار الاختصار
    print("\n5️⃣ اختبار الاختصار Ctrl+B...")
    if hasattr(main_window, 'toggle_sidebar_visibility'):
        print("✅ دالة تبديل البار الجانبي متوفرة")
    
    # عرض التقرير النهائي
    QMessageBox.information(
        main_window,
        "تقرير الاختبار الشامل 🎉",
        "✅ تم اختبار نظام البار الجانبي على جميع الواجهات!\n\n"
        "🔧 المميزات المختبرة:\n"
        "• البار الجانبي الرئيسي\n"
        "• البارات الجانبية المخفية في جميع التبويبات\n"
        "• نظام التحكم في الإظهار/الإخفاء\n"
        "• اختصار Ctrl+B\n"
        "• التحكم من إعدادات النظام\n\n"
        "🎯 جرب الآن:\n"
        "1. اضغط Ctrl+B لتبديل البار\n"
        "2. انتقل بين التبويبات المختلفة\n"
        "3. مرر الماوس على البار الجانبي\n"
        "4. غير الإعدادات من إعدادات النظام"
    )
    
    print("\n📋 التقرير النهائي:")
    print("✅ البار الجانبي الرئيسي: تم الاختبار")
    print("✅ تبويب المبيعات: تم الاختبار")
    print("✅ التبويبات الأخرى: تم الاختبار")
    print("✅ نظام التحكم: تم الاختبار")
    print("✅ الاختصارات: تم الاختبار")
    print("\n🚀 النظام جاهز للاستخدام على جميع الواجهات!")

if __name__ == "__main__":
    sys.exit(test_all_sidebars())
