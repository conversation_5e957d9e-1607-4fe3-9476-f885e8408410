from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QComboBox, QLabel, QDateEdit, QTableWidget,
                             QTableWidgetItem, QFileDialog, QMessageBox, QFrame,
                             QHeaderView, QGridLayout, QDialog, QGroupBox, QFormLayout,
                             QProgressBar, QSplitter, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QPainter, QFont, QPalette
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QBarSeries, QBarSet,
                          QValueAxis, QBarCategoryAxis, QPieSeries, QAreaSeries,
                          QScatterSeries, QSplineSeries)
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, text
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from utils.dialog_utils import make_dialog_resizable
from utils.currency_formatter import format_currency, format_number
from utils.theme_manager import theme_manager
import xlsxwriter
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from datetime import datetime, timedelta
import arabic_reshaper
from bidi.algorithm import get_display
import json
import os

class FinancialAnalysisDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        
    def setup_ui(self):
        # إعداد النافذة مع خاصية التكبير
        make_dialog_resizable(self, 800, 600, 1000, 700)
        self.setWindowTitle("التحليل المالي")
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # إطار الفترة الزمنية
        period_group = QGroupBox("الفترة الزمنية")
        period_layout = QFormLayout()
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم", "الأسبوع", "الشهر", "الربع السنوي", "السنة", "مخصص"
        ])
        period_layout.addRow("عرض بيانات:", self.period_combo)
        
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.end_date.setDate(QDate.currentDate())
        
        period_layout.addRow("من:", self.start_date)
        period_layout.addRow("إلى:", self.end_date)
        period_group.setLayout(period_layout)
        layout.addWidget(period_group)
        
        # مساحة الرسوم البيانية
        self.figure, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(10, 4))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # أزرار التحكم
        buttons = QHBoxLayout()
        update_btn = QPushButton("تحديث التحليل")
        update_btn.clicked.connect(self.update_analysis)
        export_btn = QPushButton("تصدير التقرير")
        export_btn.clicked.connect(self.export_analysis)
        buttons.addWidget(update_btn)
        buttons.addWidget(export_btn)
        layout.addLayout(buttons)
        
        # تحديث البيانات الأولية
        self.update_analysis()
        
    def update_analysis(self):
        period = self.period_combo.currentText()
        if period != "مخصص":
            today = datetime.now()
            if period == "اليوم":
                self.start_date.setDate(QDate.currentDate())
            elif period == "الأسبوع":
                self.start_date.setDate(QDate.currentDate().addDays(-7))
            elif period == "الشهر":
                self.start_date.setDate(QDate.currentDate().addMonths(-1))
            elif period == "الربع السنوي":
                self.start_date.setDate(QDate.currentDate().addMonths(-3))
            elif period == "السنة":
                self.start_date.setDate(QDate.currentDate().addYears(-1))
            self.end_date.setDate(QDate.currentDate())
        
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()
        
        with Session(self.engine) as session:
            # تحليل المبيعات والمشتريات
            self.ax1.clear()
            sales_data = session.query(
                func.date(Transaction.date).label('date'),
                func.sum(Transaction.total_amount).label('total')
            ).filter(
                and_(
                    Transaction.type == TransactionType.SALE,
                    Transaction.date.between(start_date, end_date)
                )
            ).group_by(func.date(Transaction.date)).all()
            
            purchases_data = session.query(
                func.date(Transaction.date).label('date'),
                func.sum(Transaction.total_amount).label('total')
            ).filter(
                and_(
                    Transaction.type == TransactionType.PURCHASE,
                    Transaction.date.between(start_date, end_date)
                )
            ).group_by(func.date(Transaction.date)).all()
            
            dates = [d.date for d in sales_data]
            sales = [d.total for d in sales_data]
            purchases = [d.total for d in purchases_data]
            
            self.ax1.plot(dates, sales, label='المبيعات', color='green')
            self.ax1.plot(dates, purchases, label='المشتريات', color='red')
            self.ax1.set_title('تحليل المبيعات والمشتريات')
            self.ax1.legend()
            self.ax1.tick_params(axis='x', rotation=45)
            
            # تحليل الأرباح
            self.ax2.clear()
            profits = []
            for s, p in zip(sales, purchases):
                profits.append(s - p)
            
            self.ax2.bar(dates, profits, color=['green' if x > 0 else 'red' for x in profits])
            self.ax2.set_title('صافي الربح/الخسارة')
            self.ax2.tick_params(axis='x', rotation=45)
            
            self.figure.tight_layout()
            self.canvas.draw()
            
    def export_analysis(self):
        try:
            file_name, _ = QFileDialog.getSaveFileName(
                self, "حفظ التحليل", "", "PDF Files (*.pdf);;Excel Files (*.xlsx)"
            )
            
            if file_name:
                if file_name.endswith('.pdf'):
                    self.figure.savefig(file_name, bbox_inches='tight', dpi=300)
                elif file_name.endswith('.xlsx'):
                    self.export_to_excel(file_name)
                    
                QMessageBox.information(self, "نجاح", "تم تصدير التحليل بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")
            
    def export_to_excel(self, filename):
        workbook = xlsxwriter.Workbook(filename)
        worksheet = workbook.add_worksheet()
        
        # تنسيقات
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#2C3E50',
            'font_color': 'white',
            'border': 1
        })
        
        # إضافة البيانات
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()
        
        with Session(self.engine) as session:
            sales_data = session.query(
                func.date(Transaction.date).label('date'),
                func.sum(Transaction.total_amount).label('total')
            ).filter(
                and_(
                    Transaction.type == TransactionType.SALE,
                    Transaction.date.between(start_date, end_date)
                )
            ).group_by(func.date(Transaction.date)).all()
            
            # كتابة البيانات
            worksheet.write(0, 0, "التاريخ", header_format)
            worksheet.write(0, 1, "المبيعات", header_format)
            worksheet.write(0, 2, "المشتريات", header_format)
            worksheet.write(0, 3, "صافي الربح", header_format)
            
            for row, data in enumerate(sales_data, start=1):
                worksheet.write(row, 0, data.date.strftime("%Y-%m-%d"))
                worksheet.write(row, 1, data.total)
                # إضافة المزيد من البيانات...
                
        workbook.close()

class ReportsWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # إطار التحكم في التقرير
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        control_layout = QGridLayout()
        control_frame.setLayout(control_layout)
        
        # اختيار نوع التقرير
        self.report_type = QComboBox()
        self.report_type.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 250px;
            }
        """)
        self.report_type.addItems([
            "تقرير المبيعات",
            "تقرير المشتريات",
            "كشف حساب عميل",
            "كشف حساب مورد",
            "تقرير المخزون",
            "الأرباح والخسائر",
            "التدفقات النقدية",
            "التحليل المالي"
        ])
        
        # اختيار الفترة الزمنية
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.end_date.setDate(QDate.currentDate())
        
        date_style = """
            QDateEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
        """
        self.start_date.setStyleSheet(date_style)
        self.end_date.setStyleSheet(date_style)
        
        control_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        control_layout.addWidget(self.report_type, 0, 1)
        control_layout.addWidget(QLabel("من:"), 0, 2)
        control_layout.addWidget(self.start_date, 0, 3)
        control_layout.addWidget(QLabel("إلى:"), 0, 4)
        control_layout.addWidget(self.end_date, 0, 5)
        
        layout.addWidget(control_frame)
        
        # جدول النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        results_layout = QVBoxLayout()
        results_frame.setLayout(results_layout)
        
        self.results_table = QTableWidget()
        self.results_table.setStyleSheet("""
            QTableWidget {
                border: none;
            }
            QTableWidget::item {
                padding: 8px;
            }
        """)
        
        results_layout.addWidget(self.results_table)
        layout.addWidget(results_frame)
        
        # أزرار التصدير
        export_frame = QFrame()
        export_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        export_layout = QHBoxLayout()
        export_frame.setLayout(export_layout)
        
        self.generate_btn = QPushButton("عرض التقرير")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
        """)
        
        self.export_pdf_btn = QPushButton("تصدير PDF")
        self.export_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
        """)
        
        self.export_excel_btn = QPushButton("تصدير Excel")
        self.export_excel_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        export_layout.addWidget(self.generate_btn)
        export_layout.addStretch()
        export_layout.addWidget(self.export_pdf_btn)
        export_layout.addWidget(self.export_excel_btn)
        
        # إضافة زر التحليل المالي
        analyze_btn = QPushButton("التحليل المالي")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        analyze_btn.clicked.connect(self.show_analysis)
        
        export_layout.addWidget(analyze_btn)
        
        layout.addWidget(export_frame)
        
        # ربط الأحداث
        self.generate_btn.clicked.connect(self.generate_report)
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        self.report_type.currentTextChanged.connect(self.setup_report_columns)
        
        # إعداد الأعمدة الافتراضية
        self.setup_report_columns()
        
    def setup_report_columns(self):
        report_type = self.report_type.currentText()
        if report_type == "تقرير المبيعات":
            headers = ["التاريخ", "رقم الفاتورة", "العميل", "إجمالي الفاتورة", "المدفوع", "المتبقي"]
        elif report_type == "تقرير المشتريات":
            headers = ["التاريخ", "رقم الفاتورة", "المورد", "إجمالي الفاتورة", "المدفوع", "المتبقي"]
        elif report_type in ["كشف حساب عميل", "كشف حساب مورد"]:
            headers = ["التاريخ", "البيان", "مدين", "دائن", "الرصيد"]
        elif report_type == "تقرير المخزون":
            headers = ["كود المنتج", "اسم المنتج", "سعر الشراء", "سعر البيع", "الكمية", "القيمة"]
        else:  # الأرباح والخسائر
            headers = ["البند", "مدين", "دائن", "الرصيد"]
            
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # تنسيق عرض الجدول
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(False)
        for i in range(len(headers)):
            if i == 1:  # جعل عمود البيان/اسم المنتج مرناً
                header.setSectionResizeMode(i, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.Fixed)
                header.resizeSection(i, 120)
                
    def generate_report(self):
        report_type = self.report_type.currentText()
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()
        
        with Session(self.engine) as session:
            if report_type == "تقرير المخزون":
                self.generate_inventory_report(session)
            elif report_type == "الأرباح والخسائر":
                self.generate_profit_loss_report(session, start_date, end_date)
            else:
                self.generate_transaction_report(session, start_date, end_date)
                
    def create_table_item(self, text, bg_color, alignment=Qt.AlignCenter):
        item = QTableWidgetItem(str(text))
        item.setBackground(bg_color)
        item.setTextAlignment(alignment)
        return item
                
    def generate_inventory_report(self, session):
        products = session.query(Product).all()
        self.results_table.setRowCount(len(products))
        
        total_value = 0
        for row, product in enumerate(products):
            bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")
            
            self.results_table.setItem(row, 0, self.create_table_item(product.id, bg_color))
            self.results_table.setItem(row, 1, self.create_table_item(product.name, bg_color))
            self.results_table.setItem(row, 2, self.create_table_item(f"{product.purchase_price:,.2f}", bg_color))
            self.results_table.setItem(row, 3, self.create_table_item(f"{product.sale_price:,.2f}", bg_color))
            self.results_table.setItem(row, 4, self.create_table_item(product.quantity, bg_color))
            
            value = product.quantity * product.purchase_price
            total_value += value
            self.results_table.setItem(row, 5, self.create_table_item(f"{value:,.2f}", bg_color))
            
        # إضافة صف المجموع
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        self.results_table.setItem(row, 0, QTableWidgetItem("المجموع"))
        self.results_table.setItem(row, 5, QTableWidgetItem(f"{total_value:,.2f}"))
        
        # تنسيق صف المجموع
        for col in range(self.results_table.columnCount()):
            item = self.results_table.item(row, col)
            if item:
                item.setBackground(QColor("#E2E6EA"))
                item.setFont(QFont("Arial", weight=QFont.Bold))
                
    def export_to_pdf(self):
        file_name, _ = QFileDialog.getSaveFileName(self, "حفظ التقرير", "", "PDF Files (*.pdf)")
        if not file_name:
            return
            
        try:
            # إعداد الخط العربي
            pdfmetrics.registerFont(TTFont('Arial', 'arial.ttf'))
            
            # إنشاء ملف PDF
            c = canvas.Canvas(file_name)
            c.setFont('Arial', 14)
            
            # عنوان التقرير
            title = get_display(arabic_reshaper.reshape(self.report_type.currentText()))
            c.drawString(450, 800, title)
            
            # ترويسة الجدول
            y = 750
            for col in range(self.results_table.columnCount()):
                header = get_display(arabic_reshaper.reshape(
                    self.results_table.horizontalHeaderItem(col).text()
                ))
                c.drawString(50 + col * 100, y, header)
            y -= 20
            
            # بيانات الجدول
            for row in range(self.results_table.rowCount()):
                if y < 50:  # صفحة جديدة
                    c.showPage()
                    c.setFont('Arial', 14)
                    y = 750
                    
                for col in range(self.results_table.columnCount()):
                    item = self.results_table.item(row, col)
                    if item:
                        text = get_display(arabic_reshaper.reshape(item.text()))
                        c.drawString(50 + col * 100, y, text)
                y -= 20
                
            c.save()
            QMessageBox.information(self, "تم", "تم تصدير التقرير بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            
    def export_to_excel(self):
        file_name, _ = QFileDialog.getSaveFileName(self, "حفظ التقرير", "", "Excel Files (*.xlsx)")
        if not file_name:
            return
            
        try:
            workbook = xlsxwriter.Workbook(file_name)
            worksheet = workbook.add_worksheet()
            
            # تنسيقات الخلايا
            header_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#2C3E50',
                'font_color': 'white',
                'border': 1
            })
            
            cell_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            # كتابة العناوين
            for col in range(self.results_table.columnCount()):
                header = self.results_table.horizontalHeaderItem(col).text()
                worksheet.write(0, col, header, header_format)
                worksheet.set_column(col, col, 15)  # تعيين عرض العمود
                
            # كتابة البيانات
            for row in range(self.results_table.rowCount()):
                for col in range(self.results_table.columnCount()):
                    item = self.results_table.item(row, col)
                    if item:
                        worksheet.write(row + 1, col, item.text(), cell_format)
                        
            workbook.close()
            QMessageBox.information(self, "تم", "تم تصدير التقرير بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            
    def show_analysis(self):
        dialog = FinancialAnalysisDialog(self.engine)
        dialog.exec_()

    def update_theme(self):
        """تحديث ألوان الثيم"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث الجدول
        self.results_table.setStyleSheet(theme_manager.get_stylesheet("table"))

        # تحديث الأزرار والحقول
        colors = theme_manager.get_colors()

        # تحديث إطار التحكم
        control_style = f"""
            QFrame {{
                background-color: {colors['card_bg']};
                border-radius: 5px;
                padding: 15px;
                border: 1px solid {colors['border_color']};
            }}
        """

        # تحديث القوائم المنسدلة والتواريخ
        combo_style = f"""
            QComboBox, QDateEdit {{
                padding: 8px;
                border: 1px solid {colors['border_color']};
                border-radius: 4px;
                min-width: 150px;
                background-color: {colors['input_bg']};
                color: {colors['input_text']};
            }}
            QComboBox:focus, QDateEdit:focus {{
                border-color: {colors['input_focus']};
            }}
        """

        # تحديث الأزرار
        button_style = theme_manager.get_stylesheet("button")

        # تطبيق الأنماط
        if hasattr(self, 'report_type'):
            self.report_type.setStyleSheet(combo_style)
        if hasattr(self, 'start_date'):
            self.start_date.setStyleSheet(combo_style)
        if hasattr(self, 'end_date'):
            self.end_date.setStyleSheet(combo_style)

        # تحديث الأزرار
        if hasattr(self, 'generate_btn'):
            self.generate_btn.setStyleSheet(button_style)
        if hasattr(self, 'export_pdf_btn'):
            self.export_pdf_btn.setStyleSheet(button_style)
        if hasattr(self, 'export_excel_btn'):
            self.export_excel_btn.setStyleSheet(button_style)

    def generate_transaction_report(self, session, start_date, end_date):
        """تقرير المعاملات (مبيعات/مشتريات)"""
        # هذه دالة مؤقتة - يمكن تطويرها لاحقاً
        pass

    def generate_profit_loss_report(self, session, start_date, end_date):
        """تقرير الأرباح والخسائر"""
        # هذه دالة مؤقتة - يمكن تطويرها لاحقاً
        pass