#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي المحسن - نسخ احتياطي ذكي ومجدول
Enhanced Backup System - Smart and scheduled backups
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QProgressBar, QFileDialog, QMessageBox,
                             QFrame, QGroupBox, QFormLayout, QSpinBox,
                             QCheckBox, QComboBox, QTextEdit, QListWidget,
                             QListWidgetItem, QTabWidget)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QDateTime
from PyQt5.QtGui import QFont, QColor
import shutil
import os
import zipfile
import json
import sqlite3
from datetime import datetime, timedelta
import schedule
import time
import threading


class BackupWorker(QThread):
    """عامل النسخ الاحتياطي في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    backup_completed = pyqtSignal(bool, str)
    
    def __init__(self, backup_config):
        super().__init__()
        self.backup_config = backup_config
        
    def run(self):
        """تشغيل عملية النسخ الاحتياطي"""
        try:
            self.status_updated.emit("🔄 بدء النسخ الاحتياطي...")
            self.progress_updated.emit(0)
            
            # إنشاء مجلد النسخ الاحتياطي
            backup_folder = self.create_backup_folder()
            self.progress_updated.emit(10)
            
            # نسخ قاعدة البيانات
            self.status_updated.emit("📊 نسخ قاعدة البيانات...")
            self.backup_database(backup_folder)
            self.progress_updated.emit(30)
            
            # نسخ ملفات الإعدادات
            self.status_updated.emit("⚙️ نسخ ملفات الإعدادات...")
            self.backup_settings(backup_folder)
            self.progress_updated.emit(50)
            
            # نسخ الصور والمرفقات
            self.status_updated.emit("🖼️ نسخ الصور والمرفقات...")
            self.backup_attachments(backup_folder)
            self.progress_updated.emit(70)
            
            # إنشاء ملف معلومات النسخة
            self.status_updated.emit("📝 إنشاء ملف المعلومات...")
            self.create_backup_info(backup_folder)
            self.progress_updated.emit(80)
            
            # ضغط النسخة الاحتياطية
            if self.backup_config.get('compress', True):
                self.status_updated.emit("🗜️ ضغط النسخة الاحتياطية...")
                zip_path = self.compress_backup(backup_folder)
                self.progress_updated.emit(90)
                
                # حذف المجلد غير المضغوط
                shutil.rmtree(backup_folder)
                backup_path = zip_path
            else:
                backup_path = backup_folder
            
            # تنظيف النسخ القديمة
            if self.backup_config.get('cleanup_old', True):
                self.status_updated.emit("🧹 تنظيف النسخ القديمة...")
                self.cleanup_old_backups()
            
            self.progress_updated.emit(100)
            self.status_updated.emit("✅ تم إنشاء النسخة الاحتياطية بنجاح")
            self.backup_completed.emit(True, backup_path)
            
        except Exception as e:
            self.status_updated.emit(f"❌ خطأ: {str(e)}")
            self.backup_completed.emit(False, str(e))
    
    def create_backup_folder(self):
        """إنشاء مجلد النسخة الاحتياطية"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{timestamp}"
        
        backup_base = self.backup_config.get('backup_path', 'backups')
        os.makedirs(backup_base, exist_ok=True)
        
        backup_folder = os.path.join(backup_base, backup_name)
        os.makedirs(backup_folder, exist_ok=True)
        
        return backup_folder
    
    def backup_database(self, backup_folder):
        """نسخ قاعدة البيانات"""
        db_path = self.backup_config.get('database_path', 'accounting.db')
        
        if os.path.exists(db_path):
            # نسخ ملف قاعدة البيانات
            backup_db_path = os.path.join(backup_folder, 'accounting.db')
            shutil.copy2(db_path, backup_db_path)
            
            # إنشاء نسخة SQL للقراءة
            self.export_database_to_sql(db_path, os.path.join(backup_folder, 'database_export.sql'))
    
    def export_database_to_sql(self, db_path, sql_path):
        """تصدير قاعدة البيانات إلى ملف SQL"""
        try:
            conn = sqlite3.connect(db_path)
            
            with open(sql_path, 'w', encoding='utf-8') as f:
                for line in conn.iterdump():
                    f.write('%s\n' % line)
            
            conn.close()
        except Exception as e:
            print(f"خطأ في تصدير SQL: {e}")
    
    def backup_settings(self, backup_folder):
        """نسخ ملفات الإعدادات"""
        settings_files = [
            'company_settings.json',
            'user_settings.json',
            'license.dat'
        ]
        
        settings_folder = os.path.join(backup_folder, 'settings')
        os.makedirs(settings_folder, exist_ok=True)
        
        for file_name in settings_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, os.path.join(settings_folder, file_name))
    
    def backup_attachments(self, backup_folder):
        """نسخ الصور والمرفقات"""
        attachments_folders = ['assets', 'uploads', 'images']
        
        for folder_name in attachments_folders:
            if os.path.exists(folder_name):
                backup_attachments_folder = os.path.join(backup_folder, folder_name)
                shutil.copytree(folder_name, backup_attachments_folder)
    
    def create_backup_info(self, backup_folder):
        """إنشاء ملف معلومات النسخة الاحتياطية"""
        backup_info = {
            'timestamp': datetime.now().isoformat(),
            'version': '1.0',
            'type': 'full_backup',
            'files_included': [],
            'database_size': 0,
            'total_size': 0
        }
        
        # حساب أحجام الملفات
        total_size = 0
        for root, dirs, files in os.walk(backup_folder):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                total_size += file_size
                
                relative_path = os.path.relpath(file_path, backup_folder)
                backup_info['files_included'].append({
                    'path': relative_path,
                    'size': file_size
                })
        
        backup_info['total_size'] = total_size
        
        # حفظ ملف المعلومات
        info_path = os.path.join(backup_folder, 'backup_info.json')
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
    
    def compress_backup(self, backup_folder):
        """ضغط النسخة الاحتياطية"""
        zip_path = f"{backup_folder}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, backup_folder)
                    zipf.write(file_path, arc_name)
        
        return zip_path
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        backup_base = self.backup_config.get('backup_path', 'backups')
        max_backups = self.backup_config.get('max_backups', 10)
        
        if not os.path.exists(backup_base):
            return
        
        # الحصول على قائمة النسخ الاحتياطية
        backups = []
        for item in os.listdir(backup_base):
            item_path = os.path.join(backup_base, item)
            if os.path.isfile(item_path) and item.endswith('.zip'):
                backups.append((item_path, os.path.getmtime(item_path)))
            elif os.path.isdir(item_path) and item.startswith('backup_'):
                backups.append((item_path, os.path.getmtime(item_path)))
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x[1], reverse=True)
        
        # حذف النسخ الزائدة
        for backup_path, _ in backups[max_backups:]:
            try:
                if os.path.isfile(backup_path):
                    os.remove(backup_path)
                else:
                    shutil.rmtree(backup_path)
            except Exception as e:
                print(f"خطأ في حذف النسخة القديمة {backup_path}: {e}")


class RestoreWorker(QThread):
    """عامل الاستعادة في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    restore_completed = pyqtSignal(bool, str)
    
    def __init__(self, backup_path, restore_config):
        super().__init__()
        self.backup_path = backup_path
        self.restore_config = restore_config
        
    def run(self):
        """تشغيل عملية الاستعادة"""
        try:
            self.status_updated.emit("🔄 بدء عملية الاستعادة...")
            self.progress_updated.emit(0)
            
            # فك ضغط النسخة الاحتياطية
            if self.backup_path.endswith('.zip'):
                self.status_updated.emit("📦 فك ضغط النسخة الاحتياطية...")
                extract_folder = self.extract_backup()
                self.progress_updated.emit(20)
            else:
                extract_folder = self.backup_path
            
            # قراءة معلومات النسخة
            self.status_updated.emit("📋 قراءة معلومات النسخة...")
            backup_info = self.read_backup_info(extract_folder)
            self.progress_updated.emit(30)
            
            # استعادة قاعدة البيانات
            if self.restore_config.get('restore_database', True):
                self.status_updated.emit("📊 استعادة قاعدة البيانات...")
                self.restore_database(extract_folder)
                self.progress_updated.emit(60)
            
            # استعادة الإعدادات
            if self.restore_config.get('restore_settings', True):
                self.status_updated.emit("⚙️ استعادة الإعدادات...")
                self.restore_settings(extract_folder)
                self.progress_updated.emit(80)
            
            # استعادة المرفقات
            if self.restore_config.get('restore_attachments', True):
                self.status_updated.emit("🖼️ استعادة المرفقات...")
                self.restore_attachments(extract_folder)
                self.progress_updated.emit(90)
            
            # تنظيف الملفات المؤقتة
            if self.backup_path.endswith('.zip'):
                shutil.rmtree(extract_folder)
            
            self.progress_updated.emit(100)
            self.status_updated.emit("✅ تمت الاستعادة بنجاح")
            self.restore_completed.emit(True, "تمت الاستعادة بنجاح")
            
        except Exception as e:
            self.status_updated.emit(f"❌ خطأ: {str(e)}")
            self.restore_completed.emit(False, str(e))
    
    def extract_backup(self):
        """فك ضغط النسخة الاحتياطية"""
        extract_folder = os.path.splitext(self.backup_path)[0] + "_temp"
        
        with zipfile.ZipFile(self.backup_path, 'r') as zipf:
            zipf.extractall(extract_folder)
        
        return extract_folder
    
    def read_backup_info(self, extract_folder):
        """قراءة معلومات النسخة الاحتياطية"""
        info_path = os.path.join(extract_folder, 'backup_info.json')
        
        if os.path.exists(info_path):
            with open(info_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return {}
    
    def restore_database(self, extract_folder):
        """استعادة قاعدة البيانات"""
        backup_db_path = os.path.join(extract_folder, 'accounting.db')
        
        if os.path.exists(backup_db_path):
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
            current_db = 'accounting.db'
            if os.path.exists(current_db):
                backup_current = f"{current_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(current_db, backup_current)
            
            # استعادة قاعدة البيانات
            shutil.copy2(backup_db_path, current_db)
    
    def restore_settings(self, extract_folder):
        """استعادة الإعدادات"""
        settings_folder = os.path.join(extract_folder, 'settings')
        
        if os.path.exists(settings_folder):
            for file_name in os.listdir(settings_folder):
                source_path = os.path.join(settings_folder, file_name)
                dest_path = file_name
                
                # إنشاء نسخة احتياطية من الملف الحالي
                if os.path.exists(dest_path):
                    backup_path = f"{dest_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(dest_path, backup_path)
                
                shutil.copy2(source_path, dest_path)
    
    def restore_attachments(self, extract_folder):
        """استعادة المرفقات"""
        attachments_folders = ['assets', 'uploads', 'images']
        
        for folder_name in attachments_folders:
            source_folder = os.path.join(extract_folder, folder_name)
            
            if os.path.exists(source_folder):
                # إنشاء نسخة احتياطية من المجلد الحالي
                if os.path.exists(folder_name):
                    backup_folder = f"{folder_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.move(folder_name, backup_folder)
                
                shutil.copytree(source_folder, folder_name)


class ScheduledBackupManager:
    """مدير النسخ الاحتياطي المجدولة"""
    
    def __init__(self, backup_config):
        self.backup_config = backup_config
        self.scheduler_thread = None
        self.running = False
    
    def start_scheduler(self):
        """بدء جدولة النسخ الاحتياطي"""
        if self.running:
            return
        
        self.running = True
        
        # إعداد الجدولة
        schedule_type = self.backup_config.get('schedule_type', 'daily')
        schedule_time = self.backup_config.get('schedule_time', '02:00')
        
        if schedule_type == 'daily':
            schedule.every().day.at(schedule_time).do(self.run_scheduled_backup)
        elif schedule_type == 'weekly':
            day = self.backup_config.get('schedule_day', 'sunday')
            schedule.every().week.at(schedule_time).do(self.run_scheduled_backup)
        elif schedule_type == 'monthly':
            schedule.every(30).days.at(schedule_time).do(self.run_scheduled_backup)
        
        # تشغيل الجدولة في خيط منفصل
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    def run_scheduler(self):
        """تشغيل الجدولة"""
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
    
    def run_scheduled_backup(self):
        """تشغيل النسخ الاحتياطي المجدول"""
        try:
            backup_worker = BackupWorker(self.backup_config)
            backup_worker.run()  # تشغيل مباشر للنسخ المجدولة
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي المجدول: {e}")
    
    def stop_scheduler(self):
        """إيقاف الجدولة"""
        self.running = False
        schedule.clear()


class EnhancedBackupWidget(QWidget):
    """واجهة النسخ الاحتياطي المحسنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.backup_worker = None
        self.restore_worker = None
        self.scheduler_manager = None
        self.setup_ui()
        self.load_backup_config()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # العنوان
        title_label = QLabel("💾 النسخ الاحتياطي المحسن")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #4facfe, stop:1 #00f2fe);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # تبويبات
        tabs = QTabWidget()
        
        # تبويب النسخ الاحتياطي
        backup_tab = self.create_backup_tab()
        tabs.addTab(backup_tab, "💾 إنشاء نسخة احتياطية")
        
        # تبويب الاستعادة
        restore_tab = self.create_restore_tab()
        tabs.addTab(restore_tab, "🔄 استعادة")
        
        # تبويب الجدولة
        schedule_tab = self.create_schedule_tab()
        tabs.addTab(schedule_tab, "⏰ الجدولة")
        
        # تبويب النسخ المحفوظة
        saved_backups_tab = self.create_saved_backups_tab()
        tabs.addTab(saved_backups_tab, "📂 النسخ المحفوظة")
        
        layout.addWidget(tabs)
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # إعدادات النسخ الاحتياطي
        settings_group = QGroupBox("⚙️ إعدادات النسخ الاحتياطي")
        settings_layout = QFormLayout()
        
        # مسار الحفظ
        self.backup_path_btn = QPushButton("اختيار مجلد الحفظ")
        self.backup_path_btn.clicked.connect(self.choose_backup_path)
        settings_layout.addRow("مجلد الحفظ:", self.backup_path_btn)
        
        # ضغط النسخة
        self.compress_checkbox = QCheckBox("ضغط النسخة الاحتياطية")
        self.compress_checkbox.setChecked(True)
        settings_layout.addRow("", self.compress_checkbox)
        
        # تنظيف النسخ القديمة
        self.cleanup_checkbox = QCheckBox("تنظيف النسخ القديمة")
        self.cleanup_checkbox.setChecked(True)
        settings_layout.addRow("", self.cleanup_checkbox)
        
        # عدد النسخ المحفوظة
        self.max_backups_spin = QSpinBox()
        self.max_backups_spin.setRange(1, 100)
        self.max_backups_spin.setValue(10)
        settings_layout.addRow("عدد النسخ المحفوظة:", self.max_backups_spin)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # شريط التقدم والحالة
        progress_group = QGroupBox("📊 حالة النسخ الاحتياطي")
        progress_layout = QVBoxLayout()
        
        self.backup_progress = QProgressBar()
        progress_layout.addWidget(self.backup_progress)
        
        self.backup_status = QLabel("جاهز لإنشاء نسخة احتياطية")
        self.backup_status.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.backup_status)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.start_backup_btn = QPushButton("🚀 بدء النسخ الاحتياطي")
        self.start_backup_btn.clicked.connect(self.start_backup)
        self.start_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        buttons_layout.addWidget(self.start_backup_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return widget

    def create_restore_tab(self):
        """إنشاء تبويب الاستعادة"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # اختيار النسخة الاحتياطية
        file_group = QGroupBox("📁 اختيار النسخة الاحتياطية")
        file_layout = QVBoxLayout()

        self.restore_path_btn = QPushButton("اختيار ملف النسخة الاحتياطية")
        self.restore_path_btn.clicked.connect(self.choose_restore_file)
        file_layout.addWidget(self.restore_path_btn)

        self.restore_path_label = QLabel("لم يتم اختيار ملف")
        self.restore_path_label.setStyleSheet("color: #6c757d; font-style: italic;")
        file_layout.addWidget(self.restore_path_label)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # خيارات الاستعادة
        options_group = QGroupBox("⚙️ خيارات الاستعادة")
        options_layout = QVBoxLayout()

        self.restore_database_cb = QCheckBox("استعادة قاعدة البيانات")
        self.restore_database_cb.setChecked(True)
        options_layout.addWidget(self.restore_database_cb)

        self.restore_settings_cb = QCheckBox("استعادة الإعدادات")
        self.restore_settings_cb.setChecked(True)
        options_layout.addWidget(self.restore_settings_cb)

        self.restore_attachments_cb = QCheckBox("استعادة المرفقات والصور")
        self.restore_attachments_cb.setChecked(True)
        options_layout.addWidget(self.restore_attachments_cb)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # شريط التقدم
        progress_group = QGroupBox("📊 حالة الاستعادة")
        progress_layout = QVBoxLayout()

        self.restore_progress = QProgressBar()
        progress_layout.addWidget(self.restore_progress)

        self.restore_status = QLabel("جاهز للاستعادة")
        self.restore_status.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.restore_status)

        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.start_restore_btn = QPushButton("🔄 بدء الاستعادة")
        self.start_restore_btn.clicked.connect(self.start_restore)
        self.start_restore_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.start_restore_btn.setEnabled(False)

        buttons_layout.addWidget(self.start_restore_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        layout.addStretch()

        return widget

    def create_schedule_tab(self):
        """إنشاء تبويب الجدولة"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # تفعيل الجدولة
        enable_group = QGroupBox("🔄 تفعيل النسخ الاحتياطي التلقائي")
        enable_layout = QVBoxLayout()

        self.enable_schedule_cb = QCheckBox("تفعيل النسخ الاحتياطي المجدول")
        self.enable_schedule_cb.stateChanged.connect(self.toggle_schedule)
        enable_layout.addWidget(self.enable_schedule_cb)

        enable_group.setLayout(enable_layout)
        layout.addWidget(enable_group)

        # إعدادات الجدولة
        self.schedule_settings_group = QGroupBox("⏰ إعدادات الجدولة")
        schedule_layout = QFormLayout()

        # نوع الجدولة
        self.schedule_type_combo = QComboBox()
        self.schedule_type_combo.addItems(["يومي", "أسبوعي", "شهري"])
        schedule_layout.addRow("التكرار:", self.schedule_type_combo)

        # وقت التشغيل
        self.schedule_time_edit = QComboBox()
        self.schedule_time_edit.setEditable(True)
        for hour in range(24):
            self.schedule_time_edit.addItem(f"{hour:02d}:00")
        self.schedule_time_edit.setCurrentText("02:00")
        schedule_layout.addRow("الوقت:", self.schedule_time_edit)

        # يوم الأسبوع (للجدولة الأسبوعية)
        self.schedule_day_combo = QComboBox()
        self.schedule_day_combo.addItems([
            "الأحد", "الاثنين", "الثلاثاء", "الأربعاء",
            "الخميس", "الجمعة", "السبت"
        ])
        schedule_layout.addRow("يوم الأسبوع:", self.schedule_day_combo)

        self.schedule_settings_group.setLayout(schedule_layout)
        self.schedule_settings_group.setEnabled(False)
        layout.addWidget(self.schedule_settings_group)

        # حالة الجدولة
        status_group = QGroupBox("📊 حالة الجدولة")
        status_layout = QVBoxLayout()

        self.schedule_status_label = QLabel("الجدولة غير مفعلة")
        self.schedule_status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.schedule_status_label)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.save_schedule_btn = QPushButton("💾 حفظ الإعدادات")
        self.save_schedule_btn.clicked.connect(self.save_schedule_settings)
        self.save_schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        buttons_layout.addWidget(self.save_schedule_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        layout.addStretch()

        return widget

    def create_saved_backups_tab(self):
        """إنشاء تبويب النسخ المحفوظة"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # قائمة النسخ المحفوظة
        list_group = QGroupBox("📂 النسخ الاحتياطية المحفوظة")
        list_layout = QVBoxLayout()

        self.backups_list = QListWidget()
        self.backups_list.itemSelectionChanged.connect(self.on_backup_selected)
        list_layout.addWidget(self.backups_list)

        # أزرار إدارة النسخ
        list_buttons_layout = QHBoxLayout()

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_backups_list)
        list_buttons_layout.addWidget(refresh_btn)

        self.delete_backup_btn = QPushButton("🗑️ حذف")
        self.delete_backup_btn.clicked.connect(self.delete_selected_backup)
        self.delete_backup_btn.setEnabled(False)
        list_buttons_layout.addWidget(self.delete_backup_btn)

        self.restore_selected_btn = QPushButton("🔄 استعادة")
        self.restore_selected_btn.clicked.connect(self.restore_selected_backup)
        self.restore_selected_btn.setEnabled(False)
        list_buttons_layout.addWidget(self.restore_selected_btn)

        list_buttons_layout.addStretch()
        list_layout.addLayout(list_buttons_layout)

        list_group.setLayout(list_layout)
        layout.addWidget(list_group)

        # معلومات النسخة المحددة
        info_group = QGroupBox("ℹ️ معلومات النسخة")
        info_layout = QVBoxLayout()

        self.backup_info_text = QTextEdit()
        self.backup_info_text.setReadOnly(True)
        self.backup_info_text.setMaximumHeight(150)
        info_layout.addWidget(self.backup_info_text)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # تحديث القائمة عند التحميل
        QTimer.singleShot(100, self.refresh_backups_list)

        return widget

    def load_backup_config(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        config_file = 'backup_config.json'

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # تطبيق الإعدادات على الواجهة
                self.compress_checkbox.setChecked(config.get('compress', True))
                self.cleanup_checkbox.setChecked(config.get('cleanup_old', True))
                self.max_backups_spin.setValue(config.get('max_backups', 10))
                self.enable_schedule_cb.setChecked(config.get('schedule_enabled', False))

                if config.get('schedule_enabled', False):
                    self.toggle_schedule(True)

            except Exception as e:
                print(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")

    def save_backup_config(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        config = {
            'backup_path': getattr(self, 'backup_path', 'backups'),
            'compress': self.compress_checkbox.isChecked(),
            'cleanup_old': self.cleanup_checkbox.isChecked(),
            'max_backups': self.max_backups_spin.value(),
            'schedule_enabled': self.enable_schedule_cb.isChecked(),
            'schedule_type': self.schedule_type_combo.currentText(),
            'schedule_time': self.schedule_time_edit.currentText(),
            'schedule_day': self.schedule_day_combo.currentText()
        }

        try:
            with open('backup_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")

    def choose_backup_path(self):
        """اختيار مجلد النسخ الاحتياطي"""
        folder = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخ الاحتياطي")

        if folder:
            self.backup_path = folder
            self.backup_path_btn.setText(f"المجلد: {folder}")

    def choose_restore_file(self):
        """اختيار ملف الاستعادة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف النسخة الاحتياطية",
            "",
            "Backup Files (*.zip);;All Files (*)"
        )

        if file_path:
            self.restore_file_path = file_path
            self.restore_path_label.setText(f"الملف: {os.path.basename(file_path)}")
            self.start_restore_btn.setEnabled(True)

    def start_backup(self):
        """بدء عملية النسخ الاحتياطي"""
        if self.backup_worker and self.backup_worker.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية نسخ احتياطي جارية بالفعل")
            return

        # إعداد النسخ الاحتياطي
        backup_config = {
            'backup_path': getattr(self, 'backup_path', 'backups'),
            'database_path': 'accounting.db',
            'compress': self.compress_checkbox.isChecked(),
            'cleanup_old': self.cleanup_checkbox.isChecked(),
            'max_backups': self.max_backups_spin.value()
        }

        # بدء العملية
        self.backup_worker = BackupWorker(backup_config)
        self.backup_worker.progress_updated.connect(self.backup_progress.setValue)
        self.backup_worker.status_updated.connect(self.backup_status.setText)
        self.backup_worker.backup_completed.connect(self.on_backup_completed)

        self.start_backup_btn.setEnabled(False)
        self.backup_worker.start()

    def start_restore(self):
        """بدء عملية الاستعادة"""
        if not hasattr(self, 'restore_file_path'):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف النسخة الاحتياطية أولاً")
            return

        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self,
            "تأكيد الاستعادة",
            "⚠️ تحذير: ستؤدي عملية الاستعادة إلى استبدال البيانات الحالية.\n\n"
            "هل أنت متأكد من المتابعة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        if self.restore_worker and self.restore_worker.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية استعادة جارية بالفعل")
            return

        # إعداد الاستعادة
        restore_config = {
            'restore_database': self.restore_database_cb.isChecked(),
            'restore_settings': self.restore_settings_cb.isChecked(),
            'restore_attachments': self.restore_attachments_cb.isChecked()
        }

        # بدء العملية
        self.restore_worker = RestoreWorker(self.restore_file_path, restore_config)
        self.restore_worker.progress_updated.connect(self.restore_progress.setValue)
        self.restore_worker.status_updated.connect(self.restore_status.setText)
        self.restore_worker.restore_completed.connect(self.on_restore_completed)

        self.start_restore_btn.setEnabled(False)
        self.restore_worker.start()

    def toggle_schedule(self, enabled=None):
        """تبديل حالة الجدولة"""
        if enabled is None:
            enabled = self.enable_schedule_cb.isChecked()

        self.schedule_settings_group.setEnabled(enabled)

        if enabled:
            self.schedule_status_label.setText("الجدولة مفعلة")
            self.schedule_status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.schedule_status_label.setText("الجدولة غير مفعلة")
            self.schedule_status_label.setStyleSheet("color: #dc3545; font-weight: bold;")

    def save_schedule_settings(self):
        """حفظ إعدادات الجدولة"""
        self.save_backup_config()

        if self.enable_schedule_cb.isChecked():
            # إيقاف الجدولة السابقة
            if self.scheduler_manager:
                self.scheduler_manager.stop_scheduler()

            # بدء جدولة جديدة
            backup_config = {
                'backup_path': getattr(self, 'backup_path', 'backups'),
                'database_path': 'accounting.db',
                'compress': self.compress_checkbox.isChecked(),
                'cleanup_old': self.cleanup_checkbox.isChecked(),
                'max_backups': self.max_backups_spin.value(),
                'schedule_type': self.schedule_type_combo.currentText().lower(),
                'schedule_time': self.schedule_time_edit.currentText(),
                'schedule_day': self.schedule_day_combo.currentText().lower()
            }

            self.scheduler_manager = ScheduledBackupManager(backup_config)
            self.scheduler_manager.start_scheduler()

            QMessageBox.information(self, "نجح", "تم حفظ إعدادات الجدولة وتفعيلها بنجاح")
        else:
            if self.scheduler_manager:
                self.scheduler_manager.stop_scheduler()

            QMessageBox.information(self, "نجح", "تم إيقاف الجدولة")

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        self.backups_list.clear()

        backup_path = getattr(self, 'backup_path', 'backups')

        if not os.path.exists(backup_path):
            return

        # البحث عن النسخ الاحتياطية
        backups = []
        for item in os.listdir(backup_path):
            item_path = os.path.join(backup_path, item)

            if item.endswith('.zip') or (os.path.isdir(item_path) and item.startswith('backup_')):
                # الحصول على معلومات الملف
                stat = os.stat(item_path)
                size = stat.st_size
                modified = datetime.fromtimestamp(stat.st_mtime)

                backups.append({
                    'name': item,
                    'path': item_path,
                    'size': size,
                    'modified': modified
                })

        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['modified'], reverse=True)

        # إضافة للقائمة
        for backup in backups:
            size_mb = backup['size'] / (1024 * 1024)
            item_text = f"{backup['name']} ({size_mb:.1f} MB) - {backup['modified'].strftime('%Y-%m-%d %H:%M')}"

            list_item = QListWidgetItem(item_text)
            list_item.setData(Qt.UserRole, backup)
            self.backups_list.addItem(list_item)

    def on_backup_selected(self):
        """عند اختيار نسخة احتياطية"""
        current_item = self.backups_list.currentItem()

        if current_item:
            self.delete_backup_btn.setEnabled(True)
            self.restore_selected_btn.setEnabled(True)

            # عرض معلومات النسخة
            backup_data = current_item.data(Qt.UserRole)

            info_text = f"""
📁 اسم الملف: {backup_data['name']}
📊 الحجم: {backup_data['size'] / (1024 * 1024):.1f} MB
📅 تاريخ الإنشاء: {backup_data['modified'].strftime('%Y-%m-%d %H:%M:%S')}
📂 المسار: {backup_data['path']}
            """.strip()

            self.backup_info_text.setPlainText(info_text)
        else:
            self.delete_backup_btn.setEnabled(False)
            self.restore_selected_btn.setEnabled(False)
            self.backup_info_text.clear()

    def delete_selected_backup(self):
        """حذف النسخة المحددة"""
        current_item = self.backups_list.currentItem()

        if not current_item:
            return

        backup_data = current_item.data(Qt.UserRole)

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{backup_data['name']}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if os.path.isfile(backup_data['path']):
                    os.remove(backup_data['path'])
                else:
                    shutil.rmtree(backup_data['path'])

                QMessageBox.information(self, "نجح", "تم حذف النسخة الاحتياطية بنجاح")
                self.refresh_backups_list()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف النسخة الاحتياطية:\n{str(e)}")

    def restore_selected_backup(self):
        """استعادة النسخة المحددة"""
        current_item = self.backups_list.currentItem()

        if not current_item:
            return

        backup_data = current_item.data(Qt.UserRole)
        self.restore_file_path = backup_data['path']

        # التبديل إلى تبويب الاستعادة
        parent_tabs = self.parent()
        if hasattr(parent_tabs, 'setCurrentIndex'):
            parent_tabs.setCurrentIndex(1)  # تبويب الاستعادة

        # تحديث معلومات الاستعادة
        self.restore_path_label.setText(f"الملف: {backup_data['name']}")
        self.start_restore_btn.setEnabled(True)

    def on_backup_completed(self, success, message):
        """عند اكتمال النسخ الاحتياطي"""
        self.start_backup_btn.setEnabled(True)

        if success:
            QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{message}")
            self.refresh_backups_list()
        else:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{message}")

    def on_restore_completed(self, success, message):
        """عند اكتمال الاستعادة"""
        self.start_restore_btn.setEnabled(True)

        if success:
            QMessageBox.information(
                self,
                "نجح",
                f"تمت الاستعادة بنجاح:\n{message}\n\nيُنصح بإعادة تشغيل البرنامج."
            )
        else:
            QMessageBox.critical(self, "خطأ", f"فشلت الاستعادة:\n{message}")

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        # إيقاف العمليات الجارية
        if self.backup_worker and self.backup_worker.isRunning():
            self.backup_worker.quit()
            self.backup_worker.wait()

        if self.restore_worker and self.restore_worker.isRunning():
            self.restore_worker.quit()
            self.restore_worker.wait()

        # إيقاف الجدولة
        if self.scheduler_manager:
            self.scheduler_manager.stop_scheduler()

        # حفظ الإعدادات
        self.save_backup_config()

        event.accept()
