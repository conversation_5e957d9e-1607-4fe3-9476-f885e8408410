#!/usr/bin/env python3
"""
سكريبت تطبيق الإصلاحات المتجاوبة
يطبق جميع الحلول الجذرية لمشاكل الشاشات الصغيرة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def apply_all_responsive_fixes():
    """تطبيق جميع الإصلاحات المتجاوبة"""
    
    print("🔧 بدء تطبيق الحلول الجذرية للشاشات الصغيرة...")
    
    try:
        # 1. تطبيق التنسيقات المتجاوبة
        from utils.responsive_style_applier import responsive_style_applier
        app = QApplication.instance()
        if app:
            responsive_style_applier.apply_responsive_styles(app)
            print("✅ تم تطبيق التنسيقات المتجاوبة")
        
        # 2. تحديث إعدادات DPI
        from utils.dpi_manager import dpi_manager
        dpi_manager.update_dpi_settings()
        print("✅ تم تحديث إعدادات DPI")
        
        # 3. إعداد النظام المتجاوب
        from utils.responsive_layout_manager import responsive_manager
        print(f"✅ تم تفعيل النظام المتجاوب - حجم الشاشة: {responsive_manager.screen_size.width()}x{responsive_manager.screen_size.height()}")
        
        if responsive_manager.is_very_small_screen:
            print("📱 تم اكتشاف شاشة صغيرة جداً - سيتم تطبيق تحسينات خاصة")
        elif responsive_manager.is_small_screen:
            print("💻 تم اكتشاف شاشة صغيرة - سيتم تطبيق تحسينات متوسطة")
        else:
            print("🖥️ شاشة عادية - سيتم استخدام التنسيق الافتراضي")
        
        print("🎉 تم تطبيق جميع الحلول الجذرية بنجاح!")
        print("📋 الحلول المطبقة:")
        print("   • تخطيط متجاوب للفواتير")
        print("   • تنسيقات CSS متجاوبة")
        print("   • أحجام خطوط متجاوبة")
        print("   • مسافات وحشو متجاوب")
        print("   • أزرار وحقول إدخال محسنة")
        print("   • جداول محسنة للشاشات الصغيرة")
        print("   • إصلاح تلقائي لتخطيط الفواتير")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق الإصلاحات: {e}")
        return False

def test_responsive_features():
    """اختبار الميزات المتجاوبة"""
    
    print("\n🧪 اختبار الميزات المتجاوبة...")
    
    try:
        from utils.responsive_style_applier import responsive_style_applier
        
        # اختبار الأحجام المتجاوبة
        font_size = responsive_style_applier.get_responsive_font_size()
        spacing = responsive_style_applier.get_responsive_spacing()
        button_height = responsive_style_applier.get_responsive_button_height()
        
        print(f"📏 حجم الخط المتجاوب: {font_size}px")
        print(f"📐 المسافة المتجاوبة: {spacing}px")
        print(f"🔘 ارتفاع الأزرار: {button_height}px")
        
        # اختبار مصحح التخطيط
        from utils.invoice_layout_fixer import invoice_layout_fixer
        print("✅ مصحح تخطيط الفواتير جاهز")
        
        # اختبار مدير التخطيط المتجاوب
        from utils.responsive_layout_manager import responsive_manager
        print("✅ مدير التخطيط المتجاوب جاهز")
        
        print("🎯 جميع الميزات المتجاوبة تعمل بشكل صحيح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return False

def show_responsive_info():
    """عرض معلومات النظام المتجاوب"""
    
    print("\n📊 معلومات النظام المتجاوب:")
    print("=" * 50)
    
    try:
        from utils.responsive_style_applier import responsive_style_applier
        from utils.dpi_manager import dpi_manager
        
        screen_size = responsive_style_applier.screen_size
        print(f"🖥️  حجم الشاشة: {screen_size.width()} × {screen_size.height()}")
        print(f"📱 شاشة صغيرة جداً: {'نعم' if responsive_style_applier.is_very_small_screen else 'لا'}")
        print(f"💻 شاشة صغيرة: {'نعم' if responsive_style_applier.is_small_screen else 'لا'}")
        
        dpi_info = dpi_manager.get_dpi_info()
        print(f"🔍 DPI: {dpi_info.get('dpi', 'غير محدد')}")
        print(f"📏 معامل التكبير: {dpi_info.get('scale_factor', 'غير محدد')}")
        
        # عرض الأحجام المتجاوبة
        print(f"\n📐 الأحجام المتجاوبة:")
        print(f"   • حجم الخط: {responsive_style_applier.get_responsive_font_size()}px")
        print(f"   • المسافات: {responsive_style_applier.get_responsive_spacing()}px")
        print(f"   • الحشو: {responsive_style_applier.get_responsive_padding()}px")
        print(f"   • ارتفاع الأزرار: {responsive_style_applier.get_responsive_button_height()}px")
        print(f"   • ارتفاع حقول الإدخال: {responsive_style_applier.get_responsive_input_height()}px")
        print(f"   • ارتفاع صفوف الجدول: {responsive_style_applier.get_responsive_table_row_height()}px")
        
    except Exception as e:
        print(f"❌ خطأ في عرض المعلومات: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 مرحباً بك في نظام الإصلاحات المتجاوبة!")
    print("=" * 60)
    
    # عرض معلومات النظام
    show_responsive_info()
    
    # تطبيق الإصلاحات
    if apply_all_responsive_fixes():
        # اختبار الميزات
        test_responsive_features()
        
        print("\n🎉 تم تطبيق جميع الحلول بنجاح!")
        print("💡 نصائح للاستخدام الأمثل:")
        print("   • أعد تشغيل البرنامج لضمان تطبيق جميع التحسينات")
        print("   • استخدم وضع ملء الشاشة للحصول على أفضل تجربة")
        print("   • يمكنك تغيير حجم النوافذ وسيتم التكيف تلقائياً")
        
    else:
        print("\n❌ فشل في تطبيق بعض الإصلاحات")
        print("💡 تأكد من:")
        print("   • وجود جميع الملفات المطلوبة")
        print("   • صحة مسار المشروع")
        print("   • عدم وجود أخطاء في الكود")

if __name__ == "__main__":
    # إنشاء تطبيق مؤقت للاختبار
    if not QApplication.instance():
        app = QApplication(sys.argv)
    
    main()
    
    # إنهاء التطبيق إذا تم إنشاؤه هنا
    if QApplication.instance():
        QApplication.instance().quit()
