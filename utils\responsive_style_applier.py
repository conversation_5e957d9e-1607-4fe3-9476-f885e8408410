"""
مطبق التنسيقات المتجاوبة
يطبق التنسيقات المناسبة حسب حجم الشاشة تلقائياً
"""

import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from utils.dpi_manager import dpi_manager


class ResponsiveStyleApplier:
    """مطبق التنسيقات المتجاوبة"""
    
    def __init__(self):
        self.current_style = None
        self.screen_size = dpi_manager.get_screen_size()
        self.is_small_screen = self.screen_size.width() < 1400 or self.screen_size.height() < 900
        self.is_very_small_screen = self.screen_size.width() < 1200 or self.screen_size.height() < 800
        
        # مراقب تغيير حجم الشاشة
        self.resize_timer = QTimer()
        self.resize_timer.timeout.connect(self.check_screen_size_change)
        self.resize_timer.start(2000)  # فحص كل ثانيتين
        
    def apply_responsive_styles(self, app=None):
        """تطبيق التنسيقات المتجاوبة"""
        if app is None:
            app = QApplication.instance()
        
        if not app:
            return
            
        # تحديد التنسيق المناسب
        style_file = self.get_appropriate_style_file()
        
        if style_file and style_file != self.current_style:
            try:
                with open(style_file, 'r', encoding='utf-8') as f:
                    style_content = f.read()
                
                # تطبيق التنسيق
                app.setStyleSheet(style_content)
                self.current_style = style_file
                
                print(f"✅ تم تطبيق التنسيق المتجاوب: {os.path.basename(style_file)}")
                
            except Exception as e:
                print(f"❌ خطأ في تطبيق التنسيق المتجاوب: {e}")
    
    def get_appropriate_style_file(self):
        """الحصول على ملف التنسيق المناسب"""
        base_dir = os.path.dirname(os.path.dirname(__file__))
        gui_dir = os.path.join(base_dir, 'gui')
        
        if self.is_very_small_screen:
            # للشاشات الصغيرة جداً
            style_file = os.path.join(gui_dir, 'responsive_style.qss')
        elif self.is_small_screen:
            # للشاشات الصغيرة
            style_file = os.path.join(gui_dir, 'responsive_style.qss')
        else:
            # للشاشات العادية
            style_file = os.path.join(gui_dir, 'modern_style.qss')
        
        if os.path.exists(style_file):
            return style_file
        
        # إذا لم يوجد الملف، استخدم التنسيق الافتراضي
        return os.path.join(gui_dir, 'style.qss')
    
    def check_screen_size_change(self):
        """فحص تغيير حجم الشاشة"""
        new_screen_size = dpi_manager.get_screen_size()
        
        if new_screen_size != self.screen_size:
            self.screen_size = new_screen_size
            self.is_small_screen = self.screen_size.width() < 1400 or self.screen_size.height() < 900
            self.is_very_small_screen = self.screen_size.width() < 1200 or self.screen_size.height() < 800
            
            # إعادة تطبيق التنسيقات
            self.apply_responsive_styles()
    
    def get_responsive_font_size(self, base_size=11):
        """الحصول على حجم خط متجاوب"""
        if self.is_very_small_screen:
            return max(8, base_size - 3)
        elif self.is_small_screen:
            return max(9, base_size - 2)
        else:
            return base_size
    
    def get_responsive_spacing(self, base_spacing=10):
        """الحصول على مسافة متجاوبة"""
        if self.is_very_small_screen:
            return max(2, base_spacing - 6)
        elif self.is_small_screen:
            return max(3, base_spacing - 4)
        else:
            return base_spacing
    
    def get_responsive_padding(self, base_padding=8):
        """الحصول على حشو متجاوب"""
        if self.is_very_small_screen:
            return max(2, base_padding - 4)
        elif self.is_small_screen:
            return max(3, base_padding - 3)
        else:
            return base_padding
    
    def get_responsive_button_height(self, base_height=35):
        """الحصول على ارتفاع زر متجاوب"""
        if self.is_very_small_screen:
            return max(25, base_height - 10)
        elif self.is_small_screen:
            return max(28, base_height - 7)
        else:
            return base_height
    
    def get_responsive_input_height(self, base_height=30):
        """الحصول على ارتفاع حقل إدخال متجاوب"""
        if self.is_very_small_screen:
            return max(22, base_height - 8)
        elif self.is_small_screen:
            return max(25, base_height - 5)
        else:
            return base_height
    
    def get_responsive_table_row_height(self, base_height=30):
        """الحصول على ارتفاع صف جدول متجاوب"""
        if self.is_very_small_screen:
            return max(18, base_height - 12)
        elif self.is_small_screen:
            return max(22, base_height - 8)
        else:
            return base_height
    
    def apply_responsive_widget_style(self, widget, widget_type="general"):
        """تطبيق تنسيق متجاوب على عنصر محدد"""
        try:
            if widget_type == "button":
                height = self.get_responsive_button_height()
                font_size = self.get_responsive_font_size(10)
                padding = self.get_responsive_padding(6)
                
                widget.setStyleSheet(f"""
                    QPushButton {{
                        min-height: {height}px;
                        max-height: {height + 5}px;
                        font-size: {font_size}px;
                        padding: {padding}px;
                        border-radius: 4px;
                        font-weight: bold;
                    }}
                """)
                
            elif widget_type == "input":
                height = self.get_responsive_input_height()
                font_size = self.get_responsive_font_size(10)
                padding = self.get_responsive_padding(4)
                
                widget.setStyleSheet(f"""
                    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
                        min-height: {height}px;
                        max-height: {height + 3}px;
                        font-size: {font_size}px;
                        padding: {padding}px;
                        border: 1px solid #ddd;
                        border-radius: 3px;
                    }}
                """)
                
            elif widget_type == "table":
                row_height = self.get_responsive_table_row_height()
                font_size = self.get_responsive_font_size(9)
                
                widget.setStyleSheet(f"""
                    QTableWidget {{
                        font-size: {font_size}px;
                        gridline-color: #e0e0e0;
                        border: 1px solid #ddd;
                        border-radius: 6px;
                    }}
                    QTableWidget::item {{
                        padding: 4px;
                        min-height: {row_height}px;
                        border-bottom: 1px solid #f0f0f0;
                    }}
                """)
                
                # تطبيق ارتفاع الصفوف
                if hasattr(widget, 'verticalHeader'):
                    widget.verticalHeader().setDefaultSectionSize(row_height)
                    
        except Exception as e:
            print(f"❌ خطأ في تطبيق التنسيق المتجاوب على العنصر: {e}")
    
    def create_responsive_stylesheet(self, base_styles=""):
        """إنشاء ورقة تنسيق متجاوبة"""
        font_size = self.get_responsive_font_size()
        spacing = self.get_responsive_spacing()
        padding = self.get_responsive_padding()
        button_height = self.get_responsive_button_height()
        input_height = self.get_responsive_input_height()
        
        responsive_styles = f"""
        /* تنسيقات متجاوبة */
        QWidget {{
            font-size: {font_size}px;
        }}
        
        QPushButton {{
            min-height: {button_height}px;
            max-height: {button_height + 5}px;
            padding: {padding}px;
            font-size: {font_size}px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {{
            min-height: {input_height}px;
            max-height: {input_height + 3}px;
            padding: {padding}px;
            font-size: {font_size}px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }}
        
        QVBoxLayout, QHBoxLayout {{
            spacing: {spacing}px;
        }}
        
        QFrame {{
            border-radius: 6px;
            margin: {spacing // 2}px;
            padding: {padding}px;
        }}
        """
        
        return base_styles + responsive_styles


# إنشاء مثيل عام
responsive_style_applier = ResponsiveStyleApplier()
