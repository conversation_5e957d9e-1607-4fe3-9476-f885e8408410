#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التقارير التحليلية الآمنة - بدون matplotlib
Safe Advanced Reports - Without matplotlib
"""

try:
    from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QLabel, QFrame, QComboBox, QDateEdit, QSpinBox,
                               QTabWidget, QScrollArea, QGridLayout, QTableWidget,
                               QTableWidgetItem, QHeaderView, QProgressBar,
                               QTextEdit, QGroupBox, QFormLayout, QMessageBox)
    from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
    from PyQt5.QtGui import QFont, QColor, QPalette
    from sqlalchemy.orm import Session
    from sqlalchemy import func, desc, text
    from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
    from datetime import datetime, timedelta
    import json
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    raise


class SafeAnalyticsEngine(QThread):
    """محرك التحليل الآمن"""
    
    analysis_completed = pyqtSignal(dict)
    progress_updated = pyqtSignal(int, str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, engine, analysis_type="sales"):
        super().__init__()
        self.engine = engine
        self.analysis_type = analysis_type
        
    def run(self):
        """تشغيل التحليل"""
        try:
            self.progress_updated.emit(10, "بدء التحليل...")
            
            if self.analysis_type == "sales":
                result = self.analyze_sales()
            elif self.analysis_type == "customers":
                result = self.analyze_customers()
            elif self.analysis_type == "products":
                result = self.analyze_products()
            elif self.analysis_type == "financial":
                result = self.analyze_financial()
            else:
                result = self.analyze_sales()
            
            self.progress_updated.emit(100, "تم التحليل بنجاح")
            self.analysis_completed.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def analyze_sales(self):
        """تحليل المبيعات"""
        with Session(self.engine) as session:
            self.progress_updated.emit(30, "تحليل بيانات المبيعات...")
            
            # إجمالي المبيعات
            total_sales = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            # عدد الفواتير
            sales_count = session.query(func.count(Transaction.id)).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            # متوسط قيمة الفاتورة
            avg_invoice = total_sales / sales_count if sales_count > 0 else 0
            
            # أفضل العملاء
            top_customers = session.query(
                Customer.name,
                func.sum(Transaction.total_amount).label('total')
            ).join(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).group_by(Customer.id).order_by(desc('total')).limit(5).all()
            
            self.progress_updated.emit(70, "معالجة النتائج...")
            
            return {
                'type': 'sales',
                'total_sales': total_sales,
                'sales_count': sales_count,
                'avg_invoice': avg_invoice,
                'top_customers': [(name, float(total)) for name, total in top_customers]
            }
    
    def analyze_customers(self):
        """تحليل العملاء"""
        with Session(self.engine) as session:
            self.progress_updated.emit(30, "تحليل بيانات العملاء...")
            
            # عدد العملاء
            total_customers = session.query(func.count(Customer.id)).scalar() or 0
            
            # العملاء النشطين
            active_customers = session.query(func.count(func.distinct(Transaction.customer_id))).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            # متوسط المشتريات لكل عميل
            avg_per_customer = session.query(func.avg(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            return {
                'type': 'customers',
                'total_customers': total_customers,
                'active_customers': active_customers,
                'avg_per_customer': float(avg_per_customer)
            }
    
    def analyze_products(self):
        """تحليل المنتجات"""
        with Session(self.engine) as session:
            self.progress_updated.emit(30, "تحليل بيانات المنتجات...")
            
            # عدد المنتجات
            total_products = session.query(func.count(Product.id)).scalar() or 0
            
            # أفضل المنتجات مبيعاً
            top_products = session.query(
                Product.name,
                func.sum(TransactionItem.quantity).label('total_qty'),
                func.sum(TransactionItem.quantity * Product.sale_price).label('total_revenue')
            ).join(TransactionItem).join(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).group_by(Product.id).order_by(desc('total_revenue')).limit(5).all()
            
            return {
                'type': 'products',
                'total_products': total_products,
                'top_products': [(name, int(qty), float(revenue)) for name, qty, revenue in top_products]
            }
    
    def analyze_financial(self):
        """التحليل المالي"""
        with Session(self.engine) as session:
            self.progress_updated.emit(30, "تحليل البيانات المالية...")
            
            # إجمالي المبيعات
            total_sales = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE
            ).scalar() or 0
            
            # إجمالي المشتريات
            total_purchases = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.PURCHASE
            ).scalar() or 0
            
            # إجمالي المصروفات
            total_expenses = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.EXPENSE
            ).scalar() or 0
            
            # صافي الربح
            net_profit = total_sales - total_purchases - total_expenses
            
            # هامش الربح
            profit_margin = (net_profit / total_sales * 100) if total_sales > 0 else 0
            
            return {
                'type': 'financial',
                'total_sales': total_sales,
                'total_purchases': total_purchases,
                'total_expenses': total_expenses,
                'net_profit': net_profit,
                'profit_margin': profit_margin
            }


class SafeAdvancedReportsWidget(QWidget):
    """واجهة التقارير التحليلية الآمنة"""
    
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.current_analysis = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # العنوان
        title_label = QLabel("📊 التقارير التحليلية المتقدمة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط التحكم
        control_frame = self.create_control_frame()
        layout.addWidget(control_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # منطقة النتائج
        self.results_area = QScrollArea()
        self.results_widget = QWidget()
        self.results_layout = QVBoxLayout(self.results_widget)
        self.results_area.setWidget(self.results_widget)
        self.results_area.setWidgetResizable(True)
        layout.addWidget(self.results_area)
        
        # رسالة ترحيبية
        self.show_welcome_message()
        
    def create_control_frame(self):
        """إنشاء شريط التحكم"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(frame)
        
        # نوع التحليل
        layout.addWidget(QLabel("نوع التحليل:"))
        self.analysis_combo = QComboBox()
        self.analysis_combo.addItems([
            "تحليل المبيعات",
            "تحليل العملاء", 
            "تحليل المنتجات",
            "التحليل المالي"
        ])
        layout.addWidget(self.analysis_combo)
        
        layout.addStretch()
        
        # أزرار التحكم
        self.analyze_btn = QPushButton("🔍 بدء التحليل")
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.analyze_btn.clicked.connect(self.start_analysis)
        layout.addWidget(self.analyze_btn)
        
        self.export_btn = QPushButton("📤 تصدير النتائج")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)
        
        return frame
    
    def show_welcome_message(self):
        """عرض رسالة ترحيبية"""
        welcome_label = QLabel("""
        <div style='text-align: center; padding: 50px;'>
            <h2>🎯 مرحباً بك في التقارير التحليلية المتقدمة</h2>
            <p style='font-size: 16px; color: #666;'>
                اختر نوع التحليل من القائمة أعلاه واضغط "بدء التحليل" للحصول على تحليلات مفصلة
            </p>
            <br>
            <p style='font-size: 14px; color: #888;'>
                ✅ تحليل آمن ومستقر<br>
                ✅ نتائج سريعة ودقيقة<br>
                ✅ إمكانية تصدير النتائج
            </p>
        </div>
        """)
        welcome_label.setAlignment(Qt.AlignCenter)
        self.results_layout.addWidget(welcome_label)
    
    def start_analysis(self):
        """بدء التحليل"""
        # تحديد نوع التحليل
        analysis_map = {
            "تحليل المبيعات": "sales",
            "تحليل العملاء": "customers",
            "تحليل المنتجات": "products", 
            "التحليل المالي": "financial"
        }
        
        selected_type = self.analysis_combo.currentText()
        analysis_type = analysis_map.get(selected_type, "sales")
        
        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.analyze_btn.setEnabled(False)
        
        # بدء التحليل
        self.analytics_engine = SafeAnalyticsEngine(self.engine, analysis_type)
        self.analytics_engine.analysis_completed.connect(self.on_analysis_completed)
        self.analytics_engine.progress_updated.connect(self.on_progress_updated)
        self.analytics_engine.error_occurred.connect(self.on_analysis_error)
        self.analytics_engine.start()
    
    def on_progress_updated(self, value, message):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        self.progress_bar.setFormat(f"{message} ({value}%)")
    
    def on_analysis_completed(self, results):
        """عند اكتمال التحليل"""
        self.current_analysis = results
        self.display_results(results)
        
        # إخفاء شريط التقدم وتفعيل الأزرار
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
    
    def on_analysis_error(self, error_message):
        """عند حدوث خطأ في التحليل"""
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)
        
        error_label = QLabel(f"""
        <div style='text-align: center; padding: 30px; color: #dc3545;'>
            <h3>❌ حدث خطأ في التحليل</h3>
            <p>{error_message}</p>
            <p style='font-size: 14px; color: #666;'>
                يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني
            </p>
        </div>
        """)
        
        # مسح النتائج السابقة
        self.clear_results()
        self.results_layout.addWidget(error_label)
    
    def display_results(self, results):
        """عرض نتائج التحليل"""
        # مسح النتائج السابقة
        self.clear_results()
        
        if results['type'] == 'sales':
            self.display_sales_results(results)
        elif results['type'] == 'customers':
            self.display_customers_results(results)
        elif results['type'] == 'products':
            self.display_products_results(results)
        elif results['type'] == 'financial':
            self.display_financial_results(results)
    
    def display_sales_results(self, results):
        """عرض نتائج تحليل المبيعات"""
        # مؤشرات رئيسية
        metrics_group = QGroupBox("📊 المؤشرات الرئيسية")
        metrics_layout = QFormLayout()
        
        metrics_layout.addRow("إجمالي المبيعات:", QLabel(f"{results['total_sales']:,.2f} ج.م"))
        metrics_layout.addRow("عدد الفواتير:", QLabel(f"{results['sales_count']:,}"))
        metrics_layout.addRow("متوسط قيمة الفاتورة:", QLabel(f"{results['avg_invoice']:,.2f} ج.م"))
        
        metrics_group.setLayout(metrics_layout)
        self.results_layout.addWidget(metrics_group)
        
        # أفضل العملاء
        if results['top_customers']:
            customers_group = QGroupBox("🏆 أفضل العملاء")
            customers_layout = QVBoxLayout()
            
            table = QTableWidget(len(results['top_customers']), 2)
            table.setHorizontalHeaderLabels(["اسم العميل", "إجمالي المشتريات"])
            table.horizontalHeader().setStretchLastSection(True)
            
            for i, (name, total) in enumerate(results['top_customers']):
                table.setItem(i, 0, QTableWidgetItem(name))
                table.setItem(i, 1, QTableWidgetItem(f"{total:,.2f} ج.م"))
            
            customers_layout.addWidget(table)
            customers_group.setLayout(customers_layout)
            self.results_layout.addWidget(customers_group)
    
    def display_customers_results(self, results):
        """عرض نتائج تحليل العملاء"""
        metrics_group = QGroupBox("👥 إحصائيات العملاء")
        metrics_layout = QFormLayout()
        
        metrics_layout.addRow("إجمالي العملاء:", QLabel(f"{results['total_customers']:,}"))
        metrics_layout.addRow("العملاء النشطين:", QLabel(f"{results['active_customers']:,}"))
        metrics_layout.addRow("متوسط المشتريات لكل عميل:", QLabel(f"{results['avg_per_customer']:,.2f} ج.م"))
        
        activity_rate = (results['active_customers'] / results['total_customers'] * 100) if results['total_customers'] > 0 else 0
        metrics_layout.addRow("معدل النشاط:", QLabel(f"{activity_rate:.1f}%"))
        
        metrics_group.setLayout(metrics_layout)
        self.results_layout.addWidget(metrics_group)
    
    def display_products_results(self, results):
        """عرض نتائج تحليل المنتجات"""
        metrics_group = QGroupBox("📦 إحصائيات المنتجات")
        metrics_layout = QFormLayout()
        
        metrics_layout.addRow("إجمالي المنتجات:", QLabel(f"{results['total_products']:,}"))
        
        metrics_group.setLayout(metrics_layout)
        self.results_layout.addWidget(metrics_group)
        
        # أفضل المنتجات
        if results['top_products']:
            products_group = QGroupBox("🏆 أفضل المنتجات مبيعاً")
            products_layout = QVBoxLayout()
            
            table = QTableWidget(len(results['top_products']), 3)
            table.setHorizontalHeaderLabels(["اسم المنتج", "الكمية المباعة", "إجمالي الإيرادات"])
            table.horizontalHeader().setStretchLastSection(True)
            
            for i, (name, qty, revenue) in enumerate(results['top_products']):
                table.setItem(i, 0, QTableWidgetItem(name))
                table.setItem(i, 1, QTableWidgetItem(f"{qty:,}"))
                table.setItem(i, 2, QTableWidgetItem(f"{revenue:,.2f} ج.م"))
            
            products_layout.addWidget(table)
            products_group.setLayout(products_layout)
            self.results_layout.addWidget(products_group)
    
    def display_financial_results(self, results):
        """عرض نتائج التحليل المالي"""
        metrics_group = QGroupBox("💰 المؤشرات المالية")
        metrics_layout = QFormLayout()
        
        metrics_layout.addRow("إجمالي المبيعات:", QLabel(f"{results['total_sales']:,.2f} ج.م"))
        metrics_layout.addRow("إجمالي المشتريات:", QLabel(f"{results['total_purchases']:,.2f} ج.م"))
        metrics_layout.addRow("إجمالي المصروفات:", QLabel(f"{results['total_expenses']:,.2f} ج.م"))
        metrics_layout.addRow("صافي الربح:", QLabel(f"{results['net_profit']:,.2f} ج.م"))
        metrics_layout.addRow("هامش الربح:", QLabel(f"{results['profit_margin']:.2f}%"))
        
        metrics_group.setLayout(metrics_layout)
        self.results_layout.addWidget(metrics_group)
    
    def clear_results(self):
        """مسح النتائج السابقة"""
        while self.results_layout.count():
            child = self.results_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def export_results(self):
        """تصدير النتائج"""
        if not self.current_analysis:
            return
        
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "تصدير نتائج التحليل",
            f"تحليل_{self.current_analysis['type']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.current_analysis, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "نجح التصدير", f"تم تصدير النتائج إلى:\n{filename}")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء تصدير النتائج:\n{str(e)}")


# للتوافق مع الكود الموجود
AdvancedReportsWidget = SafeAdvancedReportsWidget
