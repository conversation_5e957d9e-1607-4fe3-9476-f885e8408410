from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum, create_engine, Index, Boolean
from sqlalchemy.orm import declarative_base, relationship
from datetime import datetime
import enum

Base = declarative_base()

class AuditMixin:
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_active = Column(Boolean, default=True)

class TransactionType(str, enum.Enum):
    PURCHASE = "شراء"
    SALE = "بيع"
    EXPENSE = "مصروف"
    INCOME = "إيراد"
    SALE_RETURN = "مرتجع_مبيعات"
    PURCHASE_RETURN = "مرتجع_مشتريات"

class ExpenseCategory(Base, AuditMixin):
    __tablename__ = 'expense_categories'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(String(500))
    
    transactions = relationship("Transaction", back_populates="expense_category")

class IncomeCategory(Base, AuditMixin):
    __tablename__ = 'income_categories'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(String(500))
    
    transactions = relationship("Transaction", back_populates="income_category")

class Customer(Base, AuditMixin):
    __tablename__ = 'customers'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20))
    address = Column(String(200))
    email = Column(String(100))
    tax_number = Column(String(50))
    credit_limit = Column(Float, default=0)
    balance = Column(Float, default=0)
    
    transactions = relationship("Transaction", back_populates="customer")
    
    __table_args__ = (
        Index('idx_customer_name', 'name'),
        Index('idx_customer_phone', 'phone'),
    )

class Supplier(Base, AuditMixin):
    __tablename__ = 'suppliers'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20))
    address = Column(String(200))
    email = Column(String(100))
    tax_number = Column(String(50))
    balance = Column(Float, default=0)
    
    transactions = relationship("Transaction", back_populates="supplier")
    
    __table_args__ = (
        Index('idx_supplier_name', 'name'),
        Index('idx_supplier_phone', 'phone'),
    )

class Product(Base, AuditMixin):
    __tablename__ = 'products'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True)
    name = Column(String(100), nullable=False)
    description = Column(String(500))
    category = Column(String(100))
    purchase_price = Column(Float, nullable=False)
    sale_price = Column(Float, nullable=False)
    min_sale_price = Column(Float, default=0)  # الحد الأدنى للبيع
    min_quantity = Column(Integer, default=0)
    quantity = Column(Integer, default=0)
    unit = Column(String(20))
    barcode = Column(String(100))
    
    transaction_items = relationship("TransactionItem", back_populates="product")
    barcodes = relationship("ProductBarcode", back_populates="product", cascade="all, delete-orphan")

    __table_args__ = (
        Index('idx_product_code', 'code'),
        Index('idx_product_name', 'name'),
        Index('idx_product_category', 'category'),
    )

class ProductBarcode(Base, AuditMixin):
    """جدول الباركودات المتعددة للمنتج الواحد"""
    __tablename__ = 'product_barcodes'

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    barcode = Column(String(100), nullable=False, unique=True)
    description = Column(String(200))  # وصف الباركود (مثل: باركود المورد الأول، باركود الحجم الكبير، إلخ)
    is_primary = Column(Boolean, default=False)  # هل هو الباركود الأساسي

    product = relationship("Product", back_populates="barcodes")

    __table_args__ = (
        Index('idx_barcode', 'barcode'),
        Index('idx_product_barcode', 'product_id', 'barcode'),
    )

class Transaction(Base, AuditMixin):
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True)
    reference_number = Column(String(50), unique=True)
    type = Column(Enum(TransactionType), nullable=False)
    date = Column(DateTime, default=datetime.now)
    due_date = Column(DateTime)
    total_amount = Column(Float, nullable=False)
    paid_amount = Column(Float, default=0)
    discount = Column(Float, default=0)
    tax = Column(Float, default=0)
    notes = Column(String(500))
    status = Column(String(20), default='pending')
    customer_id = Column(Integer, ForeignKey('customers.id'))
    supplier_id = Column(Integer, ForeignKey('suppliers.id'))
    expense_category_id = Column(Integer, ForeignKey('expense_categories.id'))
    income_category_id = Column(Integer, ForeignKey('income_categories.id'))
    original_transaction_id = Column(Integer, ForeignKey('transactions.id'))  # للمرتجعات
    
    customer = relationship("Customer", back_populates="transactions")
    supplier = relationship("Supplier", back_populates="transactions")
    expense_category = relationship("ExpenseCategory", back_populates="transactions")
    income_category = relationship("IncomeCategory", back_populates="transactions")
    items = relationship("TransactionItem", back_populates="transaction")
    original_transaction = relationship("Transaction", remote_side=[id])  # للمرتجعات
    
    __table_args__ = (
        Index('idx_transaction_date', 'date'),
        Index('idx_transaction_type', 'type'),
        Index('idx_transaction_status', 'status'),
    )

class TransactionItem(Base):
    __tablename__ = 'transaction_items'
    
    id = Column(Integer, primary_key=True)
    transaction_id = Column(Integer, ForeignKey('transactions.id'))
    product_id = Column(Integer, ForeignKey('products.id'))
    quantity = Column(Integer, nullable=False)
    price = Column(Float, nullable=False)
    discount = Column(Float, default=0)
    tax = Column(Float, default=0)
    
    transaction = relationship("Transaction", back_populates="items")
    product = relationship("Product", back_populates="transaction_items")

def init_db():
    try:
        from main import resource_path
        db_path = resource_path('accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=True)
    except ImportError:
        engine = create_engine('sqlite:///accounting.db', echo=True)
    Base.metadata.create_all(engine)
    return engine

def clear_all_data(engine):
    """مسح جميع بيانات الجداول الأساسية مع الإبقاء على المستخدمين والأدوار فقط (للتسليم للعميل)"""
    from sqlalchemy.orm import Session
    with Session(engine) as session:
        session.query(TransactionItem).delete()
        session.query(Transaction).delete()
        session.query(ProductBarcode).delete()
        session.query(Product).delete()
        session.query(Customer).delete()
        session.query(Supplier).delete()
        # يمكن إضافة جداول أخرى إذا لزم الأمر
        session.commit()