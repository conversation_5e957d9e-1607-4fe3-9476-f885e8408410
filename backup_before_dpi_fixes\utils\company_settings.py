import json
import os
import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QLineEdit, QTextEdit, QFileDialog, QMessageBox,
                             QFrame, QGridLayout, QGroupBox, QCheckBox, QTabWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
import base64

class CompanySettings:
    """إدارة إعدادات الشركة"""
    
    def __init__(self):
        # تحديد المسار المطلق لملف الإعدادات في مجلد المشروع الرئيسي
        if getattr(sys, 'frozen', False):
            # إذا كان البرنامج مصدر (PyInstaller)
            base_dir = os.path.dirname(sys.executable)
        else:
            # إذا كان البرنامج يعمل من الكود المصدري
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        self.settings_file = os.path.join(base_dir, "company_settings.json")
        self.default_settings = {
            "company_name": "نظام إدارة المحاسبة",
            "company_name_english": "Accounting Management System",
            "address": "",
            "phone": "",
            "email": "",
            "website": "",
            "tax_number": "",
            "commercial_register": "",
            "logo_path": "",
            "logo_base64": ""
        }
        
    def load_settings(self):
        """تحميل إعدادات الشركة"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    # دمج الإعدادات الافتراضية مع المحفوظة
                    for key in self.default_settings:
                        if key not in settings:
                            settings[key] = self.default_settings[key]
                    return settings
            else:
                return self.default_settings.copy()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الشركة: {e}")
            return self.default_settings.copy()
    
    def save_settings(self, settings):
        """حفظ إعدادات الشركة"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الشركة: {e}")
            return False
    
    def get_logo_base64(self, image_path):
        """تحويل الصورة إلى base64"""
        try:
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                    return base64.b64encode(image_data).decode('utf-8')
            return ""
        except Exception as e:
            print(f"خطأ في تحويل الصورة: {e}")
            return ""


class CompanySettingsDialog(QDialog):
    """نافذة إعدادات الشركة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.company_settings = CompanySettings()
        self.current_settings = self.company_settings.load_settings()
        self.logo_pixmap = None
        
        self.setWindowTitle("إعدادات الشركة")
        # تكبير النافذة بالنسبة للشاشة
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen().availableGeometry()
        width = int(screen.width() * 0.5)
        height = int(screen.height() * 0.9)
        self.setMinimumSize(width, height)
        self.resize(width, height)
        self.setModal(True)
        
        # تحسين النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                border-radius: 10px;
            }
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #3498DB;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #F8F9FA;
                font-size: 18px;
                font-weight: bold;
            }
            QLabel {
                font-size: 14px;
                color: #2C3E50;
                font-weight: bold;
            }
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
                font-weight: bold;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #3498DB;
                background-color: #F8F9FA;
            }
            QPushButton {
                padding: 10px 15px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
                min-width: 100px;
            }
        """)
        
        self.setup_ui()
        self.load_current_settings()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        self.setLayout(layout)
        
        # عنوان النافذة
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498DB, stop:1 #2980B9);
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        title_layout = QHBoxLayout()
        title_frame.setLayout(title_layout)
        
        title_icon = QLabel("🏢")
        title_icon.setStyleSheet("font-size: 24px; color: white;")
        
        title_label = QLabel("إعدادات الشركة")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-left: 10px;
        """)
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addWidget(title_frame)

        # إزالة التبويبات - العودة للتصميم الأصلي

        # معلومات الشركة الأساسية
        basic_group = QGroupBox("📋 المعلومات الأساسية")
        basic_layout = QGridLayout()
        basic_group.setLayout(basic_layout)
        
        # اسم الشركة بالعربية
        basic_layout.addWidget(QLabel("اسم الشركة (عربي):"), 0, 0)
        self.company_name_ar = QLineEdit()
        self.company_name_ar.setPlaceholderText("أدخل اسم الشركة بالعربية...")
        basic_layout.addWidget(self.company_name_ar, 0, 1)
        
        # اسم الشركة بالإنجليزية
        basic_layout.addWidget(QLabel("اسم الشركة (إنجليزي):"), 1, 0)
        self.company_name_en = QLineEdit()
        self.company_name_en.setPlaceholderText("Enter company name in English...")
        basic_layout.addWidget(self.company_name_en, 1, 1)
        
        # العنوان
        basic_layout.addWidget(QLabel("العنوان:"), 2, 0)
        self.address = QTextEdit()
        self.address.setPlaceholderText("أدخل عنوان الشركة...")
        self.address.setMaximumHeight(60)
        basic_layout.addWidget(self.address, 2, 1)

        layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = QGroupBox("📞 معلومات الاتصال")
        contact_layout = QGridLayout()
        contact_group.setLayout(contact_layout)
        
        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 0, 0)
        self.phone = QLineEdit()
        self.phone.setPlaceholderText("أدخل رقم الهاتف...")
        contact_layout.addWidget(self.phone, 0, 1)
        
        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 1, 0)
        self.email = QLineEdit()
        self.email.setPlaceholderText("أدخل البريد الإلكتروني...")
        contact_layout.addWidget(self.email, 1, 1)
        
        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 2, 0)
        self.website = QLineEdit()
        self.website.setPlaceholderText("أدخل الموقع الإلكتروني...")
        contact_layout.addWidget(self.website, 2, 1)
        
        layout.addWidget(contact_group)

        # المعلومات القانونية
        legal_group = QGroupBox("📄 المعلومات القانونية")
        legal_layout = QGridLayout()
        legal_group.setLayout(legal_layout)

        # الرقم الضريبي
        legal_layout.addWidget(QLabel("الرقم الضريبي:"), 0, 0)
        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("أدخل الرقم الضريبي...")
        legal_layout.addWidget(self.tax_number, 0, 1)

        # السجل التجاري
        legal_layout.addWidget(QLabel("السجل التجاري:"), 1, 0)
        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("أدخل رقم السجل التجاري...")
        legal_layout.addWidget(self.commercial_register, 1, 1)

        layout.addWidget(legal_group)
        
        # شعار الشركة
        logo_group = QGroupBox("🖼️ شعار الشركة")
        logo_layout = QVBoxLayout()
        logo_group.setLayout(logo_layout)
        
        # عرض الشعار الحالي
        self.logo_display = QLabel("لا يوجد شعار")
        self.logo_display.setAlignment(Qt.AlignCenter)
        self.logo_display.setStyleSheet("""
            QLabel {
                border: 2px dashed #BDC3C7;
                border-radius: 8px;
                padding: 20px;
                background-color: white;
                min-height: 100px;
                color: #7F8C8D;
            }
        """)
        logo_layout.addWidget(self.logo_display)
        
        # أزرار إدارة الشعار
        logo_buttons_layout = QHBoxLayout()
        
        select_logo_btn = QPushButton("📁 اختيار شعار")
        select_logo_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        select_logo_btn.clicked.connect(self.select_logo)
        
        remove_logo_btn = QPushButton("🗑️ إزالة الشعار")
        remove_logo_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        remove_logo_btn.clicked.connect(self.remove_logo)
        
        logo_buttons_layout.addWidget(select_logo_btn)
        logo_buttons_layout.addWidget(remove_logo_btn)
        logo_buttons_layout.addStretch()
        
        logo_layout.addLayout(logo_buttons_layout)
        layout.addWidget(logo_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ الإعدادات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 14px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_settings)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                font-size: 14px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)



    def load_current_settings(self):
        """تحميل الإعدادات الحالية في النموذج"""
        self.company_name_ar.setText(self.current_settings.get("company_name", ""))
        self.company_name_en.setText(self.current_settings.get("company_name_english", ""))
        self.address.setPlainText(self.current_settings.get("address", ""))
        self.phone.setText(self.current_settings.get("phone", ""))
        self.email.setText(self.current_settings.get("email", ""))
        self.website.setText(self.current_settings.get("website", ""))
        self.tax_number.setText(self.current_settings.get("tax_number", ""))
        self.commercial_register.setText(self.current_settings.get("commercial_register", ""))
        
        # تحميل الشعار
        logo_path = self.current_settings.get("logo_path", "")
        if logo_path and os.path.exists(logo_path):
            self.load_logo_preview(logo_path)
        elif self.current_settings.get("logo_base64", ""):
            self.load_logo_from_base64(self.current_settings["logo_base64"])
    
    def select_logo(self):
        """اختيار شعار جديد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار شعار الشركة",
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif);;All Files (*)"
        )
        
        if file_path:
            self.load_logo_preview(file_path)
            self.current_settings["logo_path"] = file_path
            # تحويل الصورة إلى base64 للحفظ
            self.current_settings["logo_base64"] = self.company_settings.get_logo_base64(file_path)
    
    def load_logo_preview(self, image_path):
        """تحميل معاينة الشعار"""
        try:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # تغيير حجم الصورة للمعاينة
                scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.logo_display.setPixmap(scaled_pixmap)
                self.logo_pixmap = pixmap
                self.logo_display.setText("")
            else:
                QMessageBox.warning(self, "خطأ", "لا يمكن تحميل الصورة المحددة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الصورة:\n{str(e)}")
    
    def load_logo_from_base64(self, base64_data):
        """تحميل الشعار من base64"""
        try:
            image_data = base64.b64decode(base64_data)
            pixmap = QPixmap()
            pixmap.loadFromData(image_data)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.logo_display.setPixmap(scaled_pixmap)
                self.logo_pixmap = pixmap
                self.logo_display.setText("")
        except Exception as e:
            print(f"خطأ في تحميل الشعار من base64: {e}")
    
    def remove_logo(self):
        """إزالة الشعار"""
        self.logo_display.clear()
        self.logo_display.setText("لا يوجد شعار")
        self.logo_pixmap = None
        self.current_settings["logo_path"] = ""
        self.current_settings["logo_base64"] = ""
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تحديث الإعدادات
            self.current_settings["company_name"] = self.company_name_ar.text().strip()
            self.current_settings["company_name_english"] = self.company_name_en.text().strip()
            self.current_settings["address"] = self.address.toPlainText().strip()
            self.current_settings["phone"] = self.phone.text().strip()
            self.current_settings["email"] = self.email.text().strip()
            self.current_settings["website"] = self.website.text().strip()
            self.current_settings["tax_number"] = self.tax_number.text().strip()
            self.current_settings["commercial_register"] = self.commercial_register.text().strip()



            # حفظ الإعدادات
            if self.company_settings.save_settings(self.current_settings):
                QMessageBox.information(self, "نجح الحفظ", "تم حفظ إعدادات الشركة بنجاح!")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ الإعدادات")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")




# دالة مساعدة للوصول السريع للإعدادات
def get_company_settings():
    """الحصول على إعدادات الشركة"""
    settings = CompanySettings()
    return settings.load_settings()

def show_company_settings_dialog(parent=None):
    """عرض نافذة إعدادات الشركة"""
    dialog = CompanySettingsDialog(parent)
    return dialog.exec_()
