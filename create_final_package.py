#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import zipfile
from datetime import datetime

def create_final_package():
    """إنشاء حزمة نهائية كاملة للبرنامج"""
    
    # إنشاء مجلد الحزمة النهائية
    package_name = f"نظام_المحاسبة_النهائي_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    package_dir = os.path.join("final_package", package_name)
    
    if os.path.exists("final_package"):
        shutil.rmtree("final_package")
    
    os.makedirs(package_dir, exist_ok=True)
    
    print(f"إنشاء الحزمة النهائية: {package_name}")
    
    # نسخ ملفات البرنامج المصدر
    dist_source = os.path.join("dist", "نظام_المحاسبة")
    if os.path.exists(dist_source):
        print("نسخ ملفات البرنامج...")
        shutil.copytree(dist_source, os.path.join(package_dir, "نظام_المحاسبة"))
    
    # نسخ الملفات الأساسية
    essential_files = [
        "accounting.db",
        "company_settings.json", 
        "user_settings.json",
        "theme_settings.json",
        "modern_theme_settings.json",
        "brand_settings.json"
    ]
    
    print("نسخ الملفات الأساسية...")
    for file in essential_files:
        if os.path.exists(file):
            shutil.copy2(file, package_dir)
    
    # نسخ المجلدات المهمة
    important_dirs = [
        "assets",
        "sample_data", 
        "fonts"
    ]
    
    print("نسخ المجلدات المهمة...")
    for dir_name in important_dirs:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, os.path.join(package_dir, dir_name))
    
    # إنشاء ملف تشغيل سريع
    run_script = """@echo off
cd /d "%~dp0"
start "" "نظام_المحاسبة\\نظام_المحاسبة.exe"
"""

    with open(os.path.join(package_dir, "تشغيل_البرنامج.bat"), "w", encoding="utf-8") as f:
        f.write(run_script)

    # إنشاء سكريبت تثبيت
    install_script = """@echo off
echo ========================================
echo       نظام المحاسبة - التثبيت
echo ========================================
echo.

set "INSTALL_DIR=C:\\نظام_المحاسبة"

echo جاري إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo جاري نسخ الملفات...
xcopy /E /I /Y "*" "%INSTALL_DIR%\\"

echo جاري إنشاء اختصار على سطح المكتب...
set "DESKTOP=%USERPROFILE%\\Desktop"
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\\نظام المحاسبة.lnk" >> "%TEMP%\\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\\CreateShortcut.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\\نظام_المحاسبة\\نظام_المحاسبة.exe" >> "%TEMP%\\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\\CreateShortcut.vbs"
echo oLink.IconLocation = "%INSTALL_DIR%\\assets\\icons.ico" >> "%TEMP%\\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\\CreateShortcut.vbs"
cscript /nologo "%TEMP%\\CreateShortcut.vbs"
del "%TEMP%\\CreateShortcut.vbs"

echo.
echo ========================================
echo تم التثبيت بنجاح!
echo مسار التثبيت: %INSTALL_DIR%
echo تم إنشاء اختصار على سطح المكتب
echo ========================================
echo.
pause
"""

    with open(os.path.join(package_dir, "تثبيت_البرنامج.bat"), "w", encoding="utf-8") as f:
        f.write(install_script)
    
    # إنشاء ملف README
    readme_content = """# نظام المحاسبة النهائي

## طريقة التشغيل:
1. انقر مرتين على ملف "تشغيل_البرنامج.bat"
2. أو انتقل إلى مجلد "نظام_المحاسبة" وانقر مرتين على "نظام_المحاسبة.exe"

## الملفات المتضمنة:
- نظام_المحاسبة.exe: الملف التنفيذي الرئيسي
- accounting.db: قاعدة البيانات
- ملفات الإعدادات والثيمات
- الأصول والخطوط
- بيانات تجريبية

## المتطلبات:
- نظام تشغيل Windows 10 أو أحدث
- لا يتطلب تثبيت Python أو أي مكتبات إضافية

## الدعم الفني:
للحصول على الدعم الفني، يرجى التواصل مع المطور.

تاريخ الإنشاء: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """
"""
    
    with open(os.path.join(package_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # إنشاء ملف ZIP للحزمة
    zip_filename = f"{package_name}.zip"
    print(f"إنشاء ملف ZIP: {zip_filename}")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, "final_package")
                zipf.write(file_path, arc_name)
    
    print(f"\n✅ تم إنشاء الحزمة النهائية بنجاح!")
    print(f"📁 المجلد: {package_dir}")
    print(f"📦 ملف ZIP: {zip_filename}")
    print(f"📊 حجم الحزمة: {os.path.getsize(zip_filename) / (1024*1024):.1f} MB")
    
    return package_dir, zip_filename

if __name__ == "__main__":
    create_final_package()
