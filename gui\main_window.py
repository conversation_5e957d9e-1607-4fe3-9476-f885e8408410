from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QWidget, Q<PERSON>ox<PERSON><PERSON>out, QH<PERSON><PERSON>Layout,
                         QPushButton, QLabel, QFrame, QGridLayout, QApplication,
                         QAction, QDialog, QMessageBox, QTabWidget, QShortcut)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QIcon, QFont, QKeySequence
import sys
import os

# استيراد نظام التراخيص
try:
    from license_manager import LicenseManager
    LICENSE_SYSTEM_AVAILABLE = True
except ImportError:
    LICENSE_SYSTEM_AVAILABLE = False
from .sales import SalesWidget
from .purchases import PurchasesWidget
from .inventory import InventoryWidget
from .contacts import ContactsWidget
# from .reports import ReportsWidget  # يسبب Segmentation fault - تم استبداله
# from .finance import FinanceWidget  # تم تعطيله مؤقتاً
from .accounting import AccountingWidget
from .backup import BackupDialog
# from .dashboard import DashboardWidget  # يسبب Segmentation fault - تم استبداله
from .enhanced_reports import EnhancedReportsWidget
from .enhanced_dashboard import EnhancedDashboardWidget
from .notifications import NotificationSystem
from utils.theme_manager import theme_manager
# from .advanced_reports import AdvancedAnalyticsWidget  # تم تعطيله مؤقتاً لتجنب مشاكل الذاكرة
from .login import LoginDialog
from .audit_log import AuditLogDialog
from .invoices_view import InvoicesViewWidget
from database.audit import AuditSystem, audit_action
from utils.company_settings import show_company_settings_dialog
from main import resource_path
from .purchase_invoices_view import PurchaseInvoicesViewWidget
from .activation_dialog import ActivationDialog
from database.users import User

# استيراد النظام المتجاوب
try:
    from utils.responsive_ui import responsive_manager
    RESPONSIVE_UI_AVAILABLE = True
except ImportError:
    RESPONSIVE_UI_AVAILABLE = False
    print("⚠️ النظام المتجاوب غير متوفر - سيتم استخدام أحجام ثابتة")

class MainWindow(QMainWindow):
    def __init__(self, engine=None, user=None):
        super().__init__()
        self.engine = engine
        self.current_user = user

        # تحميل تفضيل الثيم المحفوظ
        theme_manager.load_theme_preference()
        self.is_dark_mode = theme_manager.is_dark_mode()

        # تعيين أيقونة البرنامج
        try:
            from main import resource_path
            icon_path = resource_path(os.path.join('assets', 'icons.ico'))
            self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"Error setting window icon: {e}")

        # إذا لم يتم تمرير مستخدم، عرض نافذة تسجيل الدخول
        if not self.current_user and not self.show_login():
            sys.exit()

        # إضافة نظام التدقيق
        from database.audit import AuditSystem
        from .notifications import NotificationSystem
        self.audit_system = AuditSystem(self.engine)

        # تحميل إعدادات الشركة
        self.load_company_settings()

        # تحديث عنوان النافذة
        self.refresh_company_info()

        self.setWindowIcon(QIcon(resource_path(os.path.join('assets', 'icons.ico'))))

        # إعداد النافذة المتجاوبة
        self.setup_responsive_window()

        self.setLayoutDirection(Qt.RightToLeft)

        # إعداد نظام التنبيهات
        if self.engine:
            self.notification_system = NotificationSystem(self.engine)

        # إعدادات البار الجانبي
        self.sidebar_enabled = self.load_sidebar_setting()
        self.sidebar_visible = False
        self.sidebar_expanded = False

        self.setup_menu()
        self.setup_sidebar_shortcut()  # إضافة اختصار البار الجانبي
        self.show_dashboard()

        # تطبيق التنسيق المتجاوب (بدون تغيير الألوان)
        if RESPONSIVE_UI_AVAILABLE:
            self.apply_responsive_sizing_only()

        # إعداد فحص دوري للترخيص
        if LICENSE_SYSTEM_AVAILABLE:
            self.setup_license_timer()

    def load_sidebar_setting(self):
        """تحميل إعداد البار الجانبي من قاعدة البيانات"""
        try:
            from utils.settings_manager import get_settings_manager
            settings_manager = get_settings_manager()
            if settings_manager:
                return settings_manager.get('sidebar_enabled', False)  # افتراضياً مطفي
            return False
        except Exception as e:
            print(f"خطأ في تحميل إعدادات البار الجانبي: {e}")
            return False

    def save_sidebar_setting(self, enabled):
        """حفظ إعداد البار الجانبي في قاعدة البيانات"""
        try:
            from utils.settings_manager import get_settings_manager
            settings_manager = get_settings_manager()
            if settings_manager:
                settings_manager.set('sidebar_enabled', enabled)

            self.sidebar_enabled = enabled
            print(f"✅ تم حفظ إعداد البار الجانبي: {'مفعل' if enabled else 'مطفي'}")

        except Exception as e:
            print(f"خطأ في حفظ إعدادات البار الجانبي: {e}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ في حفظ إعدادات البار الجانبي:\n{str(e)}"
            )

    def setup_sidebar_shortcut(self):
        """إعداد اختصار لوحة المفاتيح للبار الجانبي"""
        self.sidebar_shortcut = QShortcut(QKeySequence("Ctrl+B"), self)
        self.sidebar_shortcut.activated.connect(self.toggle_sidebar_visibility)
        print("🔧 تم إعداد اختصار البار الجانبي: Ctrl+B")

    def toggle_sidebar_visibility(self):
        """تبديل ظهور/إخفاء البار الجانبي"""
        if not self.sidebar_enabled:
            QMessageBox.information(
                self,
                "البار الجانبي مطفي",
                "البار الجانبي مطفي حالياً.\n\n"
                "يمكنك تفعيله من:\n"
                "⚙️ إعدادات النظام ← 🎨 اللغة والمظهر ← 📋 البار الجانبي\n\n"
                "أو استخدم الاختصار: Ctrl+B"
            )
            return

        # تبديل البار الجانبي الرئيسي
        if hasattr(self, 'sidebar'):
            if self.sidebar_visible:
                self.hide_sidebar()
            else:
                self.show_sidebar()
        # تبديل البار الجانبي المخفي
        elif hasattr(self, 'current_hidden_sidebar'):
            if self.current_hidden_sidebar.isVisible():
                self.hide_all_sidebars()
            else:
                self.show_all_sidebars()
        else:
            QMessageBox.information(
                self,
                "البار الجانبي",
                "البار الجانبي متاح في جميع صفحات النظام!\n\n"
                "🎯 يمكنك تفعيله من إعدادات النظام"
            )

    def show_sidebar(self):
        """إظهار البار الجانبي"""
        if hasattr(self, 'sidebar') and self.sidebar_enabled:
            self.sidebar.show()
            self.sidebar_visible = True
            print("👁️ تم إظهار البار الجانبي")

    def hide_sidebar(self):
        """إخفاء البار الجانبي"""
        if hasattr(self, 'sidebar'):
            self.sidebar.hide()
            self.sidebar_visible = False
            self.sidebar_expanded = False
            print("🙈 تم إخفاء البار الجانبي")

    def show_all_sidebars(self):
        """إظهار جميع البارات الجانبية"""
        if hasattr(self, 'sidebar') and self.sidebar_enabled:
            self.show_sidebar()
        if hasattr(self, 'current_hidden_sidebar') and self.sidebar_enabled:
            self.current_hidden_sidebar.show()
            self.current_hidden_sidebar.setFixedWidth(5)
        # إظهار البارات في جميع التبويبات
        self.show_all_hidden_sidebars()
        print("👁️ تم إظهار جميع البارات الجانبية")

    def hide_all_sidebars(self):
        """إخفاء جميع البارات الجانبية"""
        if hasattr(self, 'sidebar'):
            self.hide_sidebar()
        if hasattr(self, 'current_hidden_sidebar'):
            self.current_hidden_sidebar.hide()
        # إخفاء البارات في جميع التبويبات
        self.hide_all_hidden_sidebars()
        print("🙈 تم إخفاء جميع البارات الجانبية")

    def show_all_hidden_sidebars(self):
        """إظهار جميع البارات الجانبية المخفية"""
        if hasattr(self, 'main_tabs') and self.sidebar_enabled:
            for i in range(self.main_tabs.count()):
                tab_widget = self.main_tabs.widget(i)
                if tab_widget:
                    hidden_sidebars = tab_widget.findChildren(QFrame, "hiddenSidebar")
                    for sidebar in hidden_sidebars:
                        sidebar.show()
                        sidebar.setFixedWidth(5)

    def hide_all_hidden_sidebars(self):
        """إخفاء جميع البارات الجانبية المخفية"""
        if hasattr(self, 'main_tabs'):
            for i in range(self.main_tabs.count()):
                tab_widget = self.main_tabs.widget(i)
                if tab_widget:
                    hidden_sidebars = tab_widget.findChildren(QFrame, "hiddenSidebar")
                    for sidebar in hidden_sidebars:
                        sidebar.hide()

    def update_sidebar_visibility(self):
        """تحديث ظهور البار الجانبي حسب الإعدادات"""
        # تحديث البار الجانبي الرئيسي
        if hasattr(self, 'sidebar'):
            if self.sidebar_enabled:
                self.sidebar.show()
                self.sidebar_visible = True
                # إعادة تفعيل أحداث التمرير
                self.sidebar.enterEvent = self.on_sidebar_enter
                self.sidebar.leaveEvent = self.on_sidebar_leave
            else:
                self.sidebar.hide()
                self.sidebar_visible = False
                self.sidebar_expanded = False
                # إلغاء أحداث التمرير
                self.sidebar.enterEvent = lambda event: None
                self.sidebar.leaveEvent = lambda event: None

        # تحديث البار الجانبي المخفي (للصفحات الأخرى)
        if hasattr(self, 'current_hidden_sidebar'):
            self.update_hidden_sidebar_visibility()

        # تحديث جميع البارات الجانبية في التبويبات المفتوحة
        self.update_all_hidden_sidebars()

    def update_hidden_sidebar_visibility(self):
        """تحديث ظهور البار الجانبي المخفي"""
        if hasattr(self, 'current_hidden_sidebar'):
            hidden_sidebar = self.current_hidden_sidebar

            if self.sidebar_enabled:
                hidden_sidebar.show()
                hidden_sidebar.setFixedWidth(5)

                # إعادة تفعيل أحداث التمرير
                def on_hidden_enter(event):
                    hidden_sidebar.setFixedWidth(300)

                def on_hidden_leave(event):
                    hidden_sidebar.setFixedWidth(5)

                hidden_sidebar.enterEvent = on_hidden_enter
                hidden_sidebar.leaveEvent = on_hidden_leave
            else:
                hidden_sidebar.hide()
                hidden_sidebar.setFixedWidth(0)
                # إلغاء أحداث التمرير
                hidden_sidebar.enterEvent = lambda event: None
                hidden_sidebar.leaveEvent = lambda event: None

    def update_all_hidden_sidebars(self):
        """تحديث جميع البارات الجانبية المخفية في التبويبات"""
        if hasattr(self, 'main_tabs'):
            # تحديث البارات في جميع التبويبات
            for i in range(self.main_tabs.count()):
                tab_widget = self.main_tabs.widget(i)
                if tab_widget and tab_widget.layout():
                    self.update_sidebars_in_widget(tab_widget)

    def update_sidebars_in_widget(self, widget):
        """تحديث البارات الجانبية في widget معين"""
        try:
            # البحث عن البارات الجانبية المخفية في الـ widget
            hidden_sidebars = widget.findChildren(QFrame, "hiddenSidebar")

            for hidden_sidebar in hidden_sidebars:
                if self.sidebar_enabled:
                    hidden_sidebar.show()
                    hidden_sidebar.setFixedWidth(5)

                    # إعادة تفعيل أحداث التمرير
                    def on_enter(event, sidebar=hidden_sidebar):
                        sidebar.setFixedWidth(300)

                    def on_leave(event, sidebar=hidden_sidebar):
                        sidebar.setFixedWidth(5)

                    hidden_sidebar.enterEvent = on_enter
                    hidden_sidebar.leaveEvent = on_leave
                else:
                    hidden_sidebar.hide()
                    hidden_sidebar.setFixedWidth(0)
                    # إلغاء أحداث التمرير
                    hidden_sidebar.enterEvent = lambda event: None
                    hidden_sidebar.leaveEvent = lambda event: None

        except Exception as e:
            print(f"خطأ في تحديث البارات الجانبية: {e}")

    def toggle_dark_mode(self):
        """تبديل الوضع الليلي/العادي - معطل (دائماً الوضع الفاتح)"""
        # لا نفعل شيء - نبقى في الوضع الفاتح
        pass

        # تحديث نص الزر في القائمة
        self.setup_menu()

    def update_all_themes(self):
        """تحديث الألوان في جميع الصفحات"""
        # تطبيق الثيم على النافذة الرئيسية
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث الشريط الجانبي
        if hasattr(self, 'sidebar'):
            self.sidebar.setStyleSheet(theme_manager.get_stylesheet("sidebar"))

        # تحديث الصفحة الحالية
        current_widget = self.centralWidget()
        if hasattr(current_widget, 'update_theme'):
            current_widget.update_theme()
        else:
            # إعادة تحميل الصفحة الحالية لتطبيق الألوان الجديدة
            self.show_dashboard()

    @audit_action("تسجيل دخول")
    def show_login(self):
        """عرض نافذة تسجيل الدخول مع تسجيل العملية"""
        if self.engine:
            login_dialog = LoginDialog(self.engine)
            if login_dialog.exec_() == QDialog.Accepted:
                self.current_user = login_dialog.user
                return True
        return False

    def check_permission(self, permission_name):
        """التحقق من صلاحيات المستخدم"""
        if not self.current_user:
            return False
            
        for role in self.current_user.roles:
            for permission in role.permissions:
                if permission.name == permission_name:
                    return True
        return False



    def setup_menu(self):
        # تحديث تصميم شريط القوائم
        menubar = self.menuBar()
        menubar.setLayoutDirection(Qt.RightToLeft)
        # إزالة الأنماط المحلية لاستخدام الأنماط العامة من ملف CSS

        file_menu = menubar.addMenu("ملف")

        # أداة استيراد البيانات من EasAcc
        import_easacc_action = QAction("📥 استيراد من برنامج EasAcc", self)
        import_easacc_action.triggered.connect(self.show_easacc_importer)

        backup_action = QAction(QIcon("images/backup.png"), "النسخ الاحتياطي", self)
        backup_action.triggered.connect(self.show_backup_dialog)

        # إضافة إعدادات الشركة
        company_settings_action = QAction("🏢 إعدادات الشركة", self)
        company_settings_action.triggered.connect(self.show_company_settings)

        exit_action = QAction(QIcon("images/exit.png"), "خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addActions([import_easacc_action, backup_action, company_settings_action, exit_action])

        # قائمة المبيعات
        if self.check_permission("إدارة_المبيعات"):
            sales_menu = menubar.addMenu("المبيعات")
            new_sale = QAction("فاتورة مبيعات جديدة", self)
            new_sale.triggered.connect(self.show_sales)
            sales_menu.addAction(new_sale)

            view_invoices = QAction("عرض الفواتير المحفوظة", self)
            view_invoices.triggered.connect(self.show_invoices_view)
            sales_menu.addAction(view_invoices)

            sales_menu.addSeparator()

            sales_return = QAction("🔄 مرتجع المبيعات", self)
            sales_return.triggered.connect(self.show_sales_returns)
            sales_menu.addAction(sales_return)

            view_returns = QAction("📋 عرض فواتير المرتجعات", self)
            view_returns.triggered.connect(self.show_returns_view)
            sales_menu.addAction(view_returns)

        # قائمة المشتريات
        if self.check_permission("إدارة_المشتريات"):
            purchases_menu = menubar.addMenu("المشتريات")
            new_purchase = QAction("فاتورة مشتريات جديدة", self)
            new_purchase.triggered.connect(self.show_purchases)
            purchases_menu.addAction(new_purchase)

            view_purchase_invoices = QAction("عرض فواتير المشتريات", self)
            view_purchase_invoices.triggered.connect(self.show_purchase_invoices_view)
            purchases_menu.addAction(view_purchase_invoices)

            purchases_menu.addSeparator()

            purchase_return = QAction("🔄 مرتجع المشتريات", self)
            purchase_return.triggered.connect(self.show_purchase_returns)
            purchases_menu.addAction(purchase_return)

        # قائمة العملاء والموردين
        if self.check_permission("إدارة_العملاء"):
            contacts_menu = menubar.addMenu("العملاء والموردين")
            customers = QAction("كشف حساب عميل", self)
            customers.triggered.connect(self.show_customer_statement)
            suppliers = QAction("كشف حساب مورد", self)
            suppliers.triggered.connect(self.show_supplier_statement)
            contacts_menu.addActions([customers, suppliers])

        # قائمة المخزون
        if self.check_permission("إدارة_المخزون"):
            inventory_menu = menubar.addMenu("المخزون")
            stock_action = QAction("جرد المخزون", self)
            stock_action.triggered.connect(self.show_inventory)
            inventory_menu.addAction(stock_action)

        # قائمة الحسابات
        accounting_menu = menubar.addMenu("الحسابات")
        income_statement = QAction("قائمة الدخل", self)
        balance_sheet = QAction("الميزانية العمومية", self)
        general_ledger = QAction("دفتر الأستاذ العام", self)
        income_statement.triggered.connect(lambda: self.show_financial_report("قائمة الدخل"))
        balance_sheet.triggered.connect(lambda: self.show_financial_report("الميزانية العمومية"))
        general_ledger.triggered.connect(lambda: self.show_financial_report("دفتر الأستاذ العام"))
        accounting_menu.addActions([income_statement, balance_sheet, general_ledger])
        
        # إضافة قائمة التقارير المتقدمة
        reports_menu = menubar.addMenu("التقارير المتقدمة")
        advanced_reports = QAction("التقارير التحليلية", self)
        advanced_reports.triggered.connect(self.show_advanced_reports)
        reports_menu.addAction(advanced_reports)
        
        # إضافة قائمة التنبيهات
        notifications_menu = menubar.addMenu("التنبيهات")
        show_notifications = QAction("عرض التنبيهات", self)
        show_notifications.triggered.connect(self.show_notifications_window)
        notifications_settings = QAction("إعدادات التنبيهات", self)
        notifications_settings.triggered.connect(self.show_notifications_settings)
        notifications_menu.addActions([show_notifications, notifications_settings])

        # قائمة النظام
        system_menu = menubar.addMenu("النظام")

        # إضافة إعدادات التطبيق
        settings_action = QAction("⚙️ إعدادات التطبيق", self)
        settings_action.triggered.connect(self.show_app_settings)
        system_menu.addAction(settings_action)

        system_menu.addSeparator()

        if self.check_permission("إدارة_المستخدمين"):
            users_action = QAction("إدارة المستخدمين", self)
            users_action.triggered.connect(self.show_user_management)
            system_menu.addAction(users_action)

        if self.check_permission("النسخ_الاحتياطي"):
            backup_action = QAction("النسخ الاحتياطي", self)
            backup_action.triggered.connect(self.show_backup_dialog)
            system_menu.addAction(backup_action)

        # إضافة خيار سجل العمليات في قائمة النظام
        audit_log_action = QAction("سجل العمليات", self)
        audit_log_action.triggered.connect(self.show_audit_log)
        system_menu.addAction(audit_log_action)

        system_menu.addSeparator()

        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        system_menu.addAction(logout_action)

        # إضافة خيار التفعيل
        activate_action = QAction("🔑 تفعيل البرنامج", self)
        activate_action.triggered.connect(self.show_activation_dialog)
        system_menu.addAction(activate_action)

        # إضافة واجهة الويب
        web_interface_action = QAction("🌐 واجهة الويب", self)
        web_interface_action.triggered.connect(self.open_web_interface)
        system_menu.addAction(web_interface_action)

        # إضافة التنبيهات الذكية
        notifications_action = QAction("🔔 التنبيهات الذكية", self)
        notifications_action.triggered.connect(self.show_smart_notifications)
        system_menu.addAction(notifications_action)

        # إضافة النسخ الاحتياطي المحسن
        enhanced_backup_action = QAction("💾 النسخ الاحتياطي المحسن", self)
        enhanced_backup_action.triggered.connect(self.show_enhanced_backup)
        system_menu.addAction(enhanced_backup_action)

        # إضافة التحليلات الذكية
        smart_analytics_action = QAction("🧠 التحليلات الذكية", self)
        smart_analytics_action.triggered.connect(self.show_smart_analytics)
        system_menu.addAction(smart_analytics_action)

    def show_activation_dialog(self):
        """عرض نافذة تفعيل البرنامج"""
        dialog = ActivationDialog(self)
        dialog.exec_()

    def open_web_interface(self):
        """فتح واجهة الويب"""
        try:
            # محاولة استخدام واجهة الويب المبسطة أولاً
            try:
                from web.simple_web_interface import SimpleWebInterface

                # إنشاء واجهة الويب المبسطة
                if not hasattr(self, 'simple_web_interface'):
                    self.simple_web_interface = SimpleWebInterface(self.engine, port=8080)

                # بدء الخادم في الخلفية
                success = self.simple_web_interface.start_in_background()

                if success:
                    QMessageBox.information(
                        self,
                        "واجهة الويب",
                        "تم بدء واجهة الويب المبسطة بنجاح! 🎉\n\n"
                        "يمكنك الوصول إليها عبر:\n"
                        "http://localhost:8080/dashboard.html\n\n"
                        "سيتم فتح المتصفح تلقائياً\n\n"
                        "✅ لا تحتاج Flask - تعمل بدون مكتبات إضافية"
                    )
                else:
                    raise Exception("فشل في بدء الخادم المبسط")

            except Exception as simple_error:
                print(f"⚠️ فشل في الواجهة المبسطة: {simple_error}")

                # fallback للواجهة الأصلية
                from web.web_interface import WebInterface, create_web_templates

                # إنشاء القوالب إذا لم تكن موجودة
                create_web_templates()

                # إنشاء واجهة الويب
                if not hasattr(self, 'web_interface'):
                    self.web_interface = WebInterface(self.engine, port=5000)

                # بدء الخادم
                success = self.web_interface.start_server(open_browser=True)

                if success:
                    QMessageBox.information(
                        self,
                        "واجهة الويب",
                        "تم بدء واجهة الويب الكاملة بنجاح!\n\n"
                        "يمكنك الوصول إليها عبر:\n"
                        "http://127.0.0.1:5000\n\n"
                        "سيتم فتح المتصفح تلقائياً"
                    )
                else:
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        "فشل في بدء واجهة الويب\n\n"
                        "تأكد من أن المنفذ 5000 غير مستخدم"
                    )

        except ImportError as e:
            QMessageBox.warning(
                self,
                "تحذير",
                f"واجهة الويب غير متوفرة\n\n"
                f"خطأ الاستيراد: {str(e)}\n\n"
                "لاستخدام الواجهة الكاملة، قم بتثبيت Flask:\n"
                "pip install flask"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح واجهة الويب:\n{str(e)}\n\n"
                f"نوع الخطأ: {type(e).__name__}"
            )

    def show_smart_notifications(self):
        """عرض نظام التنبيهات الذكية"""
        try:
            from utils.smart_notifications import SmartNotificationSystem

            # إنشاء نافذة التنبيهات إذا لم تكن موجودة
            if not hasattr(self, 'notifications_system'):
                self.notifications_system = SmartNotificationSystem(self.engine, self)

            # عرض النافذة
            self.notifications_system.show_notifications()

        except ImportError:
            QMessageBox.warning(
                self,
                "تحذير",
                "نظام التنبيهات الذكية غير متوفر\n\n"
                "تأكد من وجود جميع الملفات المطلوبة"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح التنبيهات الذكية:\n{str(e)}"
            )

    def show_enhanced_backup(self):
        """عرض نظام النسخ الاحتياطي المحسن"""
        try:
            from utils.enhanced_backup import EnhancedBackupWidget

            # التأكد من وجود التبويبات الثابتة
            if not hasattr(self, 'main_tabs'):
                self.setup_main_interface()

            # إنشاء تبويب جديد للنسخ الاحتياطي المحسن
            backup_widget = EnhancedBackupWidget(self)
            self.add_or_switch_tab("💾 النسخ الاحتياطي المحسن", backup_widget, "💾")

        except ImportError:
            QMessageBox.warning(
                self,
                "تحذير",
                "نظام النسخ الاحتياطي المحسن غير متوفر\n\n"
                "تأكد من وجود جميع الملفات المطلوبة"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح النسخ الاحتياطي المحسن:\n{str(e)}"
            )

    def show_smart_analytics(self):
        """عرض التحليلات الذكية"""
        try:
            # استخدام النسخة الآمنة أولاً
            try:
                from gui.safe_smart_analytics import SafeSmartAnalyticsWidget
                analytics_widget = SafeSmartAnalyticsWidget(self.engine, self)
                print("✅ تم تحميل النسخة الآمنة من التحليلات الذكية")
            except ImportError:
                # fallback للنسخة الأصلية
                from gui.smart_analytics_widget import SmartAnalyticsWidget
                analytics_widget = SmartAnalyticsWidget(self.engine, self)
                print("✅ تم تحميل النسخة الأصلية من التحليلات الذكية")

            # التأكد من وجود التبويبات الثابتة
            if not hasattr(self, 'main_tabs'):
                self.setup_main_interface()

            # إنشاء تبويب جديد للتحليلات الذكية
            self.add_or_switch_tab("🧠 التحليلات الذكية", analytics_widget, "🧠")

        except ImportError as e:
            QMessageBox.warning(
                self,
                "تحذير",
                f"التحليلات الذكية غير متوفرة\n\n"
                f"خطأ الاستيراد: {str(e)}\n\n"
                "تأكد من وجود جميع الملفات المطلوبة"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح التحليلات الذكية:\n{str(e)}\n\n"
                f"نوع الخطأ: {type(e).__name__}"
            )

    @audit_action("تسجيل خروج")
    def logout(self):
        """تسجيل الخروج مع تسجيل العملية"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل تريد تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            QApplication.instance().quit()

    def show_user_management(self):
        from .login import UserManagementDialog
        dialog = UserManagementDialog(self.engine)
        result = dialog.exec_()
        
        # إذا تم تعديل المستخدم الحالي، حدث رسالة الترحيب
        if result == QDialog.Accepted:
            self.refresh_user_data()

    def show_home(self):
        """عرض الصفحة الرئيسية - استخدام show_dashboard بدلاً من ذلك"""
        self.show_dashboard()

    def show_dashboard_with_buttons(self):
        """عرض الصفحة الرئيسية مع الأزرار"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إضافة لوحة المعلومات المحسنة (بدون matplotlib)
        try:
            dashboard = EnhancedDashboardWidget(self.engine)
            main_layout.addWidget(dashboard)
        except Exception as e:
            print(f"تحذير: لا يمكن تحميل لوحة المعلومات: {e}")
            # إضافة رسالة ترحيب بسيطة كبديل
            welcome_label = QLabel("🎉 مرحباً بك في نظام المحاسبة العصري")
            welcome_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #2C3E50, stop:1 #3498DB);
                    color: white;
                    font-size: 24px;
                    font-weight: bold;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 10px;
                }
            """)
            welcome_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(welcome_label)
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 30px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                           stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border-radius: 15px;
                margin: 10px;
            }
        """)
        main_layout.addWidget(welcome_label)

        # إضافة الأزرار الرئيسية في الأسفل
        buttons_frame = QFrame()
        buttons_frame.setObjectName("buttonsFrame")
        buttons_frame.setStyleSheet("""
            #buttonsFrame {
                background-color: #F8F9FA;
                border-radius: 15px;
                padding: 30px;
            }
            QPushButton {
                background-color: white;
                border: 2px solid #DEE2E6;
                border-radius: 15px;
                padding: 30px;
                font-size: 18px;
                min-width: 200px;
                min-height: 120px;
            }
            QPushButton:hover {
                background-color: #E9ECEF;
                border-color: #0D6EFD;
                color: #0D6EFD;
            }
        """)

        grid_layout = QGridLayout(buttons_frame)
        grid_layout.setSpacing(20)

        # الأزرار الرئيسية
        menu_buttons = [
            ("المبيعات", "فواتير المبيعات وإدارة العملاء", self.show_sales),
            ("المشتريات", "فواتير المشتريات وإدارة الموردين", self.show_purchases),
            ("المخزون", "إدارة المنتجات والمخزون", self.show_inventory),
            ("العملاء والموردين", "كشوفات الحسابات والأرصدة", lambda: self.show_customer_statement()),
            ("التقارير المالية", "التقارير والتحليلات المالية", self.show_financial_reports),
            ("النسخ الاحتياطي", "حفظ واسترجاع البيانات", self.show_backup_dialog)
        ]

        for index, (title, subtitle, callback) in enumerate(menu_buttons):
            btn = QPushButton()
            btn.clicked.connect(callback)

            btn_layout = QVBoxLayout()
            btn_layout.setAlignment(Qt.AlignCenter)

            title_label = QLabel(title)
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #212529;")
            title_label.setAlignment(Qt.AlignCenter)

            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("font-size: 12px; color: #6C757D;")
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setWordWrap(True)

            btn_layout.addWidget(title_label)
            btn_layout.addWidget(subtitle_label)
            btn.setLayout(btn_layout)

            grid_layout.addWidget(btn, index // 3, index % 3)

        main_layout.addWidget(buttons_frame)

    def show_financial_report(self, report_type):
        """عرض التقارير المحاسبية"""
        # التأكد من وجود التبويبات الثابتة
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        if self.engine:
            try:
                accounting_widget = AccountingWidget(self.engine)
                accounting_widget.report_type.setCurrentText(report_type)
                # إنشاء تبويب جديد للتقرير أو استخدام تبويب موجود
                self.add_or_switch_tab(f"الحسابات - {report_type}", accounting_widget, "📊")
            except Exception as e:
                print(f"خطأ في تحميل وظيفة الحسابات: {e}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل وظيفة الحسابات: {str(e)}")

    def show_dashboard(self):
        """عرض لوحة التحكم العصرية مع Sidebar Navigation والتبويبات الرئيسية"""
        # إنشاء الواجهة الرئيسية إذا لم تكن موجودة
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        # تحميل محتوى الصفحة الرئيسية في التبويب الأول
        self.show_dashboard_content()

        # التأكد من أن التبويب الأول مفعل
        if hasattr(self, 'main_tabs'):
            self.main_tabs.setCurrentIndex(0)

    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية مع التبويبات الثابتة"""
        # إنشاء الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي (عمودي)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إضافة التبويبات الرئيسية
        self.create_main_tabs(main_layout)

    def create_main_tabs(self, layout):
        """إنشاء التبويبات الرئيسية في أعلى الشاشة"""
        # إنشاء التبويبات
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: transparent;
                margin-top: 0px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #667eea, stop:1 #764ba2);
                border: none;
                padding: 12px 30px;
                margin-right: 4px;
                border-radius: 8px 8px 0px 0px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #4CAF50, stop:1 #45a049);
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #5c6bc0, stop:1 #7986cb);
                color: white;
            }
        """)

        # إضافة التبويبات
        self.main_tabs.addTab(QWidget(), "🏠 الصفحة الرئيسية")
        self.main_tabs.addTab(QWidget(), "📄 فاتورة مبيعات جديدة")

        # ربط التبويبات بالوظائف
        self.main_tabs.currentChanged.connect(self.handle_tab_change)

        layout.addWidget(self.main_tabs)

    def handle_tab_change(self, index):
        """التعامل مع تغيير التبويب"""
        if index == 0:  # الصفحة الرئيسية
            self.show_dashboard_content()
            # إعادة تفعيل الشريط الجانبي مع تأخير
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, self.reactivate_sidebar_delayed)
        elif index == 1:  # فاتورة مبيعات جديدة
            self.show_sales_content()

    def reactivate_sidebar(self):
        """إعادة تفعيل وظائف الشريط الجانبي"""
        if hasattr(self, 'sidebar'):
            # إعادة ربط أحداث الماوس بقوة
            def enter_event(event):
                self.on_sidebar_enter(event)

            def leave_event(event):
                self.on_sidebar_leave(event)

            self.sidebar.enterEvent = enter_event
            self.sidebar.leaveEvent = leave_event

            # التأكد من أن الشريط الجانبي في الحالة المطوية
            if hasattr(self, 'sidebar_expanded') and self.sidebar_expanded:
                self.collapse_sidebar()

            # إعادة تعيين حالة الشريط الجانبي
            self.sidebar_expanded = False

            # إعادة تعيين الأنيميشن
            if hasattr(self, 'sidebar_animation'):
                self.sidebar_animation.stop()
            if hasattr(self, 'sidebar_animation2'):
                self.sidebar_animation2.stop()

            # إعادة تعيين الحجم
            self.sidebar.setMinimumWidth(60)
            self.sidebar.setMaximumWidth(60)

            # إخفاء النصوص
            self.hide_sidebar_texts()

    def force_sidebar_events(self):
        """إعادة تعيين أحداث الماوس بقوة للشريط الجانبي"""
        if hasattr(self, 'sidebar'):
            # إزالة أي أحداث موجودة
            self.sidebar.enterEvent = None
            self.sidebar.leaveEvent = None

            # إعادة تعيين الأحداث
            def enter_handler(event):
                if hasattr(self, 'sidebar') and not self.sidebar_expanded:
                    self.expand_sidebar()

            def leave_handler(event):
                if hasattr(self, 'sidebar') and self.sidebar_expanded:
                    self.collapse_sidebar()

            self.sidebar.enterEvent = enter_handler
            self.sidebar.leaveEvent = leave_handler

            # التأكد من أن الشريط في الحالة المطوية
            if hasattr(self, 'sidebar_expanded') and self.sidebar_expanded:
                self.collapse_sidebar()

    def reactivate_sidebar_delayed(self):
        """إعادة تفعيل الشريط الجانبي مع تأخير"""
        # إعادة تفعيل الشريط الجانبي
        self.reactivate_sidebar()
        self.force_sidebar_events()

        # إعادة إنشاء الأحداث من الصفر
        if hasattr(self, 'sidebar'):
            # إزالة الأحداث القديمة تماماً
            self.sidebar.enterEvent = lambda event: None
            self.sidebar.leaveEvent = lambda event: None

            # إعادة إنشاء الأحداث الجديدة
            def new_enter_event(event):
                if hasattr(self, 'sidebar_expanded') and not self.sidebar_expanded:
                    self.expand_sidebar()

            def new_leave_event(event):
                if hasattr(self, 'sidebar_expanded') and self.sidebar_expanded:
                    self.collapse_sidebar()

            # تعيين الأحداث الجديدة
            self.sidebar.enterEvent = new_enter_event
            self.sidebar.leaveEvent = new_leave_event

            # التأكد من الحالة المطوية
            self.sidebar_expanded = False
            self.sidebar.setMinimumWidth(60)
            self.sidebar.setMaximumWidth(60)
            try:
                self.hide_sidebar_texts()
            except:
                pass  # تجاهل الأخطاء في إخفاء النصوص

    def show_dashboard_content(self):
        """عرض محتوى الصفحة الرئيسية في التبويب"""
        if hasattr(self, 'main_tabs') and self.main_tabs.widget(0):
            # التحقق من وجود المحتوى مسبقاً لتجنب إعادة الإنشاء
            if self.main_tabs.widget(0).layout() is None:
                # إنشاء محتوى الصفحة الرئيسية لأول مرة فقط
                dashboard_widget = QWidget()
                dashboard_layout = QHBoxLayout(dashboard_widget)
                dashboard_layout.setContentsMargins(0, 0, 0, 0)
                dashboard_layout.setSpacing(0)

                # إنشاء الشريط الجانبي والمحتوى الرئيسي
                self.create_modern_sidebar(dashboard_layout)
                self.create_main_content(dashboard_layout)

                # إضافة المحتوى للتبويب الأول
                tab_layout = QVBoxLayout()
                tab_layout.setContentsMargins(0, 0, 0, 0)
                tab_layout.addWidget(dashboard_widget)
                self.main_tabs.widget(0).setLayout(tab_layout)

    def show_sales_content(self):
        """عرض محتوى المبيعات في التبويب الثاني"""
        if hasattr(self, 'main_tabs') and self.main_tabs.widget(1):
            # التحقق من وجود المحتوى مسبقاً لتجنب إعادة الإنشاء
            if self.main_tabs.widget(1).layout() is None:
                # إنشاء محتوى المبيعات
                sales_widget = SalesWidget(self.engine)

                # إنشاء تخطيط مع الشريط الجانبي المخفي
                content_widget = QWidget()
                content_layout = QHBoxLayout(content_widget)
                content_layout.setContentsMargins(0, 0, 0, 0)
                content_layout.setSpacing(0)

                # إنشاء شريط جانبي مخفي للصفحات الأخرى
                self.create_hidden_sidebar(content_layout)
                content_layout.addWidget(sales_widget)

                # إضافة المحتوى للتبويب الثاني
                tab_layout = QVBoxLayout()
                tab_layout.setContentsMargins(0, 0, 0, 0)
                tab_layout.addWidget(content_widget)
                self.main_tabs.widget(1).setLayout(tab_layout)

    def setup_responsive_window(self):
        """إعداد النافذة المتجاوبة"""
        try:
            from utils.responsive_ui import setup_responsive_window

            # إعداد النافذة بأحجام متجاوبة
            # الحجم الأساسي: 1400x900، الحد الأدنى: 1000x700
            setup_responsive_window(self, 1400, 900, 1000, 700)

        except ImportError:
            # إذا لم يكن النظام المتجاوب متوفراً، استخدم الأحجام الثابتة
            self.setGeometry(50, 50, 1400, 900)
            self.setMinimumSize(1000, 700)

    def apply_responsive_sizing_only(self):
        """تطبيق الأحجام المتجاوبة فقط بدون تغيير الألوان"""
        try:
            from utils.responsive_ui import responsive_manager

            # تطبيق حجم خط متجاوب على التطبيق بالكامل
            base_font_size = 12
            scaled_font_size = responsive_manager.scale_font_size(base_font_size)

            # تطبيق حجم الخط على التطبيق بدون تغيير الألوان
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtGui import QFont

            app = QApplication.instance()
            if app:
                current_font = app.font()
                current_font.setPointSize(scaled_font_size)
                app.setFont(current_font)

        except ImportError:
            # إذا لم يكن النظام المتجاوب متوفراً، لا نفعل شيء
            pass

    def add_or_switch_tab(self, tab_name, widget, icon="", force_update=False):
        """إضافة تبويب جديد أو التبديل إليه إذا كان موجوداً"""
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        # البحث عن التبويب إذا كان موجوداً
        for i in range(self.main_tabs.count()):
            if self.main_tabs.tabText(i).replace(icon + " ", "") == tab_name:
                # التبويب موجود
                if force_update:
                    # تحديث المحتوى إذا كان مطلوباً
                    tab_layout = QVBoxLayout()
                    tab_layout.setContentsMargins(0, 0, 0, 0)

                    # إضافة الشريط الجانبي للصفحات الأخرى (غير الرئيسية)
                    content_widget = QWidget()
                    content_layout = QHBoxLayout(content_widget)
                    content_layout.setContentsMargins(0, 0, 0, 0)
                    content_layout.setSpacing(0)

                    # إنشاء شريط جانبي مخفي للصفحات الأخرى
                    self.create_hidden_sidebar(content_layout)
                    content_layout.addWidget(widget)

                    tab_layout.addWidget(content_widget)

                    # إزالة المحتوى القديم
                    old_widget = self.main_tabs.widget(i)
                    if old_widget.layout():
                        while old_widget.layout().count():
                            child = old_widget.layout().takeAt(0)
                            if child.widget():
                                child.widget().deleteLater()

                    old_widget.setLayout(tab_layout)

                # التبديل للتبويب
                self.main_tabs.setCurrentIndex(i)
                return

        # التبويب غير موجود، أضفه
        new_tab = QWidget()
        tab_layout = QVBoxLayout()
        tab_layout.setContentsMargins(0, 0, 0, 0)

        # إضافة الشريط الجانبي للصفحات الأخرى (غير الرئيسية)
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # إنشاء شريط جانبي مخفي للصفحات الأخرى
        self.create_hidden_sidebar(content_layout)
        content_layout.addWidget(widget)

        tab_layout.addWidget(content_widget)
        new_tab.setLayout(tab_layout)

        tab_index = self.main_tabs.addTab(new_tab, f"{icon} {tab_name}")

        # إضافة زر الإغلاق للتبويبات الجديدة فقط (ليس للتبويبات الثابتة)
        if tab_index > 1:  # التبويبات الثابتة هي 0 و 1
            self.add_close_button_to_tab(tab_index)

        self.main_tabs.setCurrentIndex(tab_index)

    def add_close_button_to_tab(self, tab_index):
        """إضافة زر إغلاق للتبويب"""
        from PyQt5.QtWidgets import QPushButton
        from PyQt5.QtCore import pyqtSignal

        close_button = QPushButton("✕")
        close_button.setFixedSize(20, 20)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: #666;
                font-weight: bold;
                font-size: 12px;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #ff4444;
                color: white;
            }
            QPushButton:pressed {
                background-color: #cc0000;
            }
        """)

        # ربط زر الإغلاق بدالة الإغلاق
        close_button.clicked.connect(lambda checked, idx=tab_index: self.close_tab(idx))

        # إضافة الزر للتبويب
        self.main_tabs.tabBar().setTabButton(tab_index, self.main_tabs.tabBar().RightSide, close_button)

    def close_tab(self, tab_index):
        """إغلاق التبويب"""
        # التأكد من عدم إغلاق التبويبات الثابتة
        if tab_index > 1:  # التبويبات الثابتة هي 0 و 1
            # إزالة المحتوى
            widget = self.main_tabs.widget(tab_index)
            if widget:
                widget.deleteLater()

            # إزالة التبويب
            self.main_tabs.removeTab(tab_index)

            # التبديل للتبويب الرئيسي إذا لم تعد هناك تبويبات أخرى
            if self.main_tabs.count() <= 2:
                self.main_tabs.setCurrentIndex(0)

    def create_modern_sidebar(self, main_layout):
        """إنشاء شريط جانبي قابل للانزلاق (Slide Bar)"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("slideSidebar")

        # تحديد العرض حسب نوع الصفحة
        self.is_main_page = True  # افتراضياً الصفحة الرئيسية
        initial_width = 110 if self.is_main_page else 5  # 5 بكسل للصفحات الأخرى
        self.sidebar.setFixedWidth(initial_width)

        # إخفاء البار افتراضياً إذا كان مطفي في الإعدادات
        if not self.sidebar_enabled:
            self.sidebar.hide()
            self.sidebar_visible = False
        else:
            self.sidebar_visible = True

        self.sidebar.setStyleSheet("""
            QFrame#slideSidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(26, 32, 44, 0.95),
                            stop:0.5 rgba(45, 55, 72, 0.95),
                            stop:1 rgba(26, 32, 44, 0.95));
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
            }
        """)

        # إضافة أحداث التمرير (فقط إذا كان البار مفعل)
        if self.sidebar_enabled:
            self.sidebar.enterEvent = self.on_sidebar_enter
            self.sidebar.leaveEvent = self.on_sidebar_leave
        else:
            # إلغاء أحداث التمرير إذا كان البار مطفي
            self.sidebar.enterEvent = lambda event: None
            self.sidebar.leaveEvent = lambda event: None

        # إعداد الرسوم المتحركة
        self.sidebar_animation = QPropertyAnimation(self.sidebar, b"minimumWidth")
        self.sidebar_animation.setDuration(300)
        self.sidebar_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.sidebar_animation2 = QPropertyAnimation(self.sidebar, b"maximumWidth")
        self.sidebar_animation2.setDuration(300)
        self.sidebar_animation2.setEasingCurve(QEasingCurve.OutCubic)

        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)

        # شعار الشركة والعنوان
        self.create_sidebar_header(sidebar_layout)

        # قائمة التنقل الرئيسية
        self.create_navigation_menu(sidebar_layout)

        # معلومات المستخدم في الأسفل
        self.create_user_info(sidebar_layout)

        main_layout.addWidget(self.sidebar)

    def create_hidden_sidebar(self, main_layout):
        """إنشاء شريط جانبي مخفي للصفحات الأخرى (غير الرئيسية)"""
        hidden_sidebar = QFrame()
        hidden_sidebar.setObjectName("hiddenSidebar")

        # التحكم في العرض حسب إعدادات البار الجانبي
        if not self.sidebar_enabled:
            hidden_sidebar.setFixedWidth(0)  # مخفي تماماً إذا كان البار مطفي
            hidden_sidebar.hide()
        else:
            hidden_sidebar.setFixedWidth(5)  # عرض صغير جداً (مخفي)

        hidden_sidebar.setStyleSheet("""
            QFrame#hiddenSidebar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 rgba(26, 32, 44, 0.95),
                            stop:0.5 rgba(45, 55, 72, 0.95),
                            stop:1 rgba(26, 32, 44, 0.95));
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
            }
        """)

        # إضافة المحتوى مباشرة ولكن مخفي
        self.add_sidebar_content_to_hidden(hidden_sidebar)

        # إضافة أحداث التمرير للشريط المخفي (فقط إذا كان البار مفعل)
        if self.sidebar_enabled:
            def on_hidden_enter(event):
                # إظهار الشريط الكامل
                hidden_sidebar.setFixedWidth(300)

            def on_hidden_leave(event):
                # إخفاء الشريط مرة أخرى
                hidden_sidebar.setFixedWidth(5)

            hidden_sidebar.enterEvent = on_hidden_enter
            hidden_sidebar.leaveEvent = on_hidden_leave
        else:
            # إلغاء أحداث التمرير إذا كان البار مطفي
            hidden_sidebar.enterEvent = lambda event: None
            hidden_sidebar.leaveEvent = lambda event: None

        # حفظ مرجع للبار المخفي لتحديثه لاحقاً
        self.current_hidden_sidebar = hidden_sidebar

        main_layout.addWidget(hidden_sidebar)

    def add_sidebar_content_to_hidden(self, sidebar):
        """إضافة محتوى الشريط الجانبي للشريط المخفي"""
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)

        # إضافة العنوان
        title_label = QLabel("نظام المحاسبة")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            text-align: center;
            margin: 10px 0;
            padding: 10px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(title_label)

        # إضافة الأزرار مع النصوص
        nav_items = [
            ("🏠", "الرئيسية", self.show_home),
            ("📄", "فاتورة جديدة", self.show_sales),
            ("📋", "عرض الفواتير", self.show_invoices_view),
            ("🛒", "المشتريات", self.show_purchases),
            ("🔄", "مرتجع المبيعات", self.show_sales_returns),
            ("↩️", "مرتجع المشتريات", self.show_purchase_returns),
            ("📑", "فواتير المرتجعات", self.show_returns_view),
            ("👥", "العملاء والموردين", self.show_contacts),
            ("📦", "المخزون", self.show_inventory),
            ("📊", "التقارير المالية", self.show_financial_reports),
            ("⚙️", "الإعدادات", self.show_app_settings),
        ]

        for icon, text, action in nav_items:
            btn = QPushButton()
            btn.setFixedHeight(50)
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    color: white;
                    font-size: 14px;
                    font-weight: 500;
                    text-align: left;
                    padding: 8px 12px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.2);
                    border-color: rgba(255, 255, 255, 0.4);
                }
                QPushButton:pressed {
                    background: rgba(255, 255, 255, 0.3);
                }
            """)

            # تخطيط الزر
            btn_layout = QHBoxLayout(btn)
            btn_layout.setContentsMargins(8, 8, 8, 8)
            btn_layout.setSpacing(12)

            # الأيقونة
            icon_label = QLabel(icon)
            icon_label.setStyleSheet("font-size: 18px; color: white;")
            icon_label.setFixedSize(24, 24)
            icon_label.setAlignment(Qt.AlignCenter)
            btn_layout.addWidget(icon_label)

            # النص
            text_label = QLabel(text)
            text_label.setStyleSheet("font-size: 14px; color: white; font-weight: 500;")
            btn_layout.addWidget(text_label)

            btn_layout.addStretch()

            # ربط الإجراء
            btn.clicked.connect(action)
            sidebar_layout.addWidget(btn)

        # إضافة مساحة مرنة
        sidebar_layout.addStretch()

        # إضافة معلومات المستخدم
        user_name = self.current_user.full_name if self.current_user else 'مستخدم'
        user_label = QLabel(f"👤 {user_name}")
        user_label.setStyleSheet("""
            font-size: 14px;
            color: white;
            font-weight: 500;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            margin: 5px 0;
        """)
        user_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(user_label)

        # إضافة زر الخروج
        logout_btn = QPushButton("🚪 تسجيل الخروج")
        logout_btn.setFixedHeight(45)
        logout_btn.setStyleSheet("""
            QPushButton {
                background: rgba(231, 76, 60, 0.8);
                border: 2px solid rgba(231, 76, 60, 1);
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
                margin: 5px 0;
            }
            QPushButton:hover {
                background: rgba(231, 76, 60, 1);
            }
            QPushButton:pressed {
                background: rgba(192, 57, 43, 1);
            }
        """)
        logout_btn.clicked.connect(self.logout)
        sidebar_layout.addWidget(logout_btn)

    def clear_sidebar_content(self, sidebar):
        """إزالة محتوى الشريط الجانبي"""
        if sidebar.layout():
            while sidebar.layout().count():
                child = sidebar.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            sidebar.layout().deleteLater()

    def create_sidebar_header(self, layout):
        """إنشاء رأس الشريط الجانبي"""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)

        # شعار الشركة (أيقونة فقط في البداية)
        self.logo_label = QLabel("💼")
        self.logo_label.setStyleSheet("""
            font-size: 32px;
            color: #667eea;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(102, 126, 234, 0.2),
                        stop:1 rgba(118, 75, 162, 0.2));
            border-radius: 15px;
            padding: 8px;
            min-width: 40px;
            min-height: 40px;
        """)
        self.logo_label.setAlignment(Qt.AlignCenter)

        # اسم النظام (مخفي في البداية)
        self.sidebar_title = QLabel("نظام المحاسبة")
        self.sidebar_title.setStyleSheet("""
            font-size: 20px;
            font-weight: 700;
            color: white;
            margin: 5px 0;
        """)
        self.sidebar_title.setAlignment(Qt.AlignCenter)
        self.sidebar_title.hide()  # مخفي في البداية

        # نسخة النظام (مخفي في البداية)
        self.version_label = QLabel("v2025")
        self.version_label.setStyleSheet("""
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        """)
        self.version_label.setAlignment(Qt.AlignCenter)
        self.version_label.hide()  # مخفي في البداية

        header_layout.addWidget(self.logo_label)
        header_layout.addWidget(self.sidebar_title)
        header_layout.addWidget(self.version_label)

        layout.addWidget(header_frame)

    def create_navigation_menu(self, layout):
        """إنشاء قائمة التنقل العصرية"""
        nav_frame = QFrame()
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setSpacing(8)

        # عنوان القائمة
        menu_title = QLabel("القائمة الرئيسية")
        menu_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
            margin: 20px 0 10px 0;
            padding: 0 15px;
        """)
        nav_layout.addWidget(menu_title)

        # عناصر القائمة
        menu_items = [
            ("🏠", "الصفحة الرئيسية", self.show_dashboard, True),
            ("📄", "فاتورة جديدة", self.show_sales, False),
            ("📋", "عرض الفواتير", self.show_invoices_view, False),
            ("🔄", "المرتجعات", self.show_returns_view, False),
            ("🛒", "المشتريات", self.show_purchases, False),
            ("📦", "المخزون", self.show_inventory, False),
            ("👥", "العملاء والموردين", self.show_contacts, False),
            ("📊", "التقارير", self.show_reports, False),
            ("⚙️", "إعدادات الشركة", self.show_company_settings, False),
        ]

        self.nav_buttons = []
        for icon, text, callback, is_active in menu_items:
            btn = self.create_nav_button(icon, text, callback, is_active)
            nav_layout.addWidget(btn)
            self.nav_buttons.append(btn)

        layout.addWidget(nav_frame)
        layout.addStretch()

        if self.check_permission("إدارة_المشتريات"):
            self.create_nav_button("🧾", "عرض فواتير المشتريات", self.show_purchase_invoices_view)
            self.create_nav_button("🛒", "فاتورة مشتريات جديدة", self.show_purchases)
            self.create_nav_button("🔄", "مرتجع المشتريات", self.show_purchase_returns)

    def create_nav_button(self, icon, text, callback, is_active=False):
        """إنشاء زر تنقل عصري"""
        btn = QPushButton()
        btn.setObjectName("navButton")

        # تخطيط الزر
        btn_layout = QHBoxLayout()
        btn_layout.setContentsMargins(5, 8, 25, 8)  # مساحة أقل من اليسار، أكثر من اليمين
        btn_layout.setSpacing(8)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            font-size: 24px;
            color: white;
            padding: 8px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            margin: 2px;
        """)
        icon_label.setFixedSize(60, 60)  # حجم مناسب للأيقونة
        icon_label.setAlignment(Qt.AlignCenter)

        # النص (مخفي في البداية)
        text_label = QLabel(text)
        text_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
        """)
        text_label.hide()  # مخفي في البداية

        btn_layout.addWidget(icon_label, 0, Qt.AlignLeft | Qt.AlignVCenter)
        btn_layout.addWidget(text_label, 1, Qt.AlignLeft | Qt.AlignVCenter)
        btn_layout.addStretch()

        btn.setLayout(btn_layout)
        btn.setFixedHeight(70)  # ارتفاع مناسب لإظهار الأيقونات بالكامل

        # حفظ المراجع للاستخدام لاحقاً
        btn.icon_label = icon_label
        btn.text_label = text_label
        btn.callback = callback

        # تطبيق الأنماط
        active_style = """
            QPushButton#navButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #667eea, stop:1 #764ba2);
                border: none;
                border-radius: 12px;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
        """

        inactive_style = """
            QPushButton#navButton {
                background: transparent;
                border: none;
                border-radius: 12px;
            }
            QPushButton#navButton:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            QPushButton#navButton:pressed {
                background: rgba(255, 255, 255, 0.2);
            }
        """

        btn.setStyleSheet(active_style if is_active else inactive_style)
        btn.clicked.connect(lambda: self.handle_nav_click(btn, callback))

        return btn

    def handle_nav_click(self, clicked_btn, callback):
        """التعامل مع النقر على أزرار التنقل"""
        # إزالة التفعيل من جميع الأزرار
        for btn in self.nav_buttons:
            btn.setStyleSheet("""
                QPushButton#navButton {
                    background: transparent;
                    border: none;
                    border-radius: 12px;
                }
                QPushButton#navButton:hover {
                    background: rgba(255, 255, 255, 0.1);
                }
                QPushButton#navButton:pressed {
                    background: rgba(255, 255, 255, 0.2);
                }
            """)

        # تفعيل الزر المنقور
        clicked_btn.setStyleSheet("""
            QPushButton#navButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #667eea, stop:1 #764ba2);
                border: none;
                border-radius: 12px;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
        """)

        # تنفيذ الوظيفة
        callback()

    def create_user_info(self, layout):
        """إنشاء معلومات المستخدم في أسفل الشريط الجانبي"""
        user_frame = QFrame()
        user_frame.setStyleSheet("""
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px;
        """)

        user_layout = QVBoxLayout(user_frame)
        user_layout.setSpacing(8)

        # صورة المستخدم (رمز) - ظاهرة دائماً
        self.user_avatar = QLabel("👤")
        self.user_avatar.setStyleSheet("""
            font-size: 24px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 8px;
            margin: 2px;
        """)
        self.user_avatar.setFixedSize(60, 60)  # نفس حجم الأيقونات الأخرى
        self.user_avatar.setAlignment(Qt.AlignCenter)

        # اسم المستخدم (مخفي في البداية)
        self.user_name_label = QLabel(self.current_user.full_name if self.current_user else "مستخدم")
        self.user_name_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: white;
        """)
        self.user_name_label.setAlignment(Qt.AlignCenter)
        self.user_name_label.hide()  # مخفي في البداية

        # زر تسجيل الخروج (مخفي في البداية)
        self.logout_btn = QPushButton("🚪")  # أيقونة فقط في البداية
        self.logout_btn.setFixedSize(60, 60)  # نفس حجم الأيقونات الأخرى
        self.logout_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.15);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 8px;
                font-size: 24px;
                margin: 2px;
            }
            QPushButton:hover {
                background: rgba(245, 101, 101, 0.8);
            }
        """)
        self.logout_btn.clicked.connect(self.logout)

        user_layout.addWidget(self.user_avatar)
        user_layout.addWidget(self.user_name_label)
        user_layout.addWidget(self.logout_btn)

        layout.addWidget(user_frame)

    def create_main_content(self, main_layout):
        """إنشاء المحتوى الرئيسي العصري"""
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(25)

        # شريط العنوان العصري
        self.create_modern_header(content_layout)

        # بطاقات الإحصائيات
        self.create_stats_cards(content_layout)

        # الأزرار السريعة
        self.create_quick_actions(content_layout)

        main_layout.addWidget(content_widget, 1)

    def create_modern_header(self, layout):
        """إنشاء شريط العنوان العصري"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, 0.95),
                        stop:1 rgba(247, 250, 252, 0.95));
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(20px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        """)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)

        # رسالة الترحيب (الجانب الأيسر)
        from datetime import datetime
        current_date = datetime.now()

        # تحويل أسماء الأيام والشهور للعربية
        days_ar = {
            'Monday': 'الاثنين', 'Tuesday': 'الثلاثاء', 'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس', 'Friday': 'الجمعة', 'Saturday': 'السبت', 'Sunday': 'الأحد'
        }
        months_ar = {
            'January': 'يناير', 'February': 'فبراير', 'March': 'مارس', 'April': 'أبريل',
            'May': 'مايو', 'June': 'يونيو', 'July': 'يوليو', 'August': 'أغسطس',
            'September': 'سبتمبر', 'October': 'أكتوبر', 'November': 'نوفمبر', 'December': 'ديسمبر'
        }

        day_name = current_date.strftime("%A")
        month_name = current_date.strftime("%B")
        day_ar = days_ar.get(day_name, day_name)
        month_ar = months_ar.get(month_name, month_name)

        # تعريف date_text دائماً قبل أي استخدام
        date_text = f"{day_ar}, {current_date.day} {month_ar} {current_date.year}"

        # الحصول على اسم المستخدم الحالي أو fallback
        user_full_name = getattr(self.current_user, 'full_name', None)
        if user_full_name and user_full_name.strip():
            welcome_text = f"مرحباً {user_full_name}"
        else:
            user_title = self.company_settings.get('owner_name', 'المدير العام')
            welcome_text = f"مرحباً {user_title}"

        welcome_label = QLabel(welcome_text)
        welcome_label.setStyleSheet("""
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        """)
        welcome_label.setAlignment(Qt.AlignLeft)
        self.welcome_label = welcome_label  # حفظ عنصر الترحيب كخاصية

        date_label = QLabel(date_text)
        date_label.setStyleSheet("""
            font-size: 18px;
            font-weight: 500;
            color: #718096;
            margin: 5px 0 0 0;
        """)
        date_label.setAlignment(Qt.AlignLeft)

        # تخطيط النصوص
        text_layout = QVBoxLayout()
        text_layout.setSpacing(5)
        text_layout.addWidget(welcome_label)
        text_layout.addWidget(date_label)

        # الجانب الأيمن: أيقونة الوقت + لوجو الشركة + اسم الشركة
        right_layout = QHBoxLayout()
        right_layout.setSpacing(20)
        right_layout.setAlignment(Qt.AlignRight)

        # لوجو الشركة واسم الشركة
        company_layout = QVBoxLayout()
        company_layout.setSpacing(5)
        company_layout.setAlignment(Qt.AlignCenter)

        # لوجو الشركة
        self.main_logo_label = QLabel()
        self.main_logo_label.setAlignment(Qt.AlignCenter)
        self.load_company_logo()  # تحميل لوجو الشركة

        # اسم الشركة من إعدادات الشركة
        company_name = self.company_settings.get('company_name', 'شركة المحاسبة')
        self.company_name_label = QLabel(company_name)
        self.company_name_label.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin: 2px 0;
        """)
        self.company_name_label.setAlignment(Qt.AlignCenter)

        company_layout.addWidget(self.main_logo_label)
        company_layout.addWidget(self.company_name_label)

        # أيقونة الطقس أو الوقت
        time_icon = QLabel("🌅" if 6 <= current_date.hour < 12 else
                          "☀️" if 12 <= current_date.hour < 18 else "🌙")
        time_icon.setStyleSheet("""
            font-size: 48px;
            margin: 0 10px;
        """)

        right_layout.addLayout(company_layout)
        right_layout.addWidget(time_icon)

        # ترتيب العناصر: الترحيب - مساحة فارغة - الجانب الأيمن
        header_layout.addLayout(text_layout)
        header_layout.addStretch()  # مساحة فارغة في الوسط
        header_layout.addLayout(right_layout)

        layout.addWidget(header_frame)

    def create_stats_cards(self, layout):
        """إنشاء بطاقات الإحصائيات العصرية"""
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(45)  # زيادة المسافات بين البطاقات أكثر
        stats_layout.setContentsMargins(30, 30, 30, 30)

        # بيانات البطاقات
        try:
            from sqlalchemy.orm import sessionmaker
            Session = sessionmaker(bind=self.engine)
            session = Session()

            # إحصائيات سريعة
            from database.models import Transaction, Product, Customer, Supplier
            from database.models import TransactionType

            total_sales = session.query(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).count()

            total_products = session.query(Product).count()
            total_customers = session.query(Customer).count()
            total_suppliers = session.query(Supplier).count()

            session.close()

            cards_data = [
                ("📊", "إجمالي المبيعات", str(total_sales), "#667eea"),
                ("📦", "المنتجات", str(total_products), "#764ba2"),
                ("👥", "العملاء", str(total_customers), "#f093fb"),
                ("🏪", "الموردين", str(total_suppliers), "#48bb78"),
            ]

        except Exception as e:
            print(f"Error loading stats: {e}")
            cards_data = [
                ("📊", "إجمالي المبيعات", "0", "#667eea"),
                ("📦", "المنتجات", "0", "#764ba2"),
                ("👥", "العملاء", "0", "#f093fb"),
                ("🏪", "الموردين", "0", "#48bb78"),
            ]

        for index, (icon, title, value, color) in enumerate(cards_data):
            card = self.create_stat_card(icon, title, value, color)
            row = 0  # صف واحد فقط
            col = index  # كل بطاقة في عمود منفصل
            stats_layout.addWidget(card, row, col)

        layout.addWidget(stats_frame)

    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية بتصميم نظيف وواضح"""
        card = QFrame()
        card.setFixedHeight(140)  # ارتفاع أقل
        card.setMinimumWidth(250)  # عرض أقل
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 8px;
                border: none;
                margin: 5px;
            }}
        """)

        card_layout = QHBoxLayout(card)
        card_layout.setContentsMargins(15, 15, 15, 15)
        card_layout.setSpacing(15)

        # الأيقونة على اليسار
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            font-size: 40px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 10px;
            min-width: 70px;
            min-height: 70px;
            max-width: 70px;
            max-height: 70px;
        """)
        icon_label.setAlignment(Qt.AlignCenter)

        # النصوص على اليمين
        text_layout = QVBoxLayout()
        text_layout.setSpacing(5)
        text_layout.setContentsMargins(0, 0, 0, 0)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
            padding: 0;
            margin: 0;
        """)
        title_label.setWordWrap(True)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)

        # القيمة
        value_label = QLabel(str(value))
        value_label.setStyleSheet("""
            font-size: 28px;
            font-weight: 800;
            color: white;
            background: transparent;
            padding: 0;
            margin: 0;
        """)
        value_label.setAlignment(Qt.AlignLeft | Qt.AlignBottom)

        text_layout.addWidget(title_label)
        text_layout.addWidget(value_label)
        text_layout.addStretch()

        card_layout.addWidget(icon_label)
        card_layout.addLayout(text_layout, 1)

        return card

    def create_quick_actions(self, layout):
        """إنشاء الأزرار السريعة العصرية"""
        actions_frame = QFrame()
        actions_frame.setStyleSheet("""
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        """)

        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setSpacing(25)

        # عنوان القسم مع تحسين الوضوح
        title_label = QLabel("الإجراءات السريعة")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: 800;
            color: #1a202c;
            margin-bottom: 20px;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            text-align: center;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        actions_layout.addWidget(title_label)

        # شبكة الأزرار
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(20)

        # الأزرار السريعة
        quick_buttons = [
            ("📄", "فاتورة جديدة", "#667eea", self.show_sales),
            ("📋", "عرض الفواتير", "#764ba2", self.show_invoices_view),
            ("🔄", "المرتجعات", "#f093fb", self.show_returns_view),
            ("🛒", "المشتريات", "#48bb78", self.show_purchases),
            ("📦", "المخزون", "#ed8936", self.show_inventory),
            ("📊", "التقارير", "#f56565", self.show_reports),
        ]

        for index, (icon, text, color, callback) in enumerate(quick_buttons):
            btn = self.create_quick_action_button(icon, text, color, callback)
            row = index // 3
            col = index % 3
            buttons_grid.addWidget(btn, row, col)

        actions_layout.addLayout(buttons_grid)
        layout.addWidget(actions_frame)

    def create_quick_action_button(self, icon, text, color, callback):
        """إنشاء زر إجراء سريع عصري مع نصوص واضحة"""
        btn = QPushButton()
        btn.setFixedHeight(120)
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 {color}, stop:1 {self.darken_color(color)});
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 16px;
                font-weight: 700;
                text-align: center;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 {self.darken_color(color)}, stop:1 {self.darken_color(color, 0.3)});
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 {self.darken_color(color, 0.4)}, stop:1 {self.darken_color(color, 0.5)});
            }}
        """)

        # إنشاء النص المركب (أيقونة + نص)
        button_text = f"{icon}\n{text}"
        btn.setText(button_text)

        # تطبيق خط أكبر وأوضح
        font = btn.font()
        font.setPointSize(14)
        font.setBold(True)
        btn.setFont(font)

        btn.clicked.connect(callback)
        return btn

    def darken_color(self, color, factor=0.2):
        """تغميق لون معين"""
        # إزالة # من بداية اللون
        color = color.lstrip('#')

        # تحويل إلى RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        # تطبيق التغميق
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))

        # إرجاع اللون الجديد
        return f"#{r:02x}{g:02x}{b:02x}"

    def create_main_buttons(self, main_layout):
        """إنشاء أزرار الوظائف الأكثر استخداماً"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 20px;
                padding: 40px;
            }
        """)

        grid_layout = QGridLayout(buttons_frame)
        grid_layout.setSpacing(25)

        # الأزرار الرئيسية مع الأيقونات والوصف (بدون تكرار)
        main_buttons = [
            ("📄 فاتورة جديدة", "إنشاء فاتورة مبيعات جديدة", "#2ECC71", self.show_sales),
            ("📋 عرض الفواتير", "عرض جميع الفواتير المحفوظة", "#3498DB", self.show_invoices_view),
            ("🔄 مرتجع المبيعات", "إرجاع منتجات من فواتير المبيعات", "#E74C3C", self.show_sales_returns),
            ("👤 إضافة عميل", "إضافة عميل جديد للنظام", "#9B59B6", self.show_add_customer),
            ("📦 إضافة منتج", "إضافة منتج جديد للمخزون", "#E67E22", self.show_add_product),
            ("🏢 إعدادات الشركة", "تخصيص معلومات وشعار الشركة", "#8E44AD", self.show_company_settings),
            ("📊 التقارير المالية", "عرض التقارير والتحليلات المالية", "#F39C12", self.show_financial_reports),
            ("💾 النسخ الاحتياطي", "حفظ واسترجاع البيانات", "#34495E", self.show_backup_dialog)
        ]

        for index, (title, description, color, callback) in enumerate(main_buttons):
            btn = self.create_function_button(title, description, color, callback)
            row = index // 3
            col = index % 3
            grid_layout.addWidget(btn, row, col)

        main_layout.addWidget(buttons_frame)

    def create_function_button(self, title, description, color, callback):
        """إنشاء زر وظيفة مع التصميم المحسن"""
        btn = QPushButton()
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 15px;
                padding: 30px 25px;
                font-size: 18px;
                font-weight: bold;
                min-width: 280px;
                min-height: 160px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                border: 2px solid white;
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
        """)

        # تخطيط الزر
        btn_layout = QVBoxLayout()
        btn_layout.setAlignment(Qt.AlignCenter)
        btn_layout.setSpacing(15)
        btn_layout.setContentsMargins(20, 25, 20, 25)

        # العنوان مع الأيقونة
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 26px;
            font-weight: bold;
            color: white;
            margin: 8px 0;
            padding: 5px;
            text-align: center;
            background: transparent;
            line-height: 1.2;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)

        # الوصف
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            font-size: 15px;
            color: white;
            text-align: center;
            margin: 8px 0;
            padding: 5px;
            background: transparent;
            line-height: 1.3;
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)

        btn_layout.addWidget(title_label)
        btn_layout.addWidget(desc_label)
        btn.setLayout(btn_layout)

        btn.clicked.connect(callback)
        return btn

    def get_brand_name(self):
        """الحصول على اسم البراند من الإعدادات"""
        try:
            import json
            import os
            settings_file = "brand_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get('brand_name', 'شركة المحاسبة المتقدمة')
            else:
                return 'شركة المحاسبة المتقدمة'
        except:
            return 'شركة المحاسبة المتقدمة'

    def get_brand_logo_path(self):
        """الحصول على مسار لوجو البراند من الإعدادات"""
        try:
            import json
            import os, sys

            # تحديد المسار الصحيح لملف الإعدادات
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "brand_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "brand_settings.json"

            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    logo_path = settings.get('logo_path', None)

                    # إذا كان المسار نسبي، نحوله إلى مطلق
                    if logo_path and not os.path.isabs(logo_path):
                        if getattr(sys, 'frozen', False):
                            logo_path = os.path.join(base_dir, logo_path)
                        else:
                            logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), logo_path)

                    return logo_path
            return None
        except Exception as e:
            print(f"خطأ في تحميل مسار لوجو البراند: {e}")
            return None

    def load_brand_logo(self):
        """تحميل لوجو البراند"""
        from PyQt5.QtGui import QPixmap
        from PyQt5.QtCore import Qt
        import os

        logo_path = self.get_brand_logo_path()

        if logo_path and os.path.exists(logo_path):
            # تحميل الصورة المخصصة
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                # تغيير حجم الصورة لتناسب المساحة (ضعف الحجم السابق)
                scaled_pixmap = pixmap.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.logo_label.setPixmap(scaled_pixmap)
                self.logo_label.setStyleSheet("""
                    border: 3px solid rgba(255,255,255,0.3);
                    border-radius: 12px;
                    padding: 8px;
                    background: rgba(255,255,255,0.1);
                    min-width: 140px;
                    min-height: 140px;
                """)
            else:
                self.set_default_logo()
        else:
            self.set_default_logo()

    def set_default_logo(self):
        """تعيين اللوجو الافتراضي"""
        self.logo_label.setText("🏢")
        self.logo_label.setStyleSheet("""
            font-size: 96px;
            color: white;
            text-align: center;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            min-width: 140px;
            min-height: 140px;
        """)

    def load_company_logo(self):
        """تحميل لوجو الشركة من إعدادات الشركة"""
        from PyQt5.QtGui import QPixmap
        from PyQt5.QtCore import Qt
        import os, sys

        # الحصول على مسار اللوجو من إعدادات الشركة
        logo_path = self.company_settings.get('logo_path')

        if logo_path:
            # تحديد المسار الكامل للوجو
            if not os.path.isabs(logo_path):
                # إذا كان المسار نسبي، نحدد المجلد الأساسي
                if getattr(sys, 'frozen', False):
                    # إذا كان البرنامج مصدر (PyInstaller)
                    base_dir = os.path.dirname(sys.executable)
                else:
                    # إذا كان البرنامج يعمل من الكود المصدري
                    base_dir = os.path.dirname(os.path.dirname(__file__))
                logo_path = os.path.join(base_dir, logo_path)

            if os.path.exists(logo_path):
                # تحميل الصورة المخصصة
                pixmap = QPixmap(logo_path)
                if not pixmap.isNull():
                    # تصغير الصورة لتناسب المساحة المتاحة (150x150)
                    scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.main_logo_label.setPixmap(scaled_pixmap)
                    self.main_logo_label.setStyleSheet("""
                        border: 3px solid rgba(255,255,255,0.3);
                        border-radius: 12px;
                        padding: 8px;
                        background: rgba(255,255,255,0.1);
                        min-width: 140px;
                        min-height: 140px;
                    """)
                    return
                else:
                    print(f"فشل في تحميل الصورة: {logo_path}")
            else:
                print(f"ملف اللوجو غير موجود: {logo_path}")

        # استخدام أيقونة افتراضية
        self.set_default_company_logo()

    def set_default_company_logo(self):
        """تعيين لوجو الشركة الافتراضي"""
        self.main_logo_label.setText("🏢")
        self.main_logo_label.setStyleSheet("""
            font-size: 72px;
            color: #3498DB;
            text-align: center;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            min-width: 140px;
            min-height: 140px;
        """)

    def load_main_brand_logo(self):
        """تحميل لوجو البراند في المنطقة البيضاء الرئيسية"""
        from PyQt5.QtGui import QPixmap
        from PyQt5.QtCore import Qt
        import os

        logo_path = self.get_brand_logo_path()
        if logo_path and os.path.exists(logo_path):
            # تحميل الصورة المخصصة
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                # تغيير حجم الصورة للمنطقة الرئيسية (أصغر من الشريط الجانبي)
                scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.main_logo_label.setPixmap(scaled_pixmap)
                self.main_logo_label.setStyleSheet("""
                    border: 2px solid rgba(45, 55, 72, 0.2);
                    border-radius: 10px;
                    padding: 5px;
                    background: rgba(255, 255, 255, 0.8);
                    min-width: 90px;
                    min-height: 90px;
                """)
            else:
                self.set_default_main_logo()
        else:
            self.set_default_main_logo()

    def set_default_main_logo(self):
        """تعيين اللوجو الافتراضي للمنطقة الرئيسية"""
        self.main_logo_label.setText("🏢")
        self.main_logo_label.setStyleSheet("""
            font-size: 48px;
            color: #2d3748;
            text-align: center;
            border: 2px solid rgba(45, 55, 72, 0.2);
            border-radius: 10px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.8);
            min-width: 90px;
            min-height: 90px;
        """)

    def save_brand_settings(self, brand_name, logo_path=None):
        """حفظ إعدادات البراند"""
        try:
            import json, os, sys

            # تحديد المسار الصحيح لحفظ الملف
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "brand_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "brand_settings.json"

            settings = {
                'brand_name': brand_name,
                'logo_path': logo_path
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات البراند: {e}")
            return False

    def get_user_title(self):
        """الحصول على لقب المستخدم المخصص"""
        try:
            import json
            import os
            import sys
            
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "user_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "user_settings.json"
                
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get('user_title', 'مدير النظام')
            return 'مدير النظام'
        except:
            return 'مدير النظام'

    def save_user_title(self, title):
        """حفظ لقب المستخدم المخصص"""
        try:
            import json
            import os
            import sys
            
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "user_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "user_settings.json"
                
            settings = {}
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

            settings['user_title'] = title

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False

    def show_brand_settings(self):
        """عرض نافذة إعدادات البراند"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QLabel, QFileDialog, QMessageBox, QFrame
        from PyQt5.QtCore import Qt
        import os

        dialog = QDialog(self)
        dialog.setWindowTitle("إعدادات البراند")
        dialog.setModal(True)
        dialog.resize(600, 450)

        layout = QVBoxLayout(dialog)

        # اسم البراند
        name_label = QLabel("اسم الشركة/البراند:")
        name_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px 0;")
        layout.addWidget(name_label)

        name_input = QLineEdit()
        name_input.setText(self.get_brand_name())
        name_input.setStyleSheet("""
            font-size: 16px;
            padding: 10px;
            border: 2px solid #3498DB;
            border-radius: 5px;
            margin-bottom: 20px;
        """)
        layout.addWidget(name_input)

        # لقب المستخدم
        title_label = QLabel("لقب المستخدم:")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px 0;")
        layout.addWidget(title_label)

        title_input = QLineEdit()
        title_input.setText(self.get_user_title())
        title_input.setStyleSheet("""
            font-size: 16px;
            padding: 10px;
            border: 2px solid #3498DB;
            border-radius: 5px;
            margin-bottom: 20px;
        """)
        layout.addWidget(title_input)

        # اللوجو
        logo_label = QLabel("لوجو الشركة:")
        logo_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px 0;")
        layout.addWidget(logo_label)

        # عرض اللوجو الحالي
        current_logo_frame = QFrame()
        current_logo_layout = QHBoxLayout(current_logo_frame)

        self.preview_logo = QLabel()
        self.preview_logo.setFixedSize(160, 160)
        self.preview_logo.setStyleSheet("""
            border: 3px solid #3498DB;
            border-radius: 12px;
            background: #f8f9fa;
        """)
        self.preview_logo.setAlignment(Qt.AlignCenter)

        # تحميل اللوجو الحالي للمعاينة
        logo_path = self.get_brand_logo_path()
        if logo_path and os.path.exists(logo_path):
            from PyQt5.QtGui import QPixmap
            from PyQt5.QtCore import Qt
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(152, 152, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.preview_logo.setPixmap(scaled_pixmap)
            else:
                self.preview_logo.setText("🏢")
                self.preview_logo.setStyleSheet(self.preview_logo.styleSheet() + "font-size: 80px;")
        else:
            self.preview_logo.setText("🏢")
            self.preview_logo.setStyleSheet(self.preview_logo.styleSheet() + "font-size: 80px;")

        current_logo_layout.addWidget(self.preview_logo)

        # أزرار اللوجو
        logo_buttons_layout = QVBoxLayout()

        upload_logo_btn = QPushButton("رفع لوجو جديد")
        upload_logo_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)

        remove_logo_btn = QPushButton("إزالة اللوجو")
        remove_logo_btn.setStyleSheet("""
            QPushButton {
                background-color: #E67E22;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #D35400;
            }
        """)

        logo_buttons_layout.addWidget(upload_logo_btn)
        logo_buttons_layout.addWidget(remove_logo_btn)

        current_logo_layout.addLayout(logo_buttons_layout)
        layout.addWidget(current_logo_frame)

        # متغير لحفظ مسار اللوجو الجديد
        self.new_logo_path = logo_path

        # وظائف أزرار اللوجو
        def upload_logo():
            file_path, _ = QFileDialog.getOpenFileName(
                dialog,
                "اختر لوجو الشركة",
                "",
                "Image Files (*.png *.jpg *.jpeg *.bmp *.gif *.svg)"
            )
            if file_path:
                # معاينة اللوجو الجديد
                from PyQt5.QtGui import QPixmap
                from PyQt5.QtCore import Qt
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(152, 152, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.preview_logo.setPixmap(scaled_pixmap)
                    self.preview_logo.setStyleSheet("""
                        border: 3px solid #3498DB;
                        border-radius: 12px;
                        background: #f8f9fa;
                    """)
                    self.new_logo_path = file_path
                else:
                    QMessageBox.warning(dialog, "خطأ", "فشل في تحميل الصورة!")

        def remove_logo():
            self.preview_logo.clear()
            self.preview_logo.setText("🏢")
            self.preview_logo.setStyleSheet("""
                border: 3px solid #3498DB;
                border-radius: 12px;
                background: #f8f9fa;
                font-size: 80px;
            """)
            self.new_logo_path = None

        upload_logo_btn.clicked.connect(upload_logo)
        remove_logo_btn.clicked.connect(remove_logo)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ECC71;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #27AE60;
            }
        """)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)

        def save_settings():
            brand_name = name_input.text().strip()
            user_title = title_input.text().strip()

            if brand_name and user_title:
                # حفظ إعدادات البراند ولقب المستخدم
                brand_saved = self.save_brand_settings(brand_name, self.new_logo_path)
                title_saved = self.save_user_title(user_title)

                if brand_saved and title_saved:
                    QMessageBox.information(dialog, "نجح", "تم حفظ جميع الإعدادات بنجاح!")
                    dialog.accept()
                    # تحديث اللوجو واسم الشركة في المنطقة البيضاء
                    if hasattr(self, 'main_logo_label'):
                        self.load_main_brand_logo()
                    if hasattr(self, 'company_name_label'):
                        self.company_name_label.setText(brand_name)
                    # إعادة تحميل الصفحة الرئيسية
                    self.show_home()
                else:
                    QMessageBox.warning(dialog, "خطأ", "فشل في حفظ بعض الإعدادات!")
            else:
                QMessageBox.warning(dialog, "تحذير", "يرجى إدخال جميع البيانات المطلوبة!")

        save_btn.clicked.connect(save_settings)
        cancel_btn.clicked.connect(dialog.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

        dialog.exec_()

    def darken_color(self, color, factor=0.2):
        """تغميق اللون للتأثيرات"""
        color_map = {
            "#2ECC71": "#27AE60",
            "#3498DB": "#2980B9",
            "#9B59B6": "#8E44AD",
            "#8E44AD": "#7D3C98",
            "#E67E22": "#D35400",
            "#1ABC9C": "#16A085",
            "#F39C12": "#E67E22",
            "#E74C3C": "#C0392B",
            "#34495E": "#2C3E50"
        }
        return color_map.get(color, color)

    def show_add_customer(self):
        """عرض نافذة إضافة عميل جديد"""
        try:
            contacts_widget = ContactsWidget(self.engine)
            contacts_widget.tab_widget.setCurrentIndex(0)  # تبويب العملاء
            self.setCentralWidget(contacts_widget)
            # محاولة فتح نافذة إضافة عميل مباشرة
            if hasattr(contacts_widget, 'add_customer'):
                contacts_widget.add_customer()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إضافة العميل: {str(e)}")

    def show_add_product(self):
        """عرض نافذة إضافة منتج جديد"""
        try:
            inventory_widget = InventoryWidget(self.engine)
            self.setCentralWidget(inventory_widget)
            # محاولة فتح نافذة إضافة منتج مباشرة
            if hasattr(inventory_widget, 'add_product'):
                inventory_widget.add_product()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إضافة المنتج: {str(e)}")

    def show_last_invoice(self):
        """عرض آخر فاتورة تم إنشاؤها"""
        try:
            from sqlalchemy.orm import Session
            from database.models import Transaction, TransactionType

            with Session(self.engine) as session:
                last_invoice = session.query(Transaction).filter(
                    Transaction.type == TransactionType.SALE
                ).order_by(Transaction.id.desc()).first()

                if last_invoice:
                    sales_widget = SalesWidget(self.engine)
                    self.setCentralWidget(sales_widget)
                    # محاولة عرض الفاتورة
                    if hasattr(sales_widget, 'load_invoice'):
                        sales_widget.load_invoice(last_invoice.id)
                else:
                    QMessageBox.information(self, "معلومات", "لا توجد فواتير في النظام")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض آخر فاتورة: {str(e)}")

    def show_sales_report(self):
        """عرض تقرير المبيعات اليومي"""
        try:
            # التأكد من وجود التبويبات الثابتة
            if not hasattr(self, 'main_tabs'):
                self.setup_main_interface()

            # إنشاء تبويب جديد للتقارير المحسنة
            reports_widget = EnhancedReportsWidget(self.engine)
            self.add_or_switch_tab("📊 التقارير المحسنة", reports_widget, "📊")

            # التبديل إلى تبويب تقارير المبيعات
            reports_widget.tabs.setCurrentIndex(1)  # تبويب المبيعات

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التقارير:\n{str(e)}")
            print(f"Error in show_sales_report: {e}")

    def show_advanced_reports(self):
        """عرض التقارير التحليلية الآمنة"""
        try:
            # استخدام التقارير التحليلية الآمنة
            from gui.safe_advanced_reports import SafeAdvancedReportsWidget

            # التأكد من وجود التبويبات الثابتة
            if not hasattr(self, 'main_tabs'):
                self.setup_main_interface()

            # إنشاء تبويب جديد للتقارير التحليلية الآمنة
            reports_widget = SafeAdvancedReportsWidget(self.engine)
            self.add_or_switch_tab("📊 التقارير التحليلية", reports_widget, "📊")

        except ImportError as e:
            QMessageBox.warning(
                self,
                "تحذير",
                "التقارير التحليلية غير متوفرة حالياً\n\n"
                "تأكد من وجود جميع الملفات المطلوبة"
            )
            print(f"Import error in show_advanced_reports: {e}")

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء عرض التقارير التحليلية:\n{str(e)}\n\n"
                "يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني"
            )
            print(f"Error in show_advanced_reports: {e}")

    def show_reports(self):
        """عرض التقارير العادية"""
        try:
            # التأكد من وجود التبويبات الثابتة
            if not hasattr(self, 'main_tabs'):
                self.setup_main_interface()

            # إنشاء تبويب جديد للتقارير المحسنة
            reports_widget = EnhancedReportsWidget(self.engine)
            self.add_or_switch_tab("📊 التقارير", reports_widget, "📊")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التقارير:\n{str(e)}")
            print(f"Error in show_reports: {e}")

    @audit_action("عرض العملاء والموردين")
    def show_contacts(self, *args):
        """عرض العملاء والموردين مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_العملاء"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                # إنشاء تبويب جديد للعملاء والموردين أو استخدام تبويب موجود
                self.add_or_switch_tab("العملاء والموردين", ContactsWidget(self.engine), "👥")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض العملاء والموردين: {str(e)}")
            print(f"Error in show_contacts: {e}")

    def show_financial_reports(self):
        """عرض التقارير المالية"""
        try:
            # التأكد من وجود التبويبات الثابتة
            if not hasattr(self, 'main_tabs'):
                self.setup_main_interface()

            # إنشاء تبويب جديد للتقارير المحسنة
            reports_widget = EnhancedReportsWidget(self.engine)
            self.add_or_switch_tab("💰 التقارير المالية", reports_widget, "💰")

            # التبديل إلى تبويب التقارير المالية
            reports_widget.tabs.setCurrentIndex(0)  # تبويب التقارير المالية

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التقارير المالية:\n{str(e)}")
            print(f"Error in show_financial_reports: {e}")

    def show_notifications_window(self):
        """عرض نافذة التنبيهات"""
        # التأكد من وجود التبويبات الثابتة
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        if hasattr(self, 'notification_system') and self.engine:
            from .notifications import NotificationsWidget
            # إنشاء تبويب جديد للتنبيهات أو استخدام تبويب موجود
            notifications_widget = NotificationsWidget(self.engine)
            self.add_or_switch_tab("التنبيهات", notifications_widget, "🔔")
        else:
            QMessageBox.warning(self, "تنبيه", "نظام التنبيهات غير متاح حالياً")

    def show_notifications_settings(self):
        """عرض إعدادات التنبيهات"""
        if hasattr(self, 'notification_system'):
            self.notification_system.show_settings()
        else:
            QMessageBox.warning(self, "تنبيه", "نظام التنبيهات غير متاح حالياً")

    @audit_action("عرض المبيعات")
    def show_sales(self, *args):
        """عرض واجهة المبيعات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المبيعات"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                # تحميل محتوى المبيعات في التبويب الثاني
                self.show_sales_content()

                # التأكد من أن التبويب الثاني مفعل
                self.main_tabs.setCurrentIndex(1)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المبيعات: {str(e)}")
            print(f"Error in show_sales: {e}")

    @audit_action("عرض الفواتير")
    def show_invoices_view(self, *args):
        """عرض صفحة الفواتير المحفوظة مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المبيعات"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                # إنشاء تبويب جديد للفواتير أو استخدام تبويب موجود
                self.add_or_switch_tab("عرض الفواتير", InvoicesViewWidget(self.engine), "📋")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض الفواتير: {str(e)}")
            print(f"Error in show_invoices_view: {e}")

    @audit_action("عرض المشتريات")
    def show_purchases(self, *args):
        """عرض واجهة المشتريات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المشتريات"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                # إنشاء تبويب جديد للمشتريات أو استخدام تبويب موجود
                self.add_or_switch_tab("المشتريات", PurchasesWidget(self.engine), "🛒")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المشتريات: {str(e)}")
            print(f"Error in show_purchases: {e}")

    @audit_action("عرض مرتجع المبيعات")
    def show_sales_returns(self, *args):
        """عرض واجهة مرتجع المبيعات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المبيعات"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                from gui.sales_returns import SalesReturnsWidget
                # إنشاء تبويب جديد لمرتجع المبيعات أو استخدام تبويب موجود
                self.add_or_switch_tab("مرتجع المبيعات", SalesReturnsWidget(self.engine), "🔄")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض مرتجع المبيعات: {str(e)}")
            print(f"Error in show_sales_returns: {e}")

    @audit_action("عرض مرتجع المشتريات")
    def show_purchase_returns(self, *args):
        """عرض واجهة مرتجع المشتريات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المشتريات"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                from gui.purchase_returns import PurchaseReturnsWidget
                # إنشاء تبويب جديد لمرتجع المشتريات أو استخدام تبويب موجود
                self.add_or_switch_tab("مرتجع المشتريات", PurchaseReturnsWidget(self.engine), "↩️")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض مرتجع المشتريات: {str(e)}")
            print(f"Error in show_purchase_returns: {e}")

    @audit_action("عرض فواتير المرتجعات")
    def show_returns_view(self, *args):
        """عرض واجهة فواتير المرتجعات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المبيعات") or self.check_permission("إدارة_المشتريات"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                from gui.returns_view import ReturnsViewWidget
                # إنشاء تبويب جديد لفواتير المرتجعات أو استخدام تبويب موجود
                self.add_or_switch_tab("فواتير المرتجعات", ReturnsViewWidget(self.engine), "📑")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض فواتير المرتجعات: {str(e)}")
            print(f"Error in show_returns_view: {e}")

    def show_customer_statement(self):
        """عرض كشف حساب العملاء"""
        # التأكد من وجود التبويبات الثابتة
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        if self.engine:
            # البحث عن التبويب إذا كان موجوداً
            tab_found = False
            for i in range(self.main_tabs.count()):
                if self.main_tabs.tabText(i).replace("👥 ", "") == "العملاء والموردين":
                    # التبويب موجود، قم بالتبديل إليه وتحديث التبويب الداخلي
                    self.main_tabs.setCurrentIndex(i)
                    # الحصول على widget الموجود وتحديث التبويب الداخلي
                    current_widget = self.main_tabs.widget(i)
                    if current_widget and current_widget.layout():
                        # البحث عن ContactsWidget داخل التخطيط
                        for j in range(current_widget.layout().count()):
                            item = current_widget.layout().itemAt(j)
                            if item and item.widget():
                                content_widget = item.widget()
                                if content_widget.layout():
                                    for k in range(content_widget.layout().count()):
                                        sub_item = content_widget.layout().itemAt(k)
                                        if sub_item and sub_item.widget():
                                            widget = sub_item.widget()
                                            if hasattr(widget, 'tab_widget'):
                                                widget.tab_widget.setCurrentIndex(0)  # تبويب العملاء
                                                tab_found = True
                                                break
                                if tab_found:
                                    break
                    if tab_found:
                        return

            # إذا لم يوجد التبويب، أنشئه
            contacts_widget = ContactsWidget(self.engine)
            contacts_widget.tab_widget.setCurrentIndex(0)  # تبويب العملاء
            # إنشاء تبويب جديد للعملاء أو استخدام تبويب موجود
            self.add_or_switch_tab("العملاء والموردين", contacts_widget, "👥", force_update=True)

    def show_supplier_statement(self):
        """عرض كشف حساب الموردين"""
        # التأكد من وجود التبويبات الثابتة
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        if self.engine:
            # البحث عن التبويب إذا كان موجوداً
            tab_found = False
            for i in range(self.main_tabs.count()):
                if self.main_tabs.tabText(i).replace("👥 ", "") == "العملاء والموردين":
                    # التبويب موجود، قم بالتبديل إليه وتحديث التبويب الداخلي
                    self.main_tabs.setCurrentIndex(i)
                    # الحصول على widget الموجود وتحديث التبويب الداخلي
                    current_widget = self.main_tabs.widget(i)
                    if current_widget and current_widget.layout():
                        # البحث عن ContactsWidget داخل التخطيط
                        for j in range(current_widget.layout().count()):
                            item = current_widget.layout().itemAt(j)
                            if item and item.widget():
                                content_widget = item.widget()
                                if content_widget.layout():
                                    for k in range(content_widget.layout().count()):
                                        sub_item = content_widget.layout().itemAt(k)
                                        if sub_item and sub_item.widget():
                                            widget = sub_item.widget()
                                            if hasattr(widget, 'tab_widget'):
                                                widget.tab_widget.setCurrentIndex(1)  # تبويب الموردين
                                                tab_found = True
                                                break
                                if tab_found:
                                    break
                    if tab_found:
                        return

            # إذا لم يوجد التبويب، أنشئه
            contacts_widget = ContactsWidget(self.engine)
            contacts_widget.tab_widget.setCurrentIndex(1)  # تبويب الموردين
            # إنشاء تبويب جديد للموردين أو استخدام تبويب موجود
            self.add_or_switch_tab("العملاء والموردين", contacts_widget, "👥", force_update=True)

    def show_financial_reports(self):
        """عرض التقارير المالية"""
        # التأكد من وجود التبويبات الثابتة
        if not hasattr(self, 'main_tabs'):
            self.setup_main_interface()

        if self.engine:
            try:
                # إنشاء تبويب جديد للتقارير المالية المحسنة
                reports_widget = EnhancedReportsWidget(self.engine)
                self.add_or_switch_tab("💰 التقارير المالية", reports_widget, "💰")

                # التبديل إلى تبويب التقارير المالية
                reports_widget.tabs.setCurrentIndex(0)  # تبويب التقارير المالية

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التقارير المالية:\n{str(e)}")
                print(f"Error in show_financial_reports_menu: {e}")

    @audit_action("عرض المخزون")
    def show_inventory(self, *args):
        """عرض واجهة المخزون مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المخزون"):
                # التأكد من وجود التبويبات الثابتة
                if not hasattr(self, 'main_tabs'):
                    self.setup_main_interface()

                # إنشاء تبويب جديد للمخزون أو استخدام تبويب موجود
                self.add_or_switch_tab("المخزون", InventoryWidget(self.engine), "📦")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المخزون: {str(e)}")
            print(f"Error in show_inventory: {e}")

    def show_easacc_importer(self):
        """عرض أداة استيراد البيانات من برنامج EasAcc"""
        try:
            if self.engine:
                from .easacc_importer import EasAccImporterDialog
                dialog = EasAccImporterDialog(self.engine, self)
                dialog.exec_()
            else:
                QMessageBox.warning(self, "تنبيه", "قاعدة البيانات غير متاحة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح أداة الاستيراد:\n{str(e)}")
            print(f"Error in show_easacc_importer: {e}")

    def show_backup_dialog(self):
        if self.engine:
            import sys
            import os
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج exe
                db_path = os.path.join(os.path.dirname(sys.executable), 'accounting.db')
            else:
                # إذا كان البرنامج من الكود المصدري
                db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'accounting.db')
            dialog = BackupDialog(db_path)
            dialog.exec_()

    def show_company_settings(self):
        """عرض نافذة إعدادات الشركة"""
        from utils.company_settings import show_company_settings_dialog
        
        # عرض نافذة إعدادات الشركة المحسنة
        result = show_company_settings_dialog(self)
        
        if result == QDialog.Accepted:
            # إعادة تحميل إعدادات الشركة
            self.load_company_settings()
            
            # تحديث الواجهة
            self.refresh_company_info()
            self.refresh_welcome_label()
            if hasattr(self, 'company_name_label'):
                self.company_name_label.setText(self.company_settings.get('company_name', 'شركة المحاسبة'))
            if hasattr(self, 'main_logo_label'):
                self.load_company_logo()
            QMessageBox.information(self, "تم التحديث", "تم تحديث إعدادات الشركة بنجاح!")

    def show_audit_log(self):
        """عرض نافذة سجل العمليات"""
        if self.check_permission("إدارة_المستخدمين"):
            dialog = AuditLogDialog(self.engine)
            dialog.exec_()
        else:
            QMessageBox.warning(self, "تنبيه", "لا تملك صلاحية الوصول لسجل العمليات")

    def show_app_settings(self):
        """عرض نافذة إعدادات التطبيق"""
        try:
            from gui.settings_dialog import SettingsDialog
            from utils.settings_manager import init_settings_manager
            from database.settings_models import init_settings_db

            # تهيئة نظام الإعدادات
            init_settings_db(self.engine)
            settings_manager = init_settings_manager(self.engine)

            # عرض نافذة الإعدادات
            dialog = SettingsDialog(self)
            dialog.settings_changed.connect(self.apply_settings)
            dialog.show_app_settings()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح الإعدادات:\n{str(e)}")
            print(f"Error in show_app_settings: {e}")

    def apply_settings(self):
        """تطبيق الإعدادات الجديدة"""
        try:
            from utils.settings_manager import get_settings_manager
            settings_manager = get_settings_manager()

            if settings_manager:
                # تطبيق إعدادات المظهر
                self.apply_theme_settings(settings_manager)

                # تطبيق إعدادات الخط
                self.apply_font_settings(settings_manager)

                # تطبيق الاختصارات الجديدة
                self.apply_shortcuts(settings_manager)

                # إعادة تحميل العملة في جميع النوافذ
                self.refresh_currency_display()

                QMessageBox.information(self, "تم التطبيق", "تم تطبيق الإعدادات الجديدة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تطبيق الإعدادات:\n{str(e)}")

    def apply_theme_settings(self, settings_manager):
        """تطبيق إعدادات المظهر"""
        theme = settings_manager.get('theme', 'light')

        if theme == 'dark':
            # تطبيق المظهر الداكن
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2C3E50;
                    color: #ECF0F1;
                }
                QMenuBar {
                    background-color: #34495E;
                    color: #ECF0F1;
                }
                QMenuBar::item:selected {
                    background-color: #3498DB;
                }
                QMenu {
                    background-color: #34495E;
                    color: #ECF0F1;
                    border: 1px solid #7F8C8D;
                }
                QMenu::item:selected {
                    background-color: #3498DB;
                }
            """)
        else:
            # تطبيق المظهر الفاتح (الافتراضي)
            self.setStyleSheet("")

    def apply_font_settings(self, settings_manager):
        """تطبيق إعدادات الخط"""
        font_size = settings_manager.get('font_size', 24)

        # تطبيق حجم الخط على التطبيق
        font = QFont()
        font.setPointSize(font_size)
        QApplication.instance().setFont(font)

    def apply_shortcuts(self, settings_manager):
        """تطبيق الاختصارات الجديدة"""
        try:
            # إزالة الاختصارات القديمة
            if hasattr(self, 'shortcuts'):
                for shortcut in self.shortcuts:
                    shortcut.setParent(None)

            # إنشاء الاختصارات الجديدة
            from PyQt5.QtWidgets import QShortcut
            from PyQt5.QtGui import QKeySequence

            self.shortcuts = []

            # اختصار إظهار/إخفاء المكسب
            profit_shortcut = settings_manager.get_shortcut('profit_toggle')
            if profit_shortcut:
                shortcut = QShortcut(QKeySequence(profit_shortcut), self)
                shortcut.activated.connect(self.toggle_profit_display)
                self.shortcuts.append(shortcut)

            # اختصار فاتورة جديدة
            new_invoice_shortcut = settings_manager.get_shortcut('new_invoice')
            if new_invoice_shortcut:
                shortcut = QShortcut(QKeySequence(new_invoice_shortcut), self)
                shortcut.activated.connect(self.show_sales)
                self.shortcuts.append(shortcut)

            # اختصار البحث
            search_shortcut = settings_manager.get_shortcut('search')
            if search_shortcut:
                shortcut = QShortcut(QKeySequence(search_shortcut), self)
                shortcut.activated.connect(self.focus_search)
                self.shortcuts.append(shortcut)

        except Exception as e:
            print(f"Error applying shortcuts: {e}")

    def toggle_profit_display(self):
        """تبديل عرض المكسب في النافذة الحالية"""
        try:
            current_widget = self.centralWidget()
            if hasattr(current_widget, 'toggle_profit_visibility'):
                current_widget.toggle_profit_visibility()
        except Exception as e:
            print(f"Error toggling profit display: {e}")

    def focus_search(self):
        """التركيز على حقل البحث في النافذة الحالية"""
        try:
            current_widget = self.centralWidget()
            if hasattr(current_widget, 'search_input'):
                current_widget.search_input.setFocus()
                current_widget.search_input.selectAll()
        except Exception as e:
            print(f"Error focusing search: {e}")

    def refresh_currency_display(self):
        """إعادة تحميل عرض العملة في جميع النوافذ"""
        try:
            # إعادة تحميل النافذة الحالية إذا كانت تحتوي على عرض للعملة
            current_widget = self.centralWidget()
            if hasattr(current_widget, 'refresh_currency'):
                current_widget.refresh_currency()
        except Exception as e:
            print(f"Error refreshing currency display: {e}")

    def closeEvent(self, event):
        """معالجة إغلاق التطبيق"""
        # حفظ الإعدادات وإغلاق نظام التنبيهات
        if hasattr(self, 'notification_system'):
            self.notification_system.quit_app()
        event.accept()

    def on_sidebar_enter(self, event):
        """عند دخول الماوس للشريط الجانبي"""
        # لا يعمل إذا كان البار مطفي أو مخفي
        if not self.sidebar_enabled or not self.sidebar_visible:
            return
        if hasattr(self, 'sidebar') and not self.sidebar_expanded:
            self.expand_sidebar()

    def on_sidebar_leave(self, event):
        """عند خروج الماوس من الشريط الجانبي"""
        # لا يعمل إذا كان البار مطفي أو مخفي
        if not self.sidebar_enabled or not self.sidebar_visible:
            return
        if hasattr(self, 'sidebar') and self.sidebar_expanded:
            self.collapse_sidebar()

    def expand_sidebar(self):
        """توسيع الشريط الجانبي"""
        self.sidebar_expanded = True

        # تشغيل الرسوم المتحركة
        self.sidebar_animation.setStartValue(110)
        self.sidebar_animation.setEndValue(280)
        self.sidebar_animation2.setStartValue(110)
        self.sidebar_animation2.setEndValue(280)

        self.sidebar_animation.start()
        self.sidebar_animation2.start()

        # إظهار النصوص بعد تأخير قصير
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(150, self.show_sidebar_texts)

    def collapse_sidebar(self):
        """طي الشريط الجانبي"""
        self.sidebar_expanded = False

        # إخفاء النصوص أولاً
        self.hide_sidebar_texts()

        # تشغيل الرسوم المتحركة بعد تأخير قصير
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, self.start_collapse_animation)

    def start_collapse_animation(self):
        """بدء رسوم الطي المتحركة"""
        self.sidebar_animation.setStartValue(280)
        self.sidebar_animation.setEndValue(110)
        self.sidebar_animation2.setStartValue(280)
        self.sidebar_animation2.setEndValue(110)

        self.sidebar_animation.start()
        self.sidebar_animation2.start()

    def show_sidebar_texts(self):
        """إظهار نصوص الشريط الجانبي"""
        try:
            if hasattr(self, 'sidebar_title') and self.sidebar_title:
                try:
                    self.sidebar_title.show()
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'version_label') and self.version_label:
                try:
                    self.version_label.show()
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'user_name_label') and self.user_name_label:
                try:
                    self.user_name_label.show()
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'logout_btn') and self.logout_btn:
                try:
                    # تغيير زر الخروج لإظهار النص
                    self.logout_btn.setText("🚪 خروج")
                    self.logout_btn.setFixedSize(100, 60)
                    # تحديث ستايل الزر لحجم خط النص
                    self.logout_btn.setStyleSheet("""
                        QPushButton {
                            background: rgba(255, 255, 255, 0.15);
                            color: white;
                            border: none;
                            border-radius: 10px;
                            padding: 8px;
                            font-size: 16px;
                            margin: 2px;
                        }
                        QPushButton:hover {
                            background: rgba(245, 101, 101, 0.8);
                        }
                    """)
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'nav_buttons') and self.nav_buttons:
                for btn in self.nav_buttons:
                    try:
                        if hasattr(btn, 'text_label') and btn.text_label:
                            try:
                                btn.text_label.show()
                            except RuntimeError:
                                pass  # العنصر محذوف
                    except:
                        pass
        except:
            pass

    def hide_sidebar_texts(self):
        """إخفاء نصوص الشريط الجانبي مع حماية من الأخطاء"""
        try:
            if hasattr(self, 'sidebar_title') and self.sidebar_title:
                try:
                    self.sidebar_title.hide()
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'version_label') and self.version_label:
                try:
                    self.version_label.hide()
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'user_name_label') and self.user_name_label:
                try:
                    self.user_name_label.hide()
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'logout_btn') and self.logout_btn:
                try:
                    # تغيير زر الخروج لإظهار الأيقونة فقط
                    self.logout_btn.setText("🚪")
                    self.logout_btn.setFixedSize(60, 60)
                    # إعادة ستايل الأيقونة
                    self.logout_btn.setStyleSheet("""
                        QPushButton {
                            background: rgba(255, 255, 255, 0.15);
                            color: white;
                            border: none;
                            border-radius: 10px;
                            padding: 8px;
                            font-size: 24px;
                            margin: 2px;
                        }
                        QPushButton:hover {
                            background: rgba(245, 101, 101, 0.8);
                        }
                    """)
                except RuntimeError:
                    pass  # العنصر محذوف
        except:
            pass

        try:
            if hasattr(self, 'nav_buttons') and self.nav_buttons:
                for btn in self.nav_buttons:
                    try:
                        if hasattr(btn, 'text_label') and btn.text_label:
                            try:
                                btn.text_label.hide()
                            except RuntimeError:
                                pass  # العنصر محذوف
                    except:
                        pass
        except:
            pass

    def setup_license_timer(self):
        """إعداد فحص دوري للترخيص"""
        if not LICENSE_SYSTEM_AVAILABLE:
            return

        # إنشاء مؤقت للفحص الدوري (كل 30 دقيقة)
        from PyQt5.QtCore import QTimer
        self.license_timer = QTimer()
        self.license_timer.timeout.connect(self.check_license_status)
        self.license_timer.start(30 * 60 * 1000)  # 30 دقيقة بالميلي ثانية

        # فحص أولي
        self.check_license_status()

    def check_license_status(self):
        """فحص حالة الترخيص"""
        if not LICENSE_SYSTEM_AVAILABLE:
            return

        try:
            license_manager = LicenseManager()
            status = license_manager.check_license()

            if not status["valid"]:
                # إيقاف المؤقت
                if hasattr(self, 'license_timer'):
                    self.license_timer.stop()

                # عرض نافذة انتهاء الصلاحية
                QMessageBox.critical(
                    self,
                    "انتهت صلاحية البرنامج",
                    "انتهت صلاحية الترخيص.\n\n"
                    "للتجديد، راسل:\n"
                    "📧 <EMAIL>\n\n"
                    "أرسل كود العميل ورقم الجهاز مع إثبات الدفع"
                )

                # إغلاق البرنامج
                self.close()
                QApplication.instance().quit()

            elif status.get("days_remaining", 0) <= 7:
                # تحذير قبل انتهاء الصلاحية
                days_remaining = status.get("days_remaining", 0)
                QMessageBox.warning(
                    self,
                    "تحذير انتهاء الترخيص",
                    f"سينتهي الترخيص خلال {days_remaining} أيام.\n\n"
                    "للتجديد، راسل:\n"
                    "📧 <EMAIL>\n\n"
                    "أرسل كود العميل ورقم الجهاز مع إثبات الدفع"
                )

        except Exception as e:
            print(f"خطأ في فحص الترخيص: {e}")

    def show_license_renewal(self):
        """عرض نافذة تجديد الترخيص"""
        if not LICENSE_SYSTEM_AVAILABLE:
            QMessageBox.warning(
                self,
                "تحذير",
                "نظام التراخيص غير متوفر"
            )
            return

        try:
            from license_ui import check_license_and_show_dialog

            # عرض نافذة تجديد الترخيص
            if check_license_and_show_dialog():
                QMessageBox.information(
                    self,
                    "تم التجديد",
                    "تم تجديد الترخيص بنجاح!"
                )
                # إعادة تشغيل فحص الترخيص
                if hasattr(self, 'license_timer'):
                    self.license_timer.start(30 * 60 * 1000)
            else:
                QMessageBox.warning(
                    self,
                    "فشل التجديد",
                    "لم يتم تجديد الترخيص"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تجديد الترخيص:\n{str(e)}"
            )

    def show_purchase_invoices_view(self, *args):
        self.add_or_switch_tab(
            "عرض فواتير المشتريات",
            PurchaseInvoicesViewWidget(self.engine),
            icon="🧾"
        )

    def load_company_settings(self):
        """تحميل إعدادات الشركة من ملف company_settings.json دائماً"""
        import json, os, sys

        # تحديد المسار الصحيح للملف حسب نوع التشغيل
        if getattr(sys, 'frozen', False):
            # إذا كان البرنامج مصدر (PyInstaller)
            base_dir = os.path.dirname(sys.executable)
            settings_file = os.path.join(base_dir, "company_settings.json")
        else:
            # إذا كان البرنامج يعمل من الكود المصدري
            settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")

        if os.path.exists(settings_file):
            try:
                with open(settings_file, "r", encoding="utf-8") as f:
                    self.company_settings = json.load(f)
            except Exception as e:
                print(f"خطأ في تحميل إعدادات الشركة: {e}")
                self.company_settings = {}
        else:
            self.company_settings = {}

    def refresh_company_info(self):
        """تحديث اسم الشركة واللوجو في الواجهة والطباعة بعد أي تعديل"""
        self.load_company_settings()

        # تحديث اسم الشركة في العنوان
        if hasattr(self, 'company_settings') and self.company_settings.get('company_name'):
            self.setWindowTitle(self.company_settings['company_name'] + " - نظام المحاسبة العصري")

        # تحديث اسم الشركة في الواجهة الرئيسية
        if hasattr(self, 'company_name_label'):
            company_name = self.company_settings.get('company_name', 'شركة المحاسبة')
            self.company_name_label.setText(company_name)

        # تحديث اللوجو في الواجهة الرئيسية
        if hasattr(self, 'main_logo_label'):
            self.load_company_logo()

        # تحديث اللوجو في الشريط الجانبي إذا كان موجوداً
        if hasattr(self, 'logo_label'):
            self.load_main_brand_logo()

        # إجبار إعادة رسم الواجهة
        self.update()
        if hasattr(self, 'central_widget'):
            self.central_widget.update()

    def refresh_welcome_label(self):
        """تحديث نص الترحيب في شريط العنوان فقط دون إعادة رسم الصفحة بالكامل"""
        if hasattr(self, 'welcome_label'):
            if self.current_user:
                # استخدام full_name بدلاً من name
                user_full_name = getattr(self.current_user, 'full_name', None)
                if user_full_name and user_full_name.strip():
                    welcome_text = f"مرحباً {user_full_name}"
                else:
                    # إذا لم يكن هناك full_name، استخدم username
                    welcome_text = f"مرحباً {self.current_user.username}"
            else:
                welcome_text = "مرحباً المستخدم"
            self.welcome_label.setText(welcome_text)

    def refresh_user_data(self):
        """تحديث بيانات المستخدم من قاعدة البيانات"""
        if not self.current_user:
            return
            
        try:
            from sqlalchemy.orm import Session
            with Session(self.engine) as session:
                updated_user = session.query(User).filter(User.username == self.current_user.username).first()
                if updated_user:
                    self.current_user = updated_user
                    self.refresh_welcome_label()
        except Exception as e:
            print(f"خطأ في تحديث بيانات المستخدم: {e}")

    def update_welcome_message(self, new_name):
        """تحديث الرسالة الترحيبية عند تغيير الاسم الكامل"""
        if hasattr(self, 'welcome_label'):
            if new_name and new_name.strip():
                self.welcome_label.setText(f"مرحباً {new_name}")
            else:
                # إذا كان الاسم فارغ، استخدم username
                username = getattr(self.current_user, 'username', 'المستخدم')
                self.welcome_label.setText(f"مرحباً {username}")

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())