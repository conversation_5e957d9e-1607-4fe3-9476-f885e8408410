#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المحسن - بدون matplotlib
Enhanced Reports System - Without matplotlib
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QComboBox, QLabel, QDateEdit, QTableWidget,
                             QTableWidgetItem, QFileDialog, QMessageBox, QFrame,
                             QHeaderView, QGridLayout, QDialog, QGroupBox, QFormLayout,
                             QProgressBar, QSplitter, QScrollArea, QTabWidget)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QPainter, QFont, QPalette, QPen
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QBarSeries, QBarSet, 
                          QValueAxis, QBarCategoryAxis, QPieSeries, QAreaSeries,
                          QScatterSeries, QSplineSeries)
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, text
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from utils.dialog_utils import make_dialog_resizable
from utils.currency_formatter import format_currency, format_number
from utils.theme_manager import theme_manager
from utils.performance_optimizer import DatabaseOptimizer, QueryOptimizer, cache_manager
import xlsxwriter
from datetime import datetime, timedelta
import json
import os


class DataLoader(QThread):
    """فئة لتحميل البيانات في خيط منفصل لتحسين الأداء"""
    data_loaded = pyqtSignal(dict)
    progress_updated = pyqtSignal(int)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, engine, start_date, end_date, report_type="financial"):
        super().__init__()
        self.engine = engine
        self.start_date = start_date
        self.end_date = end_date
        self.report_type = report_type
        
    def run(self):
        try:
            data = {}
            
            if self.report_type == "financial":
                data = self.load_financial_data()
            elif self.report_type == "sales":
                data = self.load_sales_data()
            elif self.report_type == "inventory":
                data = self.load_inventory_data()
                
            self.data_loaded.emit(data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def load_financial_data(self):
        """تحميل البيانات المالية محسن"""
        # فحص التخزين المؤقت أولاً
        cache_key = f"financial_data_{self.start_date}_{self.end_date}"
        cached_data = cache_manager.get(cache_key)
        if cached_data:
            self.progress_updated.emit(100)
            return cached_data

        with Session(self.engine) as session:
            self.progress_updated.emit(10)

            # استعلام محسن واحد للحصول على جميع البيانات
            financial_data = session.execute(text("""
                SELECT
                    DATE(date) as transaction_date,
                    type,
                    SUM(total_amount) as total
                FROM transactions
                WHERE date BETWEEN :start_date AND :end_date
                    AND type IN ('بيع', 'شراء', 'مصروف')
                GROUP BY DATE(date), type
                ORDER BY DATE(date)
            """), {
                'start_date': self.start_date,
                'end_date': self.end_date
            }).fetchall()

            self.progress_updated.emit(50)

            # تنظيم البيانات
            sales_data = []
            purchases_data = []
            expenses_data = []

            for row in financial_data:
                date = row.transaction_date
                total = float(row.total)

                if row.type == 'بيع':
                    sales_data.append((date, total))
                elif row.type == 'شراء':
                    purchases_data.append((date, total))
                elif row.type == 'مصروف':
                    expenses_data.append((date, total))

            self.progress_updated.emit(80)

            # حساب الأرباح
            sales_dict = {date: total for date, total in sales_data}
            purchases_dict = {date: total for date, total in purchases_data}

            profit_data = []
            for date in sales_dict.keys():
                purchase_total = purchases_dict.get(date, 0)
                profit = sales_dict[date] - purchase_total
                profit_data.append((date, profit))

            self.progress_updated.emit(90)

            result = {
                'sales': sales_data,
                'purchases': purchases_data,
                'expenses': expenses_data,
                'profits': profit_data
            }

            # حفظ في التخزين المؤقت
            cache_manager.set(cache_key, result)

            return result
    
    def load_sales_data(self):
        """تحميل بيانات المبيعات محسن"""
        # فحص التخزين المؤقت
        cache_key = f"sales_data_{self.start_date}_{self.end_date}"
        cached_data = cache_manager.get(cache_key)
        if cached_data:
            return cached_data

        with Session(self.engine) as session:
            # استعلام محسن لأفضل المنتجات
            top_products = session.execute(text("""
                SELECT
                    p.name,
                    SUM(ti.quantity) as total_quantity,
                    SUM(ti.quantity * ti.unit_price) as total_value,
                    COUNT(DISTINCT t.id) as transaction_count
                FROM products p
                JOIN transaction_items ti ON p.id = ti.product_id
                JOIN transactions t ON ti.transaction_id = t.id
                WHERE t.type = 'بيع'
                    AND t.date BETWEEN :start_date AND :end_date
                    AND p.is_active = 1
                GROUP BY p.id, p.name
                ORDER BY total_quantity DESC
                LIMIT 10
            """), {
                'start_date': self.start_date,
                'end_date': self.end_date
            }).fetchall()

            result = {
                'top_products': [(p.name, float(p.total_quantity), float(p.total_value)) for p in top_products]
            }

            # حفظ في التخزين المؤقت
            cache_manager.set(cache_key, result)

            return result
    
    def load_inventory_data(self):
        """تحميل بيانات المخزون محسن"""
        # فحص التخزين المؤقت
        cache_key = f"inventory_data_{datetime.now().strftime('%Y%m%d_%H')}"  # تحديث كل ساعة
        cached_data = cache_manager.get(cache_key)
        if cached_data:
            return cached_data

        with Session(self.engine) as session:
            # استعلام محسن للمنتجات منخفضة المخزون
            low_stock = session.execute(text("""
                SELECT
                    name,
                    code,
                    quantity,
                    min_quantity,
                    category,
                    (min_quantity - quantity) as shortage
                FROM products
                WHERE quantity <= min_quantity
                    AND is_active = 1
                ORDER BY (min_quantity - quantity) DESC
                LIMIT 50
            """)).fetchall()

            result = {
                'low_stock': [(p.name, p.quantity, p.min_quantity) for p in low_stock]
            }

            # حفظ في التخزين المؤقت
            cache_manager.set(cache_key, result)

            return result


class EnhancedReportsWidget(QWidget):
    """واجهة التقارير المحسنة"""

    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.data_loader = None
        self.charts = {}
        self.current_data = {}
        self.db_optimizer = DatabaseOptimizer(engine)
        self.query_optimizer = QueryOptimizer(engine)

        # تحسين قاعدة البيانات عند البداية
        QTimer.singleShot(1000, self.optimize_database)

        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # شريط التحكم العلوي
        control_frame = self.create_control_frame()
        layout.addWidget(control_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #0d6efd;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # تبويبات التقارير
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
            QTabBar::tab {
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                background-color: #f8f9fa;
            }
            QTabBar::tab:selected {
                background-color: #0d6efd;
                color: white;
            }
        """)
        
        # تبويب التقارير المالية
        self.financial_tab = self.create_financial_tab()
        self.tabs.addTab(self.financial_tab, "📊 التقارير المالية")
        
        # تبويب تقارير المبيعات
        self.sales_tab = self.create_sales_tab()
        self.tabs.addTab(self.sales_tab, "💰 تقارير المبيعات")
        
        # تبويب تقارير المخزون
        self.inventory_tab = self.create_inventory_tab()
        self.tabs.addTab(self.inventory_tab, "📦 تقارير المخزون")
        
        layout.addWidget(self.tabs)
        
        # تحديث البيانات الأولي مع تأخير للسماح بتحميل الواجهة
        QTimer.singleShot(1500, self.load_financial_data)

        # تنظيف التخزين المؤقت دورياً
        self.cache_cleanup_timer = QTimer()
        self.cache_cleanup_timer.timeout.connect(self.cleanup_cache)
        self.cache_cleanup_timer.start(600000)  # كل 10 دقائق
    
    def create_control_frame(self):
        """إنشاء شريط التحكم"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        layout = QHBoxLayout()
        frame.setLayout(layout)
        
        # إطار الفترة الزمنية
        period_group = QGroupBox("📅 الفترة الزمنية")
        period_layout = QFormLayout()
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم", "الأسبوع", "الشهر", "الربع السنوي", "السنة", "مخصص"
        ])
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        period_layout.addRow("عرض بيانات:", self.period_combo)
        
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.end_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.end_date.setCalendarPopup(True)
        
        period_layout.addRow("من:", self.start_date)
        period_layout.addRow("إلى:", self.end_date)
        period_group.setLayout(period_layout)
        layout.addWidget(period_group)
        
        # أزرار التحكم
        buttons_group = QGroupBox("🔧 العمليات")
        buttons_layout = QVBoxLayout()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_current_tab)
        refresh_btn.setStyleSheet(self.get_button_style("#0d6efd", "#0b5ed7"))
        
        export_btn = QPushButton("📤 تصدير Excel")
        export_btn.clicked.connect(self.export_to_excel)
        export_btn.setStyleSheet(self.get_button_style("#198754", "#157347"))
        
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(export_btn)
        buttons_group.setLayout(buttons_layout)
        layout.addWidget(buttons_group)
        
        return frame
    
    def get_button_style(self, bg_color, hover_color):
        """الحصول على تصميم الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                padding-top: 9px;
                padding-left: 17px;
            }}
        """

    def create_financial_tab(self):
        """إنشاء تبويب التقارير المالية"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # منطقة الرسوم البيانية
        charts_splitter = QSplitter(Qt.Horizontal)

        # العمود الأيسر - الرسوم البيانية الرئيسية
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        left_widget.setLayout(left_layout)

        # رسم المبيعات والمشتريات
        self.sales_purchases_chart = self.create_sales_purchases_chart()
        left_layout.addWidget(self.sales_purchases_chart)

        # رسم الأرباح
        self.profit_chart = self.create_profit_chart()
        left_layout.addWidget(self.profit_chart)

        # العمود الأيمن - الإحصائيات
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        right_widget.setLayout(right_layout)

        # إحصائيات سريعة
        self.stats_widget = self.create_stats_widget()
        right_layout.addWidget(self.stats_widget)

        charts_splitter.addWidget(left_widget)
        charts_splitter.addWidget(right_widget)
        charts_splitter.setSizes([700, 300])

        layout.addWidget(charts_splitter)
        return widget

    def create_sales_tab(self):
        """إنشاء تبويب تقارير المبيعات"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # أفضل المنتجات مبيعاً
        self.top_products_chart = self.create_top_products_chart()
        layout.addWidget(self.top_products_chart)

        # جدول تفاصيل المبيعات
        self.sales_table = self.create_sales_table()
        layout.addWidget(self.sales_table)

        return widget

    def create_inventory_tab(self):
        """إنشاء تبويب تقارير المخزون"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # تحذيرات المخزون
        warning_frame = QFrame()
        warning_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 10px;
                margin: 5px;
            }
        """)
        warning_layout = QVBoxLayout()
        warning_frame.setLayout(warning_layout)

        warning_label = QLabel("⚠️ تحذيرات المخزون")
        warning_label.setFont(QFont("Arial", 12, QFont.Bold))
        warning_layout.addWidget(warning_label)

        self.low_stock_table = self.create_low_stock_table()
        warning_layout.addWidget(self.low_stock_table)

        layout.addWidget(warning_frame)

        # رسم بياني للمخزون
        self.inventory_chart = self.create_inventory_chart()
        layout.addWidget(self.inventory_chart)

        return widget

    def create_sales_purchases_chart(self):
        """إنشاء رسم المبيعات والمشتريات"""
        chart = QChart()
        chart.setTitle("📈 المبيعات والمشتريات")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # إنشاء السلاسل
        sales_series = QLineSeries()
        sales_series.setName("المبيعات")
        sales_series.setPen(QPen(QColor("#28a745"), 3))

        purchases_series = QLineSeries()
        purchases_series.setName("المشتريات")
        purchases_series.setPen(QPen(QColor("#dc3545"), 3))

        chart.addSeries(sales_series)
        chart.addSeries(purchases_series)

        # إعداد المحاور
        axis_x = QBarCategoryAxis()
        axis_y = QValueAxis()

        chart.addAxis(axis_x, Qt.AlignBottom)
        chart.addAxis(axis_y, Qt.AlignLeft)

        sales_series.attachAxis(axis_x)
        sales_series.attachAxis(axis_y)
        purchases_series.attachAxis(axis_x)
        purchases_series.attachAxis(axis_y)

        # حفظ المراجع
        self.charts['sales_purchases'] = {
            'chart': chart,
            'sales_series': sales_series,
            'purchases_series': purchases_series,
            'axis_x': axis_x,
            'axis_y': axis_y
        }

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        return chart_view

    def create_profit_chart(self):
        """إنشاء رسم الأرباح"""
        chart = QChart()
        chart.setTitle("💰 الأرباح")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة الأرباح
        profit_series = QAreaSeries()
        profit_series.setName("الأرباح")
        profit_series.setColor(QColor("#17a2b8"))

        chart.addSeries(profit_series)

        # إعداد المحاور
        axis_x = QBarCategoryAxis()
        axis_y = QValueAxis()

        chart.addAxis(axis_x, Qt.AlignBottom)
        chart.addAxis(axis_y, Qt.AlignLeft)

        profit_series.attachAxis(axis_x)
        profit_series.attachAxis(axis_y)

        # حفظ المراجع
        self.charts['profit'] = {
            'chart': chart,
            'series': profit_series,
            'axis_x': axis_x,
            'axis_y': axis_y
        }

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        return chart_view

    def create_top_products_chart(self):
        """إنشاء رسم أفضل المنتجات"""
        chart = QChart()
        chart.setTitle("🏆 أفضل المنتجات مبيعاً")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة دائرية
        series = QPieSeries()
        chart.addSeries(series)

        # حفظ المراجع
        self.charts['top_products'] = {
            'chart': chart,
            'series': series
        }

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        return chart_view

    def create_stats_widget(self):
        """إنشاء ودجت الإحصائيات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        layout = QVBoxLayout()
        frame.setLayout(layout)

        title = QLabel("📊 إحصائيات سريعة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # إحصائيات
        self.total_sales_label = QLabel("إجمالي المبيعات: --")
        self.total_purchases_label = QLabel("إجمالي المشتريات: --")
        self.total_profit_label = QLabel("إجمالي الأرباح: --")
        self.avg_daily_sales_label = QLabel("متوسط المبيعات اليومية: --")

        for label in [self.total_sales_label, self.total_purchases_label,
                     self.total_profit_label, self.avg_daily_sales_label]:
            label.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    margin: 2px;
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    font-size: 12px;
                }
            """)
            layout.addWidget(label)

        return frame

    def create_sales_table(self):
        """إنشاء جدول تفاصيل المبيعات"""
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي"])

        # تنسيق الجدول
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #0d6efd;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        return table

    def create_low_stock_table(self):
        """إنشاء جدول المنتجات منخفضة المخزون"""
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["المنتج", "الكمية الحالية", "الحد الأدنى"])

        # تنسيق الجدول
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ffeaa7;
                background-color: #fffbf0;
                alternate-background-color: #fff3cd;
            }
            QHeaderView::section {
                background-color: #fd7e14;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        return table

    def create_inventory_chart(self):
        """إنشاء رسم بياني للمخزون"""
        chart = QChart()
        chart.setTitle("📦 حالة المخزون")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة أعمدة
        series = QBarSeries()

        # مجموعات البيانات
        current_stock = QBarSet("المخزون الحالي")
        min_stock = QBarSet("الحد الأدنى")

        current_stock.setColor(QColor("#28a745"))
        min_stock.setColor(QColor("#ffc107"))

        series.append(current_stock)
        series.append(min_stock)

        chart.addSeries(series)

        # إعداد المحاور
        axis_x = QBarCategoryAxis()
        axis_y = QValueAxis()

        chart.addAxis(axis_x, Qt.AlignBottom)
        chart.addAxis(axis_y, Qt.AlignLeft)

        series.attachAxis(axis_x)
        series.attachAxis(axis_y)

        # حفظ المراجع
        self.charts['inventory'] = {
            'chart': chart,
            'series': series,
            'current_stock': current_stock,
            'min_stock': min_stock,
            'axis_x': axis_x,
            'axis_y': axis_y
        }

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        return chart_view

    def on_period_changed(self):
        """عند تغيير الفترة الزمنية"""
        period = self.period_combo.currentText()
        today = datetime.now()

        if period == "اليوم":
            self.start_date.setDate(QDate.currentDate())
            self.end_date.setDate(QDate.currentDate())
        elif period == "الأسبوع":
            self.start_date.setDate(QDate.currentDate().addDays(-7))
            self.end_date.setDate(QDate.currentDate())
        elif period == "الشهر":
            self.start_date.setDate(QDate.currentDate().addMonths(-1))
            self.end_date.setDate(QDate.currentDate())
        elif period == "الربع السنوي":
            self.start_date.setDate(QDate.currentDate().addMonths(-3))
            self.end_date.setDate(QDate.currentDate())
        elif period == "السنة":
            self.start_date.setDate(QDate.currentDate().addYears(-1))
            self.end_date.setDate(QDate.currentDate())

    def refresh_current_tab(self):
        """تحديث التبويب الحالي"""
        current_index = self.tabs.currentIndex()

        if current_index == 0:  # التقارير المالية
            self.load_financial_data()
        elif current_index == 1:  # تقارير المبيعات
            self.load_sales_data()
        elif current_index == 2:  # تقارير المخزون
            self.load_inventory_data()

    def load_financial_data(self):
        """تحميل البيانات المالية"""
        if self.data_loader and self.data_loader.isRunning():
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()

        self.data_loader = DataLoader(self.engine, start_date, end_date, "financial")
        self.data_loader.data_loaded.connect(self.on_financial_data_loaded)
        self.data_loader.progress_updated.connect(self.progress_bar.setValue)
        self.data_loader.error_occurred.connect(self.on_data_error)
        self.data_loader.finished.connect(lambda: self.progress_bar.setVisible(False))
        self.data_loader.start()

    def load_sales_data(self):
        """تحميل بيانات المبيعات"""
        if self.data_loader and self.data_loader.isRunning():
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()

        self.data_loader = DataLoader(self.engine, start_date, end_date, "sales")
        self.data_loader.data_loaded.connect(self.on_sales_data_loaded)
        self.data_loader.progress_updated.connect(self.progress_bar.setValue)
        self.data_loader.error_occurred.connect(self.on_data_error)
        self.data_loader.finished.connect(lambda: self.progress_bar.setVisible(False))
        self.data_loader.start()

    def load_inventory_data(self):
        """تحميل بيانات المخزون"""
        if self.data_loader and self.data_loader.isRunning():
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()

        self.data_loader = DataLoader(self.engine, start_date, end_date, "inventory")
        self.data_loader.data_loaded.connect(self.on_inventory_data_loaded)
        self.data_loader.progress_updated.connect(self.progress_bar.setValue)
        self.data_loader.error_occurred.connect(self.on_data_error)
        self.data_loader.finished.connect(lambda: self.progress_bar.setVisible(False))
        self.data_loader.start()

    def on_financial_data_loaded(self, data):
        """عند تحميل البيانات المالية"""
        self.current_data['financial'] = data
        self.update_financial_charts(data)
        self.update_financial_stats(data)

    def on_sales_data_loaded(self, data):
        """عند تحميل بيانات المبيعات"""
        self.current_data['sales'] = data
        self.update_sales_charts(data)
        self.update_sales_table(data)

    def on_inventory_data_loaded(self, data):
        """عند تحميل بيانات المخزون"""
        self.current_data['inventory'] = data
        self.update_inventory_charts(data)
        self.update_inventory_table(data)

    def on_data_error(self, error_message):
        """عند حدوث خطأ في تحميل البيانات"""
        QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{error_message}")
        self.progress_bar.setVisible(False)

    def update_financial_charts(self, data):
        """تحديث الرسوم البيانية المالية"""
        if 'sales_purchases' not in self.charts:
            return

        chart_data = self.charts['sales_purchases']

        # مسح البيانات السابقة
        chart_data['sales_series'].clear()
        chart_data['purchases_series'].clear()

        # إعداد البيانات
        dates = []
        sales_values = []
        purchases_values = []

        # معالجة بيانات المبيعات
        sales_dict = {str(date): value for date, value in data.get('sales', [])}
        purchases_dict = {str(date): value for date, value in data.get('purchases', [])}

        # الحصول على جميع التواريخ
        all_dates = set(sales_dict.keys()) | set(purchases_dict.keys())
        all_dates = sorted(all_dates)

        for date in all_dates:
            dates.append(date)
            sales_values.append(sales_dict.get(date, 0))
            purchases_values.append(purchases_dict.get(date, 0))

        # إضافة البيانات للرسم
        for i, date in enumerate(dates):
            chart_data['sales_series'].append(i, sales_values[i])
            chart_data['purchases_series'].append(i, purchases_values[i])

        # تحديث المحاور
        chart_data['axis_x'].clear()
        chart_data['axis_x'].append(dates)

        max_value = max(max(sales_values, default=0), max(purchases_values, default=0))
        chart_data['axis_y'].setRange(0, max_value * 1.1)

        # تحديث رسم الأرباح
        if 'profit' in self.charts:
            profit_chart = self.charts['profit']
            profit_series = QLineSeries()

            for date, profit in data.get('profits', []):
                profit_series.append(len(profit_series), profit)

            profit_chart['series'].setUpperSeries(profit_series)

    def update_financial_stats(self, data):
        """تحديث الإحصائيات المالية"""
        # حساب الإجماليات
        total_sales = sum(value for _, value in data.get('sales', []))
        total_purchases = sum(value for _, value in data.get('purchases', []))
        total_profit = sum(profit for _, profit in data.get('profits', []))

        # حساب المتوسطات
        days_count = len(data.get('sales', []))
        avg_daily_sales = total_sales / days_count if days_count > 0 else 0

        # تحديث التسميات
        self.total_sales_label.setText(f"إجمالي المبيعات: {format_currency(total_sales)}")
        self.total_purchases_label.setText(f"إجمالي المشتريات: {format_currency(total_purchases)}")
        self.total_profit_label.setText(f"إجمالي الأرباح: {format_currency(total_profit)}")
        self.avg_daily_sales_label.setText(f"متوسط المبيعات اليومية: {format_currency(avg_daily_sales)}")

    def update_sales_charts(self, data):
        """تحديث رسوم المبيعات"""
        if 'top_products' not in self.charts:
            return

        chart_data = self.charts['top_products']
        series = chart_data['series']

        # مسح البيانات السابقة
        series.clear()

        # إضافة البيانات الجديدة
        for name, quantity, value in data.get('top_products', []):
            series.append(name, quantity)

    def update_sales_table(self, data):
        """تحديث جدول المبيعات"""
        table = self.sales_table
        products = data.get('top_products', [])

        table.setRowCount(len(products))

        for row, (name, quantity, value) in enumerate(products):
            table.setItem(row, 0, QTableWidgetItem(name))
            table.setItem(row, 1, QTableWidgetItem(str(int(quantity))))
            table.setItem(row, 2, QTableWidgetItem(format_currency(value / quantity if quantity > 0 else 0)))
            table.setItem(row, 3, QTableWidgetItem(format_currency(value)))

    def update_inventory_charts(self, data):
        """تحديث رسوم المخزون"""
        if 'inventory' not in self.charts:
            return

        chart_data = self.charts['inventory']

        # مسح البيانات السابقة
        chart_data['current_stock'].remove(0, chart_data['current_stock'].count())
        chart_data['min_stock'].remove(0, chart_data['min_stock'].count())

        # إضافة البيانات الجديدة
        categories = []
        for name, current, minimum in data.get('low_stock', []):
            categories.append(name)
            chart_data['current_stock'].append(current)
            chart_data['min_stock'].append(minimum)

        # تحديث المحاور
        chart_data['axis_x'].clear()
        chart_data['axis_x'].append(categories)

    def update_inventory_table(self, data):
        """تحديث جدول المخزون"""
        table = self.low_stock_table
        low_stock = data.get('low_stock', [])

        table.setRowCount(len(low_stock))

        for row, (name, current, minimum) in enumerate(low_stock):
            table.setItem(row, 0, QTableWidgetItem(name))
            table.setItem(row, 1, QTableWidgetItem(str(current)))
            table.setItem(row, 2, QTableWidgetItem(str(minimum)))

            # تلوين الصفوف حسب الحالة
            if current <= 0:
                color = QColor("#f8d7da")  # أحمر فاتح
            elif current <= minimum:
                color = QColor("#fff3cd")  # أصفر فاتح
            else:
                color = QColor("#d1edff")  # أزرق فاتح

            for col in range(3):
                item = table.item(row, col)
                if item:
                    item.setBackground(color)

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير",
                f"تقرير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if not file_path:
                return

            # إنشاء ملف Excel
            workbook = xlsxwriter.Workbook(file_path)

            # تنسيقات
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#0d6efd',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter'
            })

            currency_format = workbook.add_format({'num_format': '#,##0.00'})

            # ورقة البيانات المالية
            if 'financial' in self.current_data:
                self.export_financial_sheet(workbook, header_format, currency_format)

            # ورقة بيانات المبيعات
            if 'sales' in self.current_data:
                self.export_sales_sheet(workbook, header_format, currency_format)

            # ورقة بيانات المخزون
            if 'inventory' in self.current_data:
                self.export_inventory_sheet(workbook, header_format, currency_format)

            workbook.close()

            QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")

    def export_financial_sheet(self, workbook, header_format, currency_format):
        """تصدير ورقة البيانات المالية"""
        worksheet = workbook.add_worksheet('البيانات المالية')

        # العناوين
        headers = ['التاريخ', 'المبيعات', 'المشتريات', 'الأرباح']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # البيانات
        data = self.current_data['financial']
        sales_dict = {str(date): value for date, value in data.get('sales', [])}
        purchases_dict = {str(date): value for date, value in data.get('purchases', [])}
        profits_dict = {str(date): profit for date, profit in data.get('profits', [])}

        all_dates = sorted(set(sales_dict.keys()) | set(purchases_dict.keys()) | set(profits_dict.keys()))

        for row, date in enumerate(all_dates, 1):
            worksheet.write(row, 0, date)
            worksheet.write(row, 1, sales_dict.get(date, 0), currency_format)
            worksheet.write(row, 2, purchases_dict.get(date, 0), currency_format)
            worksheet.write(row, 3, profits_dict.get(date, 0), currency_format)

        # تنسيق الأعمدة
        worksheet.set_column('A:A', 15)
        worksheet.set_column('B:D', 12)

    def export_sales_sheet(self, workbook, header_format, currency_format):
        """تصدير ورقة بيانات المبيعات"""
        worksheet = workbook.add_worksheet('أفضل المنتجات')

        # العناوين
        headers = ['المنتج', 'الكمية المباعة', 'إجمالي القيمة']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # البيانات
        data = self.current_data['sales']
        for row, (name, quantity, value) in enumerate(data.get('top_products', []), 1):
            worksheet.write(row, 0, name)
            worksheet.write(row, 1, int(quantity))
            worksheet.write(row, 2, value, currency_format)

        # تنسيق الأعمدة
        worksheet.set_column('A:A', 25)
        worksheet.set_column('B:C', 15)

    def export_inventory_sheet(self, workbook, header_format, currency_format):
        """تصدير ورقة بيانات المخزون"""
        worksheet = workbook.add_worksheet('تحذيرات المخزون')

        # العناوين
        headers = ['المنتج', 'الكمية الحالية', 'الحد الأدنى']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # البيانات
        data = self.current_data['inventory']
        for row, (name, current, minimum) in enumerate(data.get('low_stock', []), 1):
            worksheet.write(row, 0, name)
            worksheet.write(row, 1, current)
            worksheet.write(row, 2, minimum)

        # تنسيق الأعمدة
        worksheet.set_column('A:A', 25)
        worksheet.set_column('B:C', 15)

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            print("🔧 بدء تحسين قاعدة البيانات...")

            # إنشاء الفهارس
            self.db_optimizer.create_indexes()

            # تحسين قاعدة البيانات
            self.db_optimizer.optimize_database()

            print("✅ تم تحسين قاعدة البيانات بنجاح")

        except Exception as e:
            print(f"⚠️ خطأ في تحسين قاعدة البيانات: {e}")

    def cleanup_cache(self):
        """تنظيف التخزين المؤقت"""
        try:
            cleaned_count = cache_manager.cleanup_expired()
            if cleaned_count > 0:
                print(f"🧹 تم تنظيف {cleaned_count} عنصر من التخزين المؤقت")
        except Exception as e:
            print(f"خطأ في تنظيف التخزين المؤقت: {e}")

    def closeEvent(self, event):
        """عند إغلاق الودجت"""
        # إيقاف المؤقتات
        if hasattr(self, 'cache_cleanup_timer'):
            self.cache_cleanup_timer.stop()

        # إيقاف تحميل البيانات
        if self.data_loader and self.data_loader.isRunning():
            self.data_loader.quit()
            self.data_loader.wait()

        # تنظيف التخزين المؤقت
        cache_manager.clear()

        event.accept()
