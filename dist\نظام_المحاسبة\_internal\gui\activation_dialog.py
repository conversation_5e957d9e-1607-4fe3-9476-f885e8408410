#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تفعيل البرنامج - نسخة مبسطة ومضمونة
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QFrame, QMessageBox, QApplication)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from datetime import datetime
import hashlib

from license_manager import LicenseManager

class ActivationDialog(QDialog):
    """نافذة تفعيل البرنامج - نسخة مبسطة"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # إعداد النافذة
        self.setWindowTitle("🔑 تفعيل البرنامج")
        self.setFixedSize(800, 750)  # حجم أكبر لضمان ظهور جميع العناصر
        self.setModal(True)

        # إنشاء مدير التراخيص
        self.license_manager = LicenseManager()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # تحميل حالة التفعيل الحالية
        self.load_current_status()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # العنوان
        self.title = QLabel("🔒 البرنامج غير مفعل")
        self.title.setAlignment(Qt.AlignCenter)
        self.title.setFont(QFont("Arial", 20, QFont.Bold))
        self.title.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background-color: #fdf2f2;
                padding: 15px;
                border-radius: 10px;
                border: 2px solid #e74c3c;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.title)
        
        # معلومات الجهاز
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        info_layout = QVBoxLayout()
        
        # الحصول على بيانات الجهاز
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        # عنوان معلومات الجهاز
        info_title = QLabel("📋 بيانات الجهاز:")
        info_title.setFont(QFont("Arial", 14, QFont.Bold))
        info_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        info_layout.addWidget(info_title)
        
        # كود العميل
        code_layout = QHBoxLayout()
        code_label = QLabel(f"🔑 كود العميل:")
        code_layout.addWidget(code_label)
        
        code_value = QLabel(customer_code)
        code_value.setStyleSheet("font-family: monospace; color: #2980b9; font-weight: bold; font-size: 14px;")
        code_layout.addWidget(code_value)
        
        copy_code_btn = QPushButton("📋 نسخ")
        copy_code_btn.clicked.connect(lambda: self.copy_single_value(customer_code, 'كود العميل'))
        copy_code_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
            }
        """)
        code_layout.addWidget(copy_code_btn)
        
        info_layout.addLayout(code_layout)
        
        # رقم الجهاز
        machine_layout = QHBoxLayout()
        machine_label = QLabel(f"💻 رقم الجهاز:")
        machine_layout.addWidget(machine_label)
        
        machine_value = QLabel(machine_id)
        machine_value.setStyleSheet("font-family: monospace; color: #2980b9; font-weight: bold; font-size: 14px;")
        machine_layout.addWidget(machine_value)
        
        copy_machine_btn = QPushButton("📋 نسخ")
        copy_machine_btn.clicked.connect(lambda: self.copy_single_value(machine_id, 'رقم الجهاز'))
        copy_machine_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
            }
        """)
        machine_layout.addWidget(copy_machine_btn)
        
        info_layout.addLayout(machine_layout)
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        # تعليمات
        self.instructions = QLabel("""
📞 للحصول على كود التفعيل:
1️⃣ أرسل البيانات أعلاه إلى المطور
2️⃣ البريد الإلكتروني: <EMAIL>
3️⃣ ستحصل على كود التفعيل خلال 24 ساعة
4️⃣ أدخل الكود في الحقل أدناه واضغط "تفعيل"
        """)
        self.instructions.setStyleSheet("color: #1976d2; margin: 10px; padding: 10px; font-size: 12px;")
        layout.addWidget(self.instructions)
        
        # حقل كود التفعيل - مضمون الظهور بشكل واضح
        print("🔧 إنشاء حقل كود التفعيل...")

        # إطار كبير وواضح لحقل التفعيل
        activation_frame = QFrame()
        activation_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 5px solid #ffc107;
                border-radius: 15px;
                padding: 30px;
                margin: 20px 5px;
                min-height: 150px;
            }
        """)
        activation_layout = QVBoxLayout()
        activation_layout.setSpacing(15)

        # عنوان واضح وكبير
        activation_title = QLabel("🔑 أدخل كود التفعيل هنا:")
        activation_title.setFont(QFont("Arial", 18, QFont.Bold))
        activation_title.setAlignment(Qt.AlignCenter)
        activation_title.setStyleSheet("""
            QLabel {
                color: #856404;
                background-color: #ffeaa7;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
                border: 2px solid #fdcb6e;
            }
        """)
        activation_layout.addWidget(activation_title)

        # حقل الإدخال - كبير جداً وواضح
        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("SICOO-XXXXXXXX-XXXX-XXXX-XXXX")
        self.activation_code.setMinimumHeight(60)
        self.activation_code.setMaximumHeight(60)
        self.activation_code.setStyleSheet("""
            QLineEdit {
                padding: 20px;
                border: 4px solid #ffc107;
                border-radius: 12px;
                font-size: 18px;
                font-family: 'Courier New', monospace;
                background-color: white;
                color: #2c3e50;
                font-weight: bold;
                text-align: center;
            }
            QLineEdit:focus {
                border-color: #e74c3c;
                background-color: #fff5f5;
                box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
            }
            QLineEdit:hover {
                border-color: #f39c12;
                background-color: #fffef7;
            }
        """)

        # إضافة الحقل إلى التخطيط أولاً
        activation_layout.addWidget(self.activation_code)

        # التأكد من أن الحقل مرئي ومفعل
        self.activation_code.setVisible(True)
        self.activation_code.setEnabled(True)
        self.activation_code.show()
        self.activation_code.setFocus()

        # رسالة تأكيد واضحة
        confirm_label = QLabel("👆 اكتب أو الصق كود التفعيل في الحقل أعلاه")
        confirm_label.setAlignment(Qt.AlignCenter)
        confirm_label.setFont(QFont("Arial", 12, QFont.Bold))
        confirm_label.setStyleSheet("""
            QLabel {
                color: #856404;
                background-color: #fef9e7;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
                border: 1px solid #f1c40f;
            }
        """)
        activation_layout.addWidget(confirm_label)

        activation_frame.setLayout(activation_layout)
        layout.addWidget(activation_frame)

        print(f"✅ تم إنشاء حقل كود التفعيل - مرئي: {self.activation_code.isVisible()}, مفعل: {self.activation_code.isEnabled()}")
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        # زر نسخ بيانات الجهاز
        self.copy_data_btn = QPushButton("📋 نسخ بيانات الجهاز")
        self.copy_data_btn.clicked.connect(self.copy_machine_data)
        self.copy_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(self.copy_data_btn)
        
        # زر التفعيل
        self.activate_button = QPushButton("✅ تفعيل البرنامج")
        self.activate_button.clicked.connect(self.activate_license)
        self.activate_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(self.activate_button)
        
        # زر الإلغاء (مخفي افتراضياً)
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.cancel_renewal)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.cancel_button.setVisible(False)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

        # التأكد من ظهور حقل التفعيل بعد إعداد التخطيط
        if hasattr(self, 'activation_code'):
            self.activation_code.setVisible(True)
            self.activation_code.setEnabled(True)
            self.activation_code.show()
            self.activation_code.setFocus()
            print(f"🔄 إعادة تأكيد ظهور حقل التفعيل - مرئي: {self.activation_code.isVisible()}, مفعل: {self.activation_code.isEnabled()}")
    
    def load_current_status(self):
        """تحميل حالة التفعيل الحالية"""
        try:
            # فحص الترخيص
            license_status = self.license_manager.check_license()
            
            if license_status["valid"]:
                # البرنامج مفعل
                expiry_date = license_status['expiry_date']
                days_remaining = license_status['days_remaining']
                
                self.title.setText("✅ البرنامج مفعل")
                self.title.setStyleSheet("color: #27ae60; margin: 20px;")
                
                self.instructions.setText(f"""
✅ البرنامج مفعل بنجاح!

📊 تفاصيل الترخيص:
📅 صالح حتى: {expiry_date.strftime('%d/%m/%Y')}
⏰ متبقي: {days_remaining} يوم

🔄 لتجديد الترخيص، اضغط "تجديد الترخيص"
                """)
                
                self.activation_code.setEnabled(False)
                self.activation_code.setPlaceholderText("اضغط 'تجديد الترخيص' لإدخال كود جديد")
                # التأكد من أن الحقل يبقى مرئي
                self.activation_code.setVisible(True)
                self.activation_code.show()
                self.activate_button.setText("🔄 تجديد الترخيص")
                
            else:
                # البرنامج غير مفعل
                self.title.setText("🔒 البرنامج غير مفعل")
                self.title.setStyleSheet("color: #e74c3c; margin: 20px;")
                
                self.activation_code.setEnabled(True)
                self.activation_code.setPlaceholderText("أدخل كود التفعيل هنا...")
                # التأكد من أن الحقل مرئي ومفعل
                self.activation_code.setVisible(True)
                self.activation_code.show()
                self.activate_button.setText("🔑 تفعيل البرنامج")
                print(f"🔧 حالة حقل التفعيل: مرئي={self.activation_code.isVisible()}, مفعل={self.activation_code.isEnabled()}")
                
        except Exception as e:
            print(f"خطأ في تحميل حالة التفعيل: {e}")
    
    def copy_single_value(self, value, name):
        """نسخ قيمة واحدة إلى الحافظة"""
        try:
            QApplication.clipboard().setText(str(value))
            QMessageBox.information(self, "تم النسخ", f"تم نسخ {name} إلى الحافظة")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في نسخ {name}: {str(e)}")
    
    def copy_machine_data(self):
        """نسخ بيانات الجهاز إلى الحافظة"""
        try:
            customer_code = self.license_manager.get_customer_code()
            machine_id = self.license_manager.get_machine_id()
            
            data = f"""
بيانات الجهاز للتفعيل:

🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}

يرجى إرسال هذه البيانات إلى:
📧 <EMAIL>

للحصول على كود التفعيل.
            """
            
            QApplication.clipboard().setText(data.strip())
            QMessageBox.information(self, "تم النسخ", "تم نسخ بيانات الجهاز إلى الحافظة")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في نسخ البيانات: {str(e)}")
    
    def activate_license(self):
        """تفعيل البرنامج"""
        # فحص الترخيص الحالي
        current_status = self.license_manager.check_license()
        
        if current_status["valid"] and self.activate_button.text() == "🔄 تجديد الترخيص":
            # تفعيل وضع التجديد
            self.activation_code.setEnabled(True)
            self.activation_code.clear()
            self.activation_code.setPlaceholderText("أدخل كود التجديد الجديد...")
            self.activate_button.setText("✅ تطبيق التجديد")
            self.cancel_button.setVisible(True)
            self.activation_code.setFocus()
            return
        
        # الحصول على كود التفعيل/التجديد
        code = self.activation_code.text().strip()
        
        if not code:
            if current_status["valid"]:
                QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التجديد")
            else:
                QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التفعيل")
            return
        
        # التحقق من صحة الكود
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        validation_result = self.validate_license_code(code, customer_code, machine_id)
        
        if not validation_result["valid"]:
            QMessageBox.critical(self, "كود غير صحيح", validation_result["message"])
            return
        
        # حفظ الترخيص الجديد
        try:
            if self.license_manager._save_license(validation_result["license_data"]):
                # عرض رسالة نجاح
                expiry_date = validation_result['expiry_date']
                days_remaining = validation_result['days_remaining']
                
                if current_status["valid"]:
                    success_msg = f"🎉 تم تجديد الترخيص بنجاح!\n\n📅 صالح حتى: {expiry_date.strftime('%d/%m/%Y')}\n⏰ إجمالي الأيام: {days_remaining} يوم"
                    QMessageBox.information(self, "تم التجديد بنجاح! 🎉", success_msg)
                else:
                    success_msg = f"🎉 تم تفعيل البرنامج بنجاح!\n\n📅 صالح حتى: {expiry_date.strftime('%d/%m/%Y')}\n⏰ مدة التفعيل: {days_remaining} يوم"
                    QMessageBox.information(self, "تم التفعيل بنجاح! 🎉", success_msg)
                
                # تحديث واجهة المستخدم
                self.load_current_status()
                
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الترخيص")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التفعيل:\n{str(e)}")
    
    def cancel_renewal(self):
        """إلغاء عملية التجديد"""
        self.activation_code.setEnabled(False)
        self.activation_code.setPlaceholderText("اضغط 'تجديد الترخيص' لإدخال كود جديد")
        self.cancel_button.setVisible(False)
        self.activate_button.setText("🔄 تجديد الترخيص")
        self.load_current_status()
    
    def validate_license_code(self, license_code, customer_code, machine_id):
        """التحقق من صحة كود الترخيص"""
        try:
            # التحقق من استخدام الكود مسبقاً
            if self.license_manager.is_code_used(license_code):
                return {
                    "valid": False,
                    "message": "هذا الكود مستخدم مسبقاً ولا يمكن استخدامه مرة أخرى"
                }
            
            # تنسيق الكود المتوقع: SICOO-YYYYMMDD-CUSTOMER-MACHINE-HASH
            parts = license_code.strip().upper().split('-')
            
            if len(parts) != 5 or parts[0] != "SICOO":
                return {
                    "valid": False,
                    "message": "تنسيق كود الترخيص غير صحيح"
                }
            
            date_str = parts[1]
            customer_part = parts[2]
            machine_part = parts[3]
            hash_part = parts[4]
            
            # التحقق من التاريخ
            try:
                expiry_date = datetime.strptime(date_str, "%Y%m%d")
                current_date = datetime.now()
                
                if current_date > expiry_date:
                    return {
                        "valid": False,
                        "message": "كود الترخيص منتهي الصلاحية"
                    }
                
                days_remaining = (expiry_date - current_date).days
                
            except ValueError:
                return {
                    "valid": False,
                    "message": "تاريخ انتهاء الترخيص غير صحيح"
                }
            
            # التحقق من كود العميل ورقم الجهاز
            expected_customer = customer_code[:6].upper()
            expected_machine = machine_id[:6].upper()
            
            if customer_part != expected_customer:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا العميل"
                }
            
            if machine_part != expected_machine:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا الجهاز"
                }
            
            # التحقق من الـ hash
            data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
            expected_hash = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
            
            if hash_part != expected_hash:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صحيح أو تالف"
                }
            
            # التحقق من وجود ترخيص سابق لحساب المدة الإضافية
            current_license = self.license_manager._load_license()
            final_expiry_date = expiry_date
            license_type = "FULL"
            additional_days = days_remaining
            
            if current_license and current_license.get("expiry_date"):
                try:
                    current_expiry = datetime.fromisoformat(current_license["expiry_date"])
                    
                    if current_expiry > current_date:
                        # إضافة المدة الجديدة على الترخيص الموجود
                        final_expiry_date = current_expiry + (expiry_date - current_date)
                        license_type = "EXTENDED"
                        additional_days = (final_expiry_date - current_date).days
                        
                except Exception as e:
                    print(f"خطأ في حساب المدة الإضافية: {e}")
            
            # إنشاء بيانات الترخيص
            license_data = {
                "customer_code": customer_code,
                "machine_id": machine_id,
                "expiry_date": final_expiry_date.isoformat(),
                "license_type": license_type,
                "license_code": license_code,
                "activated_date": datetime.now().isoformat(),
                "original_expiry": expiry_date.isoformat(),
                "additional_days": additional_days
            }
            
            return {
                "valid": True,
                "message": "كود الترخيص صحيح",
                "expiry_date": final_expiry_date,
                "days_remaining": additional_days,
                "license_data": license_data,
                "is_extension": license_type == "EXTENDED"
            }
            
        except Exception as e:
            return {
                "valid": False,
                "message": f"خطأ في التحقق من الكود: {str(e)}"
            }
