#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التحليلات الذكية - عرض التحليلات والتنبؤات
Smart Analytics Interface - Display analytics and predictions
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QScrollArea, QGroupBox,
                             QFormLayout, QComboBox, QDateEdit, QTabWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QTextEdit, QSplitter)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QDate
from PyQt5.QtGui import QFont, QColor, QPainter
from PyQt5.QtChart import (QChart, QChartView, QLineSeries, QBarSeries, QBarSet, 
                          QValueAxis, QBarCategoryAxis, QPieSeries, QAreaSeries)
from utils.smart_analytics import AnalyticsEngine
from utils.currency_formatter import format_currency, format_number
from datetime import datetime, timedelta
import json


class SmartAnalyticsWidget(QWidget):
    """واجهة التحليلات الذكية"""
    
    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.analytics_engine = None
        self.current_analysis = {}
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # العنوان الرئيسي
        title_frame = self.create_title_frame()
        layout.addWidget(title_frame)
        
        # شريط التحكم
        control_frame = self.create_control_frame()
        layout.addWidget(control_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #28a745;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # تبويبات التحليلات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
            QTabBar::tab {
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                background-color: #f8f9fa;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # تبويب اتجاهات المبيعات
        self.sales_trends_tab = self.create_sales_trends_tab()
        self.tabs.addTab(self.sales_trends_tab, "📈 اتجاهات المبيعات")
        
        # تبويب سلوك العملاء
        self.customer_behavior_tab = self.create_customer_behavior_tab()
        self.tabs.addTab(self.customer_behavior_tab, "👥 سلوك العملاء")
        
        # تبويب أداء المنتجات
        self.product_performance_tab = self.create_product_performance_tab()
        self.tabs.addTab(self.product_performance_tab, "📦 أداء المنتجات")
        
        # تبويب الصحة المالية
        self.financial_health_tab = self.create_financial_health_tab()
        self.tabs.addTab(self.financial_health_tab, "💰 الصحة المالية")
        
        # تبويب التنبؤات
        self.predictions_tab = self.create_predictions_tab()
        self.tabs.addTab(self.predictions_tab, "🔮 التنبؤات")
        
        layout.addWidget(self.tabs)
    
    def create_title_frame(self):
        """إنشاء إطار العنوان"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        
        layout = QHBoxLayout()
        frame.setLayout(layout)
        
        title_label = QLabel("🧠 التحليلات الذكية")
        title_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        
        subtitle_label = QLabel("تحليل البيانات والتنبؤات المتقدمة")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-size: 14px;")
        
        text_layout = QVBoxLayout()
        text_layout.addWidget(title_label)
        text_layout.addWidget(subtitle_label)
        
        layout.addLayout(text_layout)
        layout.addStretch()
        
        return frame
    
    def create_control_frame(self):
        """إنشاء شريط التحكم"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        layout = QHBoxLayout()
        frame.setLayout(layout)
        
        # نوع التحليل
        analysis_group = QGroupBox("نوع التحليل")
        analysis_layout = QFormLayout()
        
        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "اتجاهات المبيعات",
            "سلوك العملاء", 
            "أداء المنتجات",
            "الصحة المالية",
            "التنبؤات"
        ])
        analysis_layout.addRow("التحليل:", self.analysis_type_combo)
        
        analysis_group.setLayout(analysis_layout)
        layout.addWidget(analysis_group)
        
        # الفترة الزمنية
        period_group = QGroupBox("الفترة الزمنية")
        period_layout = QFormLayout()
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "آخر 3 شهور", "آخر 6 شهور", "آخر سنة", "مخصص"
        ])
        period_layout.addRow("الفترة:", self.period_combo)
        
        period_group.setLayout(period_layout)
        layout.addWidget(period_group)
        
        # أزرار التحكم
        buttons_group = QGroupBox("العمليات")
        buttons_layout = QVBoxLayout()
        
        self.analyze_btn = QPushButton("🚀 بدء التحليل")
        self.analyze_btn.clicked.connect(self.start_analysis)
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.export_btn = QPushButton("📤 تصدير النتائج")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        buttons_layout.addWidget(self.analyze_btn)
        buttons_layout.addWidget(self.export_btn)
        buttons_group.setLayout(buttons_layout)
        layout.addWidget(buttons_group)
        
        return frame
    
    def create_sales_trends_tab(self):
        """إنشاء تبويب اتجاهات المبيعات"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # الرسم البياني الرئيسي
        self.sales_chart = self.create_sales_chart()
        layout.addWidget(self.sales_chart)
        
        # ملخص الإحصائيات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        stats_layout = QHBoxLayout()
        stats_frame.setLayout(stats_layout)
        
        self.total_sales_label = QLabel("إجمالي المبيعات: --")
        self.avg_growth_label = QLabel("متوسط النمو: --")
        self.best_month_label = QLabel("أفضل شهر: --")
        
        for label in [self.total_sales_label, self.avg_growth_label, self.best_month_label]:
            label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    padding: 10px;
                    border-radius: 5px;
                    font-weight: bold;
                    border: 1px solid #dee2e6;
                }
            """)
            stats_layout.addWidget(label)
        
        layout.addWidget(stats_frame)
        
        return widget
    
    def create_customer_behavior_tab(self):
        """إنشاء تبويب سلوك العملاء"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # تقسيم العملاء
        splitter = QSplitter(Qt.Horizontal)
        
        # الرسم البياني
        self.customer_chart = self.create_customer_chart()
        splitter.addWidget(self.customer_chart)
        
        # جدول العملاء
        self.customer_table = QTableWidget()
        self.customer_table.setColumnCount(4)
        self.customer_table.setHorizontalHeaderLabels([
            "العميل", "عدد المشتريات", "إجمالي الإنفاق", "التصنيف"
        ])
        self.customer_table.horizontalHeader().setStretchLastSection(True)
        splitter.addWidget(self.customer_table)
        
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        # رؤى العملاء
        insights_frame = QFrame()
        insights_frame.setStyleSheet("""
            QFrame {
                background-color: #e7f3ff;
                border: 1px solid #b3d9ff;
                border-radius: 5px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        insights_layout = QVBoxLayout()
        insights_frame.setLayout(insights_layout)
        
        insights_title = QLabel("💡 رؤى العملاء")
        insights_title.setFont(QFont("Arial", 12, QFont.Bold))
        insights_layout.addWidget(insights_title)
        
        self.customer_insights = QTextEdit()
        self.customer_insights.setMaximumHeight(100)
        self.customer_insights.setReadOnly(True)
        insights_layout.addWidget(self.customer_insights)
        
        layout.addWidget(insights_frame)
        
        return widget
    
    def create_product_performance_tab(self):
        """إنشاء تبويب أداء المنتجات"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # جدول أداء المنتجات
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(6)
        self.product_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية المباعة", "الإيرادات", "الربح المقدر", "هامش الربح %", "التقييم"
        ])
        self.product_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.product_table)
        
        # توصيات المنتجات
        recommendations_frame = QFrame()
        recommendations_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        recommendations_layout = QVBoxLayout()
        recommendations_frame.setLayout(recommendations_layout)
        
        recommendations_title = QLabel("💡 توصيات المنتجات")
        recommendations_title.setFont(QFont("Arial", 12, QFont.Bold))
        recommendations_layout.addWidget(recommendations_title)
        
        self.product_recommendations = QTextEdit()
        self.product_recommendations.setMaximumHeight(100)
        self.product_recommendations.setReadOnly(True)
        recommendations_layout.addWidget(self.product_recommendations)
        
        layout.addWidget(recommendations_frame)
        
        return widget
    
    def create_financial_health_tab(self):
        """إنشاء تبويب الصحة المالية"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # مؤشر الصحة المالية
        health_frame = QFrame()
        health_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #56ab2f, stop:1 #a8e6cf);
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        
        health_layout = QHBoxLayout()
        health_frame.setLayout(health_layout)
        
        self.health_score_label = QLabel("نقاط الصحة المالية: --")
        self.health_score_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
        
        self.health_status_label = QLabel("الحالة: --")
        self.health_status_label.setStyleSheet("color: white; font-size: 14px;")
        
        health_text_layout = QVBoxLayout()
        health_text_layout.addWidget(self.health_score_label)
        health_text_layout.addWidget(self.health_status_label)
        
        health_layout.addLayout(health_text_layout)
        health_layout.addStretch()
        
        layout.addWidget(health_frame)
        
        # الرسم البياني المالي
        self.financial_chart = self.create_financial_chart()
        layout.addWidget(self.financial_chart)
        
        return widget
    
    def create_predictions_tab(self):
        """إنشاء تبويب التنبؤات"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # تنبؤات المبيعات
        sales_pred_frame = QFrame()
        sales_pred_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        sales_pred_layout = QVBoxLayout()
        sales_pred_frame.setLayout(sales_pred_layout)
        
        sales_pred_title = QLabel("📈 تنبؤات المبيعات")
        sales_pred_title.setFont(QFont("Arial", 14, QFont.Bold))
        sales_pred_layout.addWidget(sales_pred_title)
        
        self.sales_prediction_text = QTextEdit()
        self.sales_prediction_text.setMaximumHeight(100)
        self.sales_prediction_text.setReadOnly(True)
        sales_pred_layout.addWidget(self.sales_prediction_text)
        
        layout.addWidget(sales_pred_frame)
        
        # تنبؤات المخزون
        inventory_pred_frame = QFrame()
        inventory_pred_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                margin: 10px;
            }
        """)
        
        inventory_pred_layout = QVBoxLayout()
        inventory_pred_frame.setLayout(inventory_pred_layout)
        
        inventory_pred_title = QLabel("📦 تنبؤات المخزون")
        inventory_pred_title.setFont(QFont("Arial", 14, QFont.Bold))
        inventory_pred_layout.addWidget(inventory_pred_title)
        
        self.inventory_prediction_text = QTextEdit()
        self.inventory_prediction_text.setMaximumHeight(100)
        self.inventory_prediction_text.setReadOnly(True)
        inventory_pred_layout.addWidget(self.inventory_prediction_text)
        
        layout.addWidget(inventory_pred_frame)
        
        layout.addStretch()

        return widget

    def create_sales_chart(self):
        """إنشاء رسم اتجاهات المبيعات"""
        chart = QChart()
        chart.setTitle("📈 اتجاهات المبيعات الشهرية")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة المبيعات
        self.sales_series = QLineSeries()
        self.sales_series.setName("المبيعات")

        # سلسلة النمو
        self.growth_series = QAreaSeries()
        self.growth_series.setName("معدل النمو")

        chart.addSeries(self.sales_series)
        chart.addSeries(self.growth_series)

        # إعداد المحاور
        self.sales_axis_x = QBarCategoryAxis()
        self.sales_axis_y = QValueAxis()

        chart.addAxis(self.sales_axis_x, Qt.AlignBottom)
        chart.addAxis(self.sales_axis_y, Qt.AlignLeft)

        self.sales_series.attachAxis(self.sales_axis_x)
        self.sales_series.attachAxis(self.sales_axis_y)

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        chart_view.setMinimumHeight(300)

        return chart_view

    def create_customer_chart(self):
        """إنشاء رسم تصنيف العملاء"""
        chart = QChart()
        chart.setTitle("👥 تصنيف العملاء")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة دائرية
        self.customer_series = QPieSeries()
        chart.addSeries(self.customer_series)

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        chart_view.setMinimumHeight(300)

        return chart_view

    def create_financial_chart(self):
        """إنشاء رسم الصحة المالية"""
        chart = QChart()
        chart.setTitle("💰 الأداء المالي الشهري")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلاسل البيانات
        self.revenue_series = QLineSeries()
        self.revenue_series.setName("الإيرادات")

        self.profit_series = QLineSeries()
        self.profit_series.setName("الأرباح")

        self.expenses_series = QLineSeries()
        self.expenses_series.setName("المصروفات")

        chart.addSeries(self.revenue_series)
        chart.addSeries(self.profit_series)
        chart.addSeries(self.expenses_series)

        # إعداد المحاور
        self.financial_axis_x = QBarCategoryAxis()
        self.financial_axis_y = QValueAxis()

        chart.addAxis(self.financial_axis_x, Qt.AlignBottom)
        chart.addAxis(self.financial_axis_y, Qt.AlignLeft)

        for series in [self.revenue_series, self.profit_series, self.expenses_series]:
            series.attachAxis(self.financial_axis_x)
            series.attachAxis(self.financial_axis_y)

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        chart_view.setMinimumHeight(300)

        return chart_view

    def start_analysis(self):
        """بدء التحليل"""
        if self.analytics_engine and self.analytics_engine.isRunning():
            return

        # تحديد نوع التحليل
        analysis_type_map = {
            "اتجاهات المبيعات": "sales_trends",
            "سلوك العملاء": "customer_behavior",
            "أداء المنتجات": "product_performance",
            "الصحة المالية": "financial_health",
            "التنبؤات": "predictions"
        }

        selected_type = self.analysis_type_combo.currentText()
        analysis_type = analysis_type_map.get(selected_type, "sales_trends")

        # معاملات التحليل
        parameters = {
            'period': self.period_combo.currentText()
        }

        # بدء التحليل
        self.analytics_engine = AnalyticsEngine(self.engine, analysis_type, parameters)
        self.analytics_engine.analysis_completed.connect(self.on_analysis_completed)
        self.analytics_engine.progress_updated.connect(self.on_progress_updated)
        self.analytics_engine.error_occurred.connect(self.on_analysis_error)

        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.analyze_btn.setEnabled(False)

        self.analytics_engine.start()

    def on_progress_updated(self, value, message):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        self.progress_bar.setFormat(f"{message} ({value}%)")

    def on_analysis_completed(self, results):
        """عند اكتمال التحليل"""
        self.current_analysis = results
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)
        self.export_btn.setEnabled(True)

        # عرض النتائج حسب نوع التحليل
        analysis_type = results.get('type', '')

        if analysis_type == 'sales_trends':
            self.display_sales_trends(results)
            self.tabs.setCurrentIndex(0)
        elif analysis_type == 'customer_behavior':
            self.display_customer_behavior(results)
            self.tabs.setCurrentIndex(1)
        elif analysis_type == 'product_performance':
            self.display_product_performance(results)
            self.tabs.setCurrentIndex(2)
        elif analysis_type == 'financial_health':
            self.display_financial_health(results)
            self.tabs.setCurrentIndex(3)
        elif analysis_type == 'predictions':
            self.display_predictions(results)
            self.tabs.setCurrentIndex(4)

    def on_analysis_error(self, error_message):
        """عند حدوث خطأ في التحليل"""
        self.progress_bar.setVisible(False)
        self.analyze_btn.setEnabled(True)

        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "خطأ في التحليل", f"حدث خطأ أثناء التحليل:\n{error_message}")

    def display_sales_trends(self, results):
        """عرض نتائج اتجاهات المبيعات"""
        monthly_data = results.get('monthly_data', [])
        summary = results.get('summary', {})

        # تحديث الرسم البياني
        self.sales_series.clear()
        self.sales_axis_x.clear()

        months = []
        sales_values = []

        for i, data in enumerate(monthly_data):
            month = data['month']
            sales = data['total_sales']

            months.append(month)
            sales_values.append(sales)
            self.sales_series.append(i, sales)

        self.sales_axis_x.append(months)

        if sales_values:
            max_value = max(sales_values)
            self.sales_axis_y.setRange(0, max_value * 1.1)

        # تحديث الإحصائيات
        self.total_sales_label.setText(f"إجمالي المبيعات: {format_currency(summary.get('total_sales', 0))}")
        self.avg_growth_label.setText(f"متوسط النمو: {summary.get('avg_growth_rate', 0):.1f}%")

        best_month = summary.get('best_month', {})
        if best_month:
            self.best_month_label.setText(f"أفضل شهر: {best_month.get('month', '--')} ({format_currency(best_month.get('total_sales', 0))})")

    def export_results(self):
        """تصدير نتائج التحليل"""
        if not hasattr(self, 'current_analysis') or not self.current_analysis:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "تحذير", "لا توجد نتائج تحليل للتصدير.\nيرجى تشغيل التحليل أولاً.")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            # اختيار مكان الحفظ
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير نتائج التحليل الذكي",
                f"تحليل_ذكي_{self.current_analysis.get('type', 'unknown')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json);;Text Files (*.txt)"
            )

            if filename:
                # إعداد البيانات للتصدير
                export_data = {
                    'تاريخ_التصدير': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'نوع_التحليل': self.current_analysis.get('type', 'غير محدد'),
                    'فترة_التحليل': self.current_analysis.get('period', 'غير محدد'),
                    'النتائج': self.current_analysis,
                    'ملاحظات': 'تم إنشاء هذا التقرير بواسطة نظام التحليلات الذكية'
                }

                # حفظ الملف
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(
                    self,
                    "نجح التصدير",
                    f"تم تصدير نتائج التحليل بنجاح إلى:\n{filename}"
                )

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"حدث خطأ أثناء تصدير النتائج:\n{str(e)}"
            )

    def display_customer_behavior(self, results):
        """عرض نتائج سلوك العملاء"""
        # تنفيذ بسيط لعرض النتائج
        pass

    def display_product_performance(self, results):
        """عرض نتائج أداء المنتجات"""
        # تنفيذ بسيط لعرض النتائج
        pass

    def display_financial_health(self, results):
        """عرض نتائج الصحة المالية"""
        # تنفيذ بسيط لعرض النتائج
        pass

    def display_predictions(self, results):
        """عرض نتائج التنبؤات"""
        # تنفيذ بسيط لعرض النتائج
        pass
