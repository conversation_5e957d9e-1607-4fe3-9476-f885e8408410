from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel,
from utils.dpi_manager import dpi_manager
                           QFileDialog, QProgressBar, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt
from utils.dialog_utils import setup_medium_dialog
import shutil
import os
from datetime import datetime
import zipfile

class BackupDialog(QDialog):
    def __init__(self, db_path):
        super().__init__()
        # إعداد DPI للواجهة
        try:
            dpi_manager.setup_widget_dpi(self)
        except:
            pass
        self.db_path = db_path

        # إعداد النافذة مع خاصية التكبير
        setup_medium_dialog(self, "نظام النسخ الاحتياطي", 500, 400, 600, 500)

        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("النسخ الاحتياطي")
        self.setMinimumWidth(400)
        layout = QVBoxLayout()
        
        # تصميم محسن للواجهة
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
            }
            QPushButton {
                background-color: #0D6EFD;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0B5ED7;
            }
            QLabel {
                color: #212529;
                font-size: 14px;
            }
            QProgressBar {
                border: 2px solid #DEE2E6;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #0D6EFD;
                border-radius: 3px;
            }
        """)

        # إضافة الشعار
        title = QLabel("نظام النسخ الاحتياطي")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)

        # خيارات النسخ الاحتياطي
        self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup.setChecked(True)
        layout.addWidget(self.auto_backup)

        self.compress_backup = QCheckBox("ضغط النسخة الاحتياطية")
        self.compress_backup.setChecked(True)
        layout.addWidget(self.compress_backup)

        # شريط التقدم
        self.progress = QProgressBar()
        self.progress.hide()
        layout.addWidget(self.progress)

        # أزرار العمليات
        backup_btn = QPushButton("إنشاء نسخة احتياطية")
        backup_btn.clicked.connect(self.create_backup)
        layout.addWidget(backup_btn)

        restore_btn = QPushButton("استعادة نسخة احتياطية")
        restore_btn.clicked.connect(self.restore_backup)
        layout.addWidget(restore_btn)

        self.setLayout(layout)

    def create_backup(self):
        try:
            # اختيار مسار الحفظ
            backup_dir = QFileDialog.getExistingDirectory(self, "اختر مجلد الحفظ")
            if not backup_dir:
                return

            self.progress.show()
            self.progress.setValue(0)

            # إنشاء اسم الملف مع التاريخ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"accounting_backup_{timestamp}"
            
            if self.compress_backup.isChecked():
                backup_path = os.path.join(backup_dir, f"{backup_filename}.zip")
                # إنشاء ملف مضغوط
                with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    self.progress.setValue(30)
                    # نسخ قاعدة البيانات
                    zipf.write(self.db_path, "accounting.db")
                    self.progress.setValue(70)
                    # إضافة ملف الإعدادات إذا وجد
                    settings_path = os.path.join(os.path.dirname(self.db_path), "settings.json")
                    if os.path.exists(settings_path):
                        zipf.write(settings_path, "settings.json")
            else:
                backup_path = os.path.join(backup_dir, backup_filename)
                os.makedirs(backup_path, exist_ok=True)
                self.progress.setValue(30)
                # نسخ الملفات
                shutil.copy2(self.db_path, os.path.join(backup_path, "accounting.db"))
                self.progress.setValue(70)

            self.progress.setValue(100)
            QMessageBox.information(self, "نجاح", "تم إنشاء النسخة الاحتياطية بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")
        finally:
            self.progress.hide()

    def restore_backup(self):
        try:
            backup_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية",
                filter="Backup files (*.zip);;All files (*.*)"
            )
            if not backup_path:
                return

            reply = QMessageBox.warning(
                self, "تأكيد",
                "سيتم استبدال البيانات الحالية بالنسخة الاحتياطية. هل تريد المتابعة؟",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

            self.progress.show()
            self.progress.setValue(0)

            if backup_path.endswith('.zip'):
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    self.progress.setValue(30)
                    zipf.extractall(os.path.dirname(self.db_path))
            else:
                self.progress.setValue(30)
                shutil.copy2(
                    os.path.join(backup_path, "accounting.db"),
                    self.db_path
                )

            self.progress.setValue(100)
            QMessageBox.information(
                self,
                "نجاح",
                "تم استعادة النسخة الاحتياطية بنجاح! سيتم إغلاق البرنامج الآن، يرجى إعادة تشغيله."
            )
            from PyQt5.QtWidgets import QApplication
            QApplication.quit()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}"
            )
        finally:
            self.progress.hide()

    def closeEvent(self, event):
        if self.auto_backup.isChecked():
            # حفظ إعدادات النسخ الاحتياطي التلقائي
            backup_dir = os.path.join(os.path.dirname(self.db_path), "auto_backup")
            os.makedirs(backup_dir, exist_ok=True)
            self.create_auto_backup(backup_dir)
        event.accept()

    def create_auto_backup(self, backup_dir):
        try:
            # حذف النسخ القديمة (الاحتفاظ بآخر 5 نسخ)
            backups = sorted(
                [f for f in os.listdir(backup_dir) if f.startswith("auto_backup_")],
                reverse=True
            )
            for old_backup in backups[5:]:
                os.remove(os.path.join(backup_dir, old_backup))

            # إنشاء نسخة جديدة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(backup_dir, f"auto_backup_{timestamp}.zip")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(self.db_path, "accounting.db")
                
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}")