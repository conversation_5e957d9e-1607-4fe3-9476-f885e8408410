from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QFormLayout, QLineEdit,
                           QPushButton, QLabel, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QIcon
from gui.modern_theme import COLORS, FONTS, SPACING
from sqlalchemy.orm import Session, joinedload
from database.users import User, Role
from datetime import datetime
import os
import json
import sys

class LoginDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.user = None

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)

        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setWindowTitle("تسجيل الدخول - نظام المحاسبة العصري")
        self.setMinimumSize(720, 1430)  # تصغير العرض بنسبة 20%
        self.setMaximumSize(720, 1430)

        # تعيين أيقونة البرنامج
        try:
            from main import resource_path
            icon_path = resource_path(os.path.join('assets', 'icons.ico'))
            self.setWindowIcon(QIcon(icon_path))
        except Exception:
            pass

        # إضافة لوجو الشركة في أعلى النافذة (ثابت دائماً)
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        from main import resource_path
        logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
        pixmap = QPixmap(logo_path)
        # تكبير اللوجو بنسبة 75% (180 * 1.75 = 315)
        logo_label.setPixmap(pixmap.scaled(315, 315, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setStyleSheet(logo_label.styleSheet() + "background: transparent; border: none; padding: 48px; margin-bottom: 32px; min-height: 350px; max-height: 350px;")
        layout.addWidget(logo_label)

        # عنوان النظام
        title_label = QLabel("نظام المحاسبة العصري")
        title_label.setProperty("title", True)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: white; font-size: 32px; font-weight: bold;")
        layout.addWidget(title_label)

        # عنوان فرعي
        subtitle_label = QLabel("تسجيل الدخول للوصول إلى حسابك")
        subtitle_label.setProperty("subtitle", True)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: white; font-size: 18px;")
        layout.addWidget(subtitle_label)

        # حقول تسجيل الدخول
        self.username = QLineEdit()
        self.username.setPlaceholderText("🔤 اسم المستخدم")
        # سيتم تعيين القيمة الافتراضية لاحقاً بناءً على ملف user_settings.json
        layout.addWidget(self.username)

        self.password = QLineEdit()
        self.password.setPlaceholderText("🔒 كلمة المرور")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setText("")  # لا تملأ كلمة المرور تلقائياً
        layout.addWidget(self.password)

        # CheckBox تذكرني
        self.remember_me = QCheckBox("تذكرني")
        layout.addWidget(self.remember_me)

        # تحميل اسم المستخدم المحفوظ إذا وجد
        self.load_remembered_user()

        # زر تسجيل الدخول
        login_btn = QPushButton("🚀 تسجيل الدخول")
        login_btn.clicked.connect(self.login)
        layout.addWidget(login_btn)

        # مسافة إضافية
        layout.addStretch()

        # معلومات إضافية
        info_label = QLabel("© 2024 نظام المحاسبة العصري - جميع الحقوق محفوظة")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet(f"""
            color: {COLORS['text_muted']};
            font-size: {FONTS['sizes']['xs']};
            margin-top: {SPACING['lg']};
        """)
        layout.addWidget(info_label)

        self.setLayout(layout)

        # تعيين خلفية جريديانت جديد
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #141E30, stop:1 #243B55);
            }
        """)

    def load_remembered_user(self):
        """تحميل اسم المستخدم المحفوظ إذا كان خيار تذكرني مفعلاً"""
        try:
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "user_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "user_settings.json")
            
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                username = settings.get('last_username', "")
                remember = settings.get('remember_me', False)
                if remember and username:
                    self.username.setText(username)
                    self.remember_me.setChecked(True)
                else:
                    self.username.setText("")
                    self.remember_me.setChecked(False)
            else:
                self.username.setText("")
                self.remember_me.setChecked(False)
        except Exception:
            self.username.setText("")
            self.remember_me.setChecked(False)

    def login(self):
        username = self.username.text()
        password = self.password.text()

        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            with Session(self.engine) as session:
                # تحميل المستخدم مع الأدوار والصلاحيات مباشرة
                user = session.query(User).options(
                    joinedload(User.roles).joinedload(Role.permissions)
                ).filter(User.username == username).first()
                
                if user and user.check_password(password):
                    if not user.is_active:
                        QMessageBox.warning(self, "خطأ", "هذا الحساب غير نشط")
                        return
                        
                    # تحديث آخر تسجيل دخول
                    user.last_login = datetime.now()
                    session.commit()
                    
                    # حفظ نسخة من المستخدم مع جميع البيانات المرتبطة
                    session.refresh(user)
                    self.user = user
                    # حفظ اسم المستخدم إذا كان تذكرني مفعلاً
                    self.save_remembered_user()
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسجيل الدخول: {str(e)}")
            print(f"Login error: {e}")

    def save_remembered_user(self):
        """حفظ اسم المستخدم إذا كان خيار تذكرني مفعلاً"""
        try:
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "user_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "user_settings.json")
            
            settings = {}
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            if self.remember_me.isChecked():
                settings['last_username'] = self.username.text()
                settings['remember_me'] = True
            else:
                settings['last_username'] = ""
                settings['remember_me'] = False
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception:
            pass

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QFormLayout, QLineEdit,
                           QLabel, QMessageBox, QCheckBox, QListWidget, QListWidgetItem,
                           QFrame, QGroupBox, QScrollArea)
from PyQt5.QtCore import Qt
from sqlalchemy.orm import Session
from database.users import User, Role, Permission
from datetime import datetime

class UserManagementDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)

        self.setup_ui()
        self.load_users()
        
    def setup_ui(self):
        self.setWindowTitle("إدارة المستخدمين")
        self.setMinimumSize(800, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 200px;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton[primary="true"] {
                background-color: #0D6EFD;
                color: white;
            }
            QPushButton[primary="true"]:hover {
                background-color: #0B5ED7;
            }
            QPushButton[danger="true"] {
                background-color: #DC3545;
                color: white;
            }
            QPushButton[danger="true"]:hover {
                background-color: #BB2D3B;
            }
            QGroupBox {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                margin-top: 10px;
                padding: 15px;
            }
        """)

        layout = QHBoxLayout()
        
        # جدول المستخدمين
        users_group = QGroupBox("المستخدمون")
        users_layout = QVBoxLayout()
        
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الحالة", "آخر دخول"
        ])
        self.users_table.horizontalHeader().setStretchLastSection(True)
        self.users_table.itemSelectionChanged.connect(self.on_user_selected)
        
        users_layout.addWidget(self.users_table)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_btn = QPushButton("إضافة مستخدم")
        add_btn.setProperty("primary", True)
        add_btn.clicked.connect(self.show_add_user_dialog)
        
        edit_btn = QPushButton("تعديل")
        edit_btn.clicked.connect(self.show_edit_user_dialog)
        
        delete_btn = QPushButton("حذف")
        delete_btn.setProperty("danger", True)
        delete_btn.clicked.connect(self.delete_user)
        
        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        
        users_layout.addLayout(buttons_layout)
        users_group.setLayout(users_layout)
        
        # تفاصيل المستخدم والصلاحيات
        details_group = QGroupBox("تفاصيل المستخدم")
        details_layout = QVBoxLayout()
        
        self.username_label = QLabel()
        self.fullname_label = QLabel()
        self.email_label = QLabel()
        self.status_label = QLabel()
        
        details_layout.addWidget(QLabel("<b>اسم المستخدم:</b>"))
        details_layout.addWidget(self.username_label)
        details_layout.addWidget(QLabel("<b>الاسم الكامل:</b>"))
        details_layout.addWidget(self.fullname_label)
        details_layout.addWidget(QLabel("<b>البريد الإلكتروني:</b>"))
        details_layout.addWidget(self.email_label)
        details_layout.addWidget(QLabel("<b>الحالة:</b>"))
        details_layout.addWidget(self.status_label)
        
        # قائمة الأدوار والصلاحيات
        roles_group = QGroupBox("الأدوار والصلاحيات")
        roles_layout = QVBoxLayout()
        
        self.roles_list = QListWidget()
        roles_layout.addWidget(self.roles_list)
        
        roles_group.setLayout(roles_layout)
        details_layout.addWidget(roles_group)
        
        details_group.setLayout(details_layout)
        
        # إضافة المجموعات للتخطيط الرئيسي
        layout.addWidget(users_group, 2)
        layout.addWidget(details_group, 1)
        
        self.setLayout(layout)

    def load_users(self):
        with Session(self.engine) as session:
            users = session.query(User).all()
            self.users_table.setRowCount(len(users))
            
            for row, user in enumerate(users):
                self.users_table.setItem(row, 0, QTableWidgetItem(user.username))
                self.users_table.setItem(row, 1, QTableWidgetItem(user.full_name or ""))
                self.users_table.setItem(row, 2, QTableWidgetItem(user.email or ""))
                self.users_table.setItem(row, 3, QTableWidgetItem("نشط" if user.is_active else "غير نشط"))
                self.users_table.setItem(row, 4, QTableWidgetItem(
                    user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else ""
                ))

    def on_user_selected(self):
        selected_items = self.users_table.selectedItems()
        if not selected_items:
            return
            
        username = self.users_table.item(selected_items[0].row(), 0).text()
        
        with Session(self.engine) as session:
            user = session.query(User).filter(User.username == username).first()
            if user:
                self.username_label.setText(user.username)
                self.fullname_label.setText(user.full_name or "")
                self.email_label.setText(user.email or "")
                self.status_label.setText("نشط" if user.is_active else "غير نشط")
                
                # تحديث قائمة الأدوار
                self.roles_list.clear()
                for role in user.roles:
                    role_item = QListWidgetItem(f"{role.name} - {role.description}")
                    permissions = ", ".join([p.name for p in role.permissions])
                    role_item.setToolTip(f"الصلاحيات: {permissions}")
                    self.roles_list.addItem(role_item)

    def show_add_user_dialog(self):
        dialog = AddEditUserDialog(self.engine)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def show_edit_user_dialog(self):
        selected_items = self.users_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مستخدم للتعديل")
            return

        username = self.users_table.item(selected_items[0].row(), 0).text()

        with Session(self.engine) as session:
            user = session.query(User).filter(User.username == username).first()
            if user:
                # تمرير النافذة الرئيسية كـ parent للـ AddEditUserDialog
                dialog = AddEditUserDialog(self.engine, user)
                dialog.setParent(self.parent())  # تمرير النافذة الرئيسية
                if dialog.exec_() == QDialog.Accepted:
                    self.load_users()

    def delete_user(self):
        selected_items = self.users_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مستخدم للحذف")
            return

        username = self.users_table.item(selected_items[0].row(), 0).text()

        reply = QMessageBox.question(
            self, "تأكيد",
            f"هل أنت متأكد من حذف المستخدم {username}؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with Session(self.engine) as session:
                    user = session.query(User).filter(User.username == username).first()
                    if user:
                        session.delete(user)
                        session.commit()
                        self.load_users()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المستخدم: {str(e)}")

class AddEditUserDialog(QDialog):
    def __init__(self, engine, user=None):
        super().__init__()
        self.engine = engine
        self.user = user
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("إضافة مستخدم جديد" if not self.user else "تعديل مستخدم")
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout()
        form_layout = QFormLayout()
        
        # حقول البيانات
        self.username = QLineEdit()
        self.username.setPlaceholderText("اسم المستخدم")
        
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور")
        self.password.setEchoMode(QLineEdit.Password)
        
        self.fullname = QLineEdit()
        self.fullname.setPlaceholderText("الاسم الكامل")
        
        self.email = QLineEdit()
        self.email.setPlaceholderText("البريد الإلكتروني")
        
        self.is_active = QCheckBox("المستخدم نشط")
        self.is_active.setChecked(True)
        
        # قائمة الأدوار
        self.roles_list = QListWidget()
        with Session(self.engine) as session:
            roles = session.query(Role).all()
            for role in roles:
                item = QListWidgetItem(role.name)
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                item.setCheckState(Qt.Unchecked)
                self.roles_list.addItem(item)
        
        # ملء البيانات في حالة التعديل
        if self.user:
            self.username.setText(self.user.username)
            self.username.setEnabled(False)
            self.fullname.setText(self.user.full_name or "")
            self.email.setText(self.user.email or "")
            self.is_active.setChecked(self.user.is_active)
            
            # تحديد الأدوار الحالية
            for i in range(self.roles_list.count()):
                item = self.roles_list.item(i)
                if any(role.name == item.text() for role in self.user.roles):
                    item.setCheckState(Qt.Checked)
        
        form_layout.addRow("اسم المستخدم:", self.username)
        form_layout.addRow("كلمة المرور:", self.password)
        form_layout.addRow("الاسم الكامل:", self.fullname)
        form_layout.addRow("البريد الإلكتروني:", self.email)
        form_layout.addRow("الحالة:", self.is_active)
        form_layout.addRow("الأدوار:", self.roles_list)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setProperty("primary", True)
        save_btn.clicked.connect(self.save_user)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def save_user(self):
        username = self.username.text()
        password = self.password.text()
        fullname = self.fullname.text()
        email = self.email.text()
        is_active = self.is_active.isChecked()
        
        if not username:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم")
            return
            
        if not self.user and not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال كلمة المرور")
            return
            
        try:
            with Session(self.engine) as session:
                if not self.user:
                    # إنشاء مستخدم جديد
                    user = User(
                        username=username,
                        full_name=fullname,
                        email=email,
                        is_active=is_active
                    )
                    user.set_password(password)
                    session.add(user)
                else:
                    # تحديث المستخدم الحالي من نفس الجلسة
                    user = session.query(User).filter(User.username == self.user.username).first()
                    if password:
                        user.set_password(password)
                    user.full_name = fullname
                    user.email = email
                    user.is_active = is_active
                
                # تحديث الأدوار
                selected_roles = []
                for i in range(self.roles_list.count()):
                    item = self.roles_list.item(i)
                    if item.checkState() == Qt.Checked:
                        role = session.query(Role).filter(Role.name == item.text()).first()
                        if role:
                            selected_roles.append(role)
                
                user.roles = selected_roles
                session.commit()
                
                # إذا كان هذا المستخدم الحالي، حدث رسالة الترحيب في النافذة الرئيسية
                if self.user and hasattr(self.parent(), 'update_welcome_message'):
                    self.parent().update_welcome_message(fullname)
                
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المستخدم: {str(e)}")

# تطبيق الإصلاح
