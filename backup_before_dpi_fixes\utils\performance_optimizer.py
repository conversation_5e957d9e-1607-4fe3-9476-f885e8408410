#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن الأداء - تحسين استعلامات قاعدة البيانات والذاكرة
Performance Optimizer - Database queries and memory optimization
"""

from sqlalchemy.orm import Session
from sqlalchemy import text, func, and_, or_
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from datetime import datetime, timedelta
import gc
import psutil
import os


class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    def __init__(self, engine):
        self.engine = engine
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        with Session(self.engine) as session:
            try:
                # تحليل الجداول
                session.execute(text("ANALYZE"))
                
                # إعادة فهرسة
                session.execute(text("REINDEX"))
                
                # تنظيف قاعدة البيانات
                session.execute(text("VACUUM"))
                
                session.commit()
                return True
                
            except Exception as e:
                print(f"خطأ في تحسين قاعدة البيانات: {e}")
                session.rollback()
                return False
    
    def create_indexes(self):
        """إنشاء فهارس لتحسين الأداء"""
        with Session(self.engine) as session:
            try:
                # فهارس للمعاملات
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_transaction_date_type 
                    ON transactions(date, type)
                """))
                
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_transaction_customer 
                    ON transactions(customer_id) WHERE customer_id IS NOT NULL
                """))
                
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_transaction_supplier 
                    ON transactions(supplier_id) WHERE supplier_id IS NOT NULL
                """))
                
                # فهارس لعناصر المعاملات
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_transaction_item_product 
                    ON transaction_items(product_id)
                """))
                
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_transaction_item_transaction 
                    ON transaction_items(transaction_id)
                """))
                
                # فهارس للمنتجات
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_product_category 
                    ON products(category) WHERE category IS NOT NULL
                """))
                
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_product_quantity 
                    ON products(quantity, min_quantity)
                """))
                
                session.commit()
                return True
                
            except Exception as e:
                print(f"خطأ في إنشاء الفهارس: {e}")
                session.rollback()
                return False
    
    def get_optimized_sales_data(self, start_date, end_date, limit=100):
        """الحصول على بيانات المبيعات محسنة"""
        with Session(self.engine) as session:
            # استعلام محسن للمبيعات
            query = session.query(
                func.date(Transaction.date).label('sale_date'),
                func.sum(Transaction.total_amount).label('total_sales'),
                func.count(Transaction.id).label('transactions_count')
            ).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date)
            ).group_by(
                func.date(Transaction.date)
            ).order_by(
                func.date(Transaction.date).desc()
            ).limit(limit)
            
            return query.all()
    
    def get_optimized_top_products(self, start_date, end_date, limit=10):
        """الحصول على أفضل المنتجات محسن"""
        with Session(self.engine) as session:
            # استعلام محسن لأفضل المنتجات
            query = session.query(
                Product.name,
                Product.code,
                func.sum(TransactionItem.quantity).label('total_quantity'),
                func.sum(TransactionItem.quantity * TransactionItem.unit_price).label('total_value'),
                func.count(TransactionItem.id).label('sales_count')
            ).join(
                TransactionItem, Product.id == TransactionItem.product_id
            ).join(
                Transaction, TransactionItem.transaction_id == Transaction.id
            ).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date.between(start_date, end_date),
                Product.is_active == True
            ).group_by(
                Product.id, Product.name, Product.code
            ).order_by(
                func.sum(TransactionItem.quantity).desc()
            ).limit(limit)
            
            return query.all()
    
    def get_optimized_low_stock(self, limit=50):
        """الحصول على المنتجات منخفضة المخزون محسن"""
        with Session(self.engine) as session:
            # استعلام محسن للمنتجات منخفضة المخزون
            query = session.query(
                Product.name,
                Product.code,
                Product.quantity,
                Product.min_quantity,
                Product.category,
                (Product.min_quantity - Product.quantity).label('shortage')
            ).filter(
                Product.quantity <= Product.min_quantity,
                Product.is_active == True
            ).order_by(
                (Product.min_quantity - Product.quantity).desc()
            ).limit(limit)
            
            return query.all()


class MemoryOptimizer:
    """محسن الذاكرة"""
    
    @staticmethod
    def get_memory_usage():
        """الحصول على استخدام الذاكرة"""
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent()
        }
    
    @staticmethod
    def cleanup_memory():
        """تنظيف الذاكرة"""
        # تشغيل جامع القمامة
        collected = gc.collect()
        
        # الحصول على معلومات الذاكرة بعد التنظيف
        memory_after = MemoryOptimizer.get_memory_usage()
        
        return {
            'collected_objects': collected,
            'memory_usage': memory_after
        }
    
    @staticmethod
    def optimize_for_large_datasets():
        """تحسين للبيانات الكبيرة"""
        # تعديل إعدادات جامع القمامة
        gc.set_threshold(700, 10, 10)
        
        # تشغيل تنظيف فوري
        gc.collect()


class QueryOptimizer:
    """محسن الاستعلامات"""
    
    def __init__(self, engine):
        self.engine = engine
    
    def get_paginated_transactions(self, page=1, per_page=50, transaction_type=None, 
                                 start_date=None, end_date=None):
        """الحصول على المعاملات مع التصفح"""
        with Session(self.engine) as session:
            query = session.query(Transaction)
            
            # تطبيق المرشحات
            if transaction_type:
                query = query.filter(Transaction.type == transaction_type)
            
            if start_date:
                query = query.filter(Transaction.date >= start_date)
            
            if end_date:
                query = query.filter(Transaction.date <= end_date)
            
            # ترتيب وتصفح
            query = query.order_by(Transaction.date.desc())
            
            # حساب الإجمالي
            total = query.count()
            
            # تطبيق التصفح
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            
            return {
                'items': items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            }
    
    def get_aggregated_data(self, start_date, end_date):
        """الحصول على البيانات المجمعة بكفاءة"""
        with Session(self.engine) as session:
            # استعلام واحد للحصول على جميع الإحصائيات
            result = session.execute(text("""
                SELECT 
                    transaction_type,
                    COUNT(*) as count,
                    SUM(total_amount) as total,
                    AVG(total_amount) as average,
                    MIN(total_amount) as minimum,
                    MAX(total_amount) as maximum
                FROM transactions 
                WHERE date BETWEEN :start_date AND :end_date
                GROUP BY transaction_type
            """), {
                'start_date': start_date,
                'end_date': end_date
            }).fetchall()
            
            # تنظيم النتائج
            stats = {}
            for row in result:
                stats[row.transaction_type] = {
                    'count': row.count,
                    'total': float(row.total or 0),
                    'average': float(row.average or 0),
                    'minimum': float(row.minimum or 0),
                    'maximum': float(row.maximum or 0)
                }
            
            return stats


class CacheManager:
    """مدير التخزين المؤقت"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 دقائق
    
    def get(self, key):
        """الحصول على قيمة من التخزين المؤقت"""
        if key in self.cache:
            data, timestamp = self.cache[key]
            if datetime.now().timestamp() - timestamp < self.cache_timeout:
                return data
            else:
                del self.cache[key]
        return None
    
    def set(self, key, value):
        """حفظ قيمة في التخزين المؤقت"""
        self.cache[key] = (value, datetime.now().timestamp())
    
    def clear(self):
        """مسح التخزين المؤقت"""
        self.cache.clear()
    
    def cleanup_expired(self):
        """تنظيف البيانات المنتهية الصلاحية"""
        current_time = datetime.now().timestamp()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp >= self.cache_timeout
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        return len(expired_keys)


# إنشاء مثيل عام لمدير التخزين المؤقت
cache_manager = CacheManager()
