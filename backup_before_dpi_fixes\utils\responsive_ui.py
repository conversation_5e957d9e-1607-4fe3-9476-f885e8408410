#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الواجهة المتجاوبة - تكيف مع أحجام الشاشات المختلفة
Responsive UI System - Adapt to different screen sizes
"""

from PyQt5.QtWidgets import QApplication, QDesktopWidget
from PyQt5.QtCore import QRect, QSize, Qt
from PyQt5.QtGui import QFont, QFontMetrics
import json
import os


class ResponsiveManager:
    """مدير الواجهة المتجاوبة"""
    
    def __init__(self):
        self.screen_info = self.get_screen_info()
        self.scale_factor = self.calculate_scale_factor()
        self.font_scale = self.calculate_font_scale()
        self.load_saved_settings()
    
    def get_screen_info(self):
        """الحصول على معلومات الشاشة"""
        app = QApplication.instance()
        if not app:
            return {
                'width': 1920,
                'height': 1080,
                'dpi': 96,
                'device_pixel_ratio': 1.0
            }
        
        desktop = app.desktop()
        screen = desktop.screenGeometry()
        
        # الحصول على DPI
        dpi_x = desktop.logicalDpiX()
        dpi_y = desktop.logicalDpiY()
        avg_dpi = (dpi_x + dpi_y) / 2
        
        # الحصول على device pixel ratio
        device_pixel_ratio = app.devicePixelRatio()
        
        return {
            'width': screen.width(),
            'height': screen.height(),
            'dpi': avg_dpi,
            'device_pixel_ratio': device_pixel_ratio,
            'available_geometry': desktop.availableGeometry()
        }
    
    def calculate_scale_factor(self):
        """حساب عامل التكبير بناءً على حجم الشاشة"""
        width = self.screen_info['width']
        height = self.screen_info['height']
        dpi = self.screen_info['dpi']
        device_ratio = self.screen_info['device_pixel_ratio']
        
        # الشاشة المرجعية (1920x1080 @ 96 DPI)
        reference_width = 1920
        reference_height = 1080
        reference_dpi = 96
        
        # حساب عامل التكبير بناءً على العرض والارتفاع
        width_scale = width / reference_width
        height_scale = height / reference_height
        
        # حساب عامل التكبير بناءً على DPI
        dpi_scale = dpi / reference_dpi
        
        # استخدام المتوسط مع وزن أكبر للعرض
        base_scale = (width_scale * 0.6 + height_scale * 0.4)
        
        # تطبيق عامل DPI
        final_scale = base_scale * dpi_scale * device_ratio
        
        # تحديد حدود آمنة للتكبير
        final_scale = max(0.5, min(3.0, final_scale))
        
        return final_scale
    
    def calculate_font_scale(self):
        """حساب عامل تكبير الخطوط"""
        dpi = self.screen_info['dpi']
        device_ratio = self.screen_info['device_pixel_ratio']
        
        # عامل تكبير الخط بناءً على DPI
        font_scale = (dpi / 96) * device_ratio
        
        # حدود آمنة للخطوط
        font_scale = max(0.7, min(2.5, font_scale))
        
        return font_scale
    
    def get_screen_category(self):
        """تحديد فئة الشاشة"""
        width = self.screen_info['width']
        height = self.screen_info['height']
        
        if width <= 1366 and height <= 768:
            return 'small'  # شاشات صغيرة
        elif width <= 1920 and height <= 1080:
            return 'medium'  # شاشات متوسطة
        elif width <= 2560 and height <= 1440:
            return 'large'  # شاشات كبيرة
        else:
            return 'xlarge'  # شاشات كبيرة جداً
    
    def scale_size(self, size):
        """تكبير الحجم بناءً على عامل التكبير"""
        if isinstance(size, (int, float)):
            return int(size * self.scale_factor)
        elif isinstance(size, QSize):
            return QSize(
                int(size.width() * self.scale_factor),
                int(size.height() * self.scale_factor)
            )
        elif isinstance(size, (tuple, list)) and len(size) == 2:
            return (
                int(size[0] * self.scale_factor),
                int(size[1] * self.scale_factor)
            )
        return size
    
    def scale_font_size(self, font_size):
        """تكبير حجم الخط"""
        return max(8, int(font_size * self.font_scale))
    
    def get_window_size(self, base_width, base_height, min_width=None, min_height=None):
        """الحصول على حجم النافذة المناسب"""
        # تكبير الحجم الأساسي
        scaled_width = self.scale_size(base_width)
        scaled_height = self.scale_size(base_height)
        
        # التأكد من عدم تجاوز حجم الشاشة
        available = self.screen_info['available_geometry']
        max_width = int(available.width() * 0.95)  # 95% من عرض الشاشة
        max_height = int(available.height() * 0.9)  # 90% من ارتفاع الشاشة
        
        scaled_width = min(scaled_width, max_width)
        scaled_height = min(scaled_height, max_height)
        
        # تطبيق الحد الأدنى إذا تم تحديده
        if min_width:
            scaled_width = max(scaled_width, self.scale_size(min_width))
        if min_height:
            scaled_height = max(scaled_height, self.scale_size(min_height))
        
        return scaled_width, scaled_height
    
    def get_centered_geometry(self, width, height):
        """الحصول على موقع النافذة في المنتصف"""
        available = self.screen_info['available_geometry']
        
        x = (available.width() - width) // 2 + available.x()
        y = (available.height() - height) // 2 + available.y()
        
        return QRect(x, y, width, height)
    
    def get_responsive_margins(self, base_margin=10):
        """الحصول على هوامش متجاوبة"""
        scaled_margin = self.scale_size(base_margin)
        return max(5, min(50, scaled_margin))  # حدود آمنة للهوامش
    
    def get_responsive_spacing(self, base_spacing=10):
        """الحصول على مسافات متجاوبة"""
        scaled_spacing = self.scale_size(base_spacing)
        return max(2, min(30, scaled_spacing))  # حدود آمنة للمسافات
    
    def create_responsive_font(self, base_size=12, weight=None, family=None):
        """إنشاء خط متجاوب"""
        scaled_size = self.scale_font_size(base_size)
        
        font = QFont()
        
        if family:
            font.setFamily(family)
        else:
            # استخدام خط افتراضي مناسب للنظام
            font.setFamily("Segoe UI, Arial, sans-serif")
        
        font.setPointSize(scaled_size)
        
        if weight:
            font.setWeight(weight)
        
        return font
    
    def get_button_size(self, text="", base_width=100, base_height=35):
        """الحصول على حجم الزر المناسب للنص"""
        # تكبير الحجم الأساسي
        scaled_width = self.scale_size(base_width)
        scaled_height = self.scale_size(base_height)
        
        # حساب العرض المطلوب للنص
        if text:
            font = self.create_responsive_font(12)
            metrics = QFontMetrics(font)
            text_width = metrics.width(text) + self.scale_size(20)  # إضافة هامش
            scaled_width = max(scaled_width, text_width)
        
        return scaled_width, scaled_height
    
    def save_settings(self):
        """حفظ إعدادات الواجهة المتجاوبة"""
        settings = {
            'screen_info': self.screen_info,
            'scale_factor': self.scale_factor,
            'font_scale': self.font_scale,
            'last_update': self.get_current_timestamp()
        }
        
        try:
            with open('responsive_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الواجهة المتجاوبة: {e}")
    
    def load_saved_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if os.path.exists('responsive_settings.json'):
                with open('responsive_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # التحقق من تغيير الشاشة
                saved_screen = settings.get('screen_info', {})
                current_screen = self.screen_info
                
                if (saved_screen.get('width') != current_screen['width'] or 
                    saved_screen.get('height') != current_screen['height'] or
                    abs(saved_screen.get('dpi', 96) - current_screen['dpi']) > 10):
                    # الشاشة تغيرت، إعادة حساب العوامل
                    print("🔄 تم اكتشاف تغيير في الشاشة، إعادة حساب عوامل التكبير...")
                    self.save_settings()
                else:
                    # استخدام العوامل المحفوظة
                    self.scale_factor = settings.get('scale_factor', self.scale_factor)
                    self.font_scale = settings.get('font_scale', self.font_scale)
        
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الواجهة المتجاوبة: {e}")
    
    def get_current_timestamp(self):
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def print_debug_info(self):
        """طباعة معلومات التشخيص"""
        print("🖥️ معلومات الشاشة:")
        print(f"   الدقة: {self.screen_info['width']}x{self.screen_info['height']}")
        print(f"   DPI: {self.screen_info['dpi']:.1f}")
        print(f"   Device Pixel Ratio: {self.screen_info['device_pixel_ratio']}")
        print(f"   فئة الشاشة: {self.get_screen_category()}")
        print(f"   عامل التكبير: {self.scale_factor:.2f}")
        print(f"   عامل تكبير الخط: {self.font_scale:.2f}")


# إنشاء مثيل عام للاستخدام
responsive_manager = ResponsiveManager()


def setup_responsive_window(window, base_width=1200, base_height=800, min_width=800, min_height=600):
    """إعداد نافذة متجاوبة"""
    # الحصول على الحجم المناسب
    width, height = responsive_manager.get_window_size(base_width, base_height, min_width, min_height)
    
    # تعيين الحجم والموقع
    geometry = responsive_manager.get_centered_geometry(width, height)
    window.setGeometry(geometry)
    
    # تعيين الحد الأدنى
    min_w, min_h = responsive_manager.scale_size(min_width), responsive_manager.scale_size(min_height)
    window.setMinimumSize(min_w, min_h)
    
    return width, height


def create_responsive_stylesheet(base_font_size=12):
    """إنشاء stylesheet متجاوب للأحجام فقط (بدون ألوان)"""
    font_size = responsive_manager.scale_font_size(base_font_size)
    margin = responsive_manager.get_responsive_margins(10)
    spacing = responsive_manager.get_responsive_spacing(5)

    return f"""
        QWidget {{
            font-size: {font_size}px;
        }}

        QFrame {{
            margin: {margin//2}px;
        }}

        QPushButton {{
            padding: {spacing}px {spacing*2}px;
            font-size: {font_size}px;
            min-height: {responsive_manager.scale_size(30)}px;
        }}

        QLabel {{
            font-size: {font_size}px;
        }}

        QLineEdit, QTextEdit, QComboBox {{
            font-size: {font_size}px;
            padding: {spacing//2}px;
            min-height: {responsive_manager.scale_size(25)}px;
        }}

        QTableWidget {{
            font-size: {font_size-1}px;
        }}

        QHeaderView::section {{
            font-size: {font_size}px;
            padding: {spacing}px;
            min-height: {responsive_manager.scale_size(30)}px;
        }}
    """


def create_responsive_sizing_only():
    """إنشاء تنسيق للأحجام فقط بدون ألوان أو خلفيات"""
    font_size = responsive_manager.scale_font_size(12)
    margin = responsive_manager.get_responsive_margins(8)
    spacing = responsive_manager.get_responsive_spacing(4)

    return {
        'font_size': font_size,
        'margin': margin,
        'spacing': spacing,
        'button_height': responsive_manager.scale_size(30),
        'input_height': responsive_manager.scale_size(25),
        'icon_size': responsive_manager.scale_size(24)
    }


def apply_responsive_layout(layout, base_margin=10, base_spacing=10):
    """تطبيق تخطيط متجاوب"""
    margin = responsive_manager.get_responsive_margins(base_margin)
    spacing = responsive_manager.get_responsive_spacing(base_spacing)
    
    layout.setContentsMargins(margin, margin, margin, margin)
    layout.setSpacing(spacing)


def get_responsive_icon_size(base_size=24):
    """الحصول على حجم الأيقونة المتجاوب"""
    return responsive_manager.scale_size(base_size)


# طباعة معلومات التشخيص عند التحميل
if __name__ == "__main__":
    responsive_manager.print_debug_info()
