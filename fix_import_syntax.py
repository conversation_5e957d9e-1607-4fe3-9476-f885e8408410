#!/usr/bin/env python3
"""
إصلاح أخطاء الاستيراد في ملفات GUI
"""

import os
import re
from pathlib import Path


def fix_import_syntax():
    """إصلاح أخطاء الاستيراد في جميع ملفات GUI"""
    gui_path = Path("gui")
    
    if not gui_path.exists():
        print("❌ مجلد gui غير موجود")
        return
    
    fixed_files = []
    errors = []
    
    # البحث عن جميع ملفات Python
    for py_file in gui_path.glob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن النمط الخاطئ
            pattern = r'(from PyQt5\.QtWidgets import \([^)]*)\nfrom utils\.dpi_manager import dpi_manager\n([^)]*\))'
            
            if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                # إصلاح النمط
                fixed_content = re.sub(
                    pattern,
                    r'\1\2\nfrom utils.dpi_manager import dpi_manager',
                    content,
                    flags=re.MULTILINE | re.DOTALL
                )
                
                # حفظ الملف المصحح
                with open(py_file, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                fixed_files.append(str(py_file))
                print(f"✅ تم إصلاح {py_file.name}")
            
        except Exception as e:
            error_msg = f"خطأ في إصلاح {py_file.name}: {e}"
            errors.append(error_msg)
            print(f"❌ {error_msg}")
    
    print(f"\n📊 النتائج:")
    print(f"   • تم إصلاح {len(fixed_files)} ملف")
    print(f"   • عدد الأخطاء: {len(errors)}")
    
    if fixed_files:
        print(f"\n✅ الملفات المصححة:")
        for file in fixed_files:
            print(f"   • {file}")
    
    if errors:
        print(f"\n❌ الأخطاء:")
        for error in errors:
            print(f"   • {error}")


if __name__ == "__main__":
    print("🔧 إصلاح أخطاء الاستيراد في ملفات GUI...")
    fix_import_syntax()
    print("✅ انتهى الإصلاح!")
