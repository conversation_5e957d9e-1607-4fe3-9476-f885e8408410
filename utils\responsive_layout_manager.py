"""
مدير التخطيط المتجاوب - حل جذري لمشاكل الشاشات الصغيرة
يعيد تنظيم الواجهات تلقائياً حسب حجم الشاشة
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from utils.dpi_manager import dpi_manager


class ResponsiveLayoutManager:
    """مدير التخطيط المتجاوب"""
    
    def __init__(self):
        self.screen_size = dpi_manager.get_screen_size()
        self.is_small_screen = self.screen_size.width() < 1400 or self.screen_size.height() < 900
        self.is_very_small_screen = self.screen_size.width() < 1200 or self.screen_size.height() < 800
        
    def setup_responsive_invoice_layout(self, widget):
        """إعداد تخطيط متجاوب للفواتير"""
        if not hasattr(widget, 'layout') or not widget.layout():
            return
            
        main_layout = widget.layout()
        
        if self.is_small_screen:
            # إعادة تنظيم الفاتورة للشاشات الصغيرة
            self._reorganize_invoice_for_small_screen(widget, main_layout)
        
        # تطبيق أحجام متجاوبة
        self._apply_responsive_sizes(widget)
        
    def _reorganize_invoice_for_small_screen(self, widget, main_layout):
        """إعادة تنظيم الفاتورة للشاشات الصغيرة"""
        
        # البحث عن العناصر الرئيسية
        header_widgets = []
        search_widgets = []
        table_widgets = []
        footer_widgets = []
        
        # تجميع العناصر حسب النوع
        for i in range(main_layout.count()):
            item = main_layout.itemAt(i)
            if item and item.widget():
                widget_obj = item.widget()
                
                if isinstance(widget_obj, QFrame):
                    # فحص محتوى الإطار
                    if self._contains_search_elements(widget_obj):
                        search_widgets.append(widget_obj)
                    elif self._contains_table_elements(widget_obj):
                        table_widgets.append(widget_obj)
                    elif self._contains_buttons(widget_obj):
                        footer_widgets.append(widget_obj)
                    else:
                        header_widgets.append(widget_obj)
                elif isinstance(widget_obj, QTableWidget):
                    table_widgets.append(widget_obj)
                elif isinstance(widget_obj, (QPushButton, QHBoxLayout)):
                    footer_widgets.append(widget_obj)
                else:
                    header_widgets.append(widget_obj)
        
        # إنشاء تخطيط جديد متجاوب
        self._create_responsive_layout(widget, header_widgets, search_widgets, table_widgets, footer_widgets)
    
    def _contains_search_elements(self, widget):
        """فحص إذا كان العنصر يحتوي على عناصر بحث"""
        if not widget.layout():
            return False
            
        for i in range(widget.layout().count()):
            item = widget.layout().itemAt(i)
            if item and item.widget():
                w = item.widget()
                if isinstance(w, QLineEdit) and ("بحث" in w.placeholderText() or "البحث" in w.placeholderText()):
                    return True
        return False
    
    def _contains_table_elements(self, widget):
        """فحص إذا كان العنصر يحتوي على جداول"""
        if not widget.layout():
            return False
            
        for i in range(widget.layout().count()):
            item = widget.layout().itemAt(i)
            if item and item.widget():
                if isinstance(item.widget(), QTableWidget):
                    return True
        return False
    
    def _contains_buttons(self, widget):
        """فحص إذا كان العنصر يحتوي على أزرار"""
        if not widget.layout():
            return False
            
        button_count = 0
        for i in range(widget.layout().count()):
            item = widget.layout().itemAt(i)
            if item and item.widget():
                if isinstance(item.widget(), QPushButton):
                    button_count += 1
        
        return button_count >= 2  # إذا كان يحتوي على أزرار متعددة
    
    def _create_responsive_layout(self, parent_widget, header_widgets, search_widgets, table_widgets, footer_widgets):
        """إنشاء تخطيط جديد متجاوب"""
        
        # إنشاء تخطيط رئيسي جديد
        new_layout = QVBoxLayout()
        new_layout.setSpacing(dpi_manager.scale_value(5))
        new_layout.setContentsMargins(dpi_manager.scale_value(5), dpi_manager.scale_value(5), 
                                     dpi_manager.scale_value(5), dpi_manager.scale_value(5))
        
        # 1. منطقة الرأس (مضغوطة)
        if header_widgets:
            header_frame = QFrame()
            header_layout = QVBoxLayout(header_frame)
            header_layout.setSpacing(dpi_manager.scale_value(3))
            
            for widget in header_widgets:
                header_layout.addWidget(widget)
                self._make_widget_compact(widget)
            
            header_frame.setMaximumHeight(dpi_manager.scale_value(120))
            new_layout.addWidget(header_frame)
        
        # 2. منطقة البحث (في صف واحد)
        if search_widgets:
            search_frame = QFrame()
            search_layout = QHBoxLayout(search_frame)
            search_layout.setSpacing(dpi_manager.scale_value(5))
            
            for widget in search_widgets:
                search_layout.addWidget(widget)
                self._optimize_search_widget(widget)
            
            search_frame.setMaximumHeight(dpi_manager.scale_value(60))
            new_layout.addWidget(search_frame)
        
        # 3. منطقة الجدول (تأخذ معظم المساحة)
        if table_widgets:
            table_frame = QFrame()
            table_layout = QVBoxLayout(table_frame)
            table_layout.setSpacing(dpi_manager.scale_value(2))
            
            for widget in table_widgets:
                table_layout.addWidget(widget)
                self._optimize_table_widget(widget)
            
            new_layout.addWidget(table_frame, 1)  # تأخذ المساحة المتبقية
        
        # 4. منطقة الأزرار (مضغوطة)
        if footer_widgets:
            footer_frame = QFrame()
            footer_layout = QHBoxLayout(footer_frame)
            footer_layout.setSpacing(dpi_manager.scale_value(5))
            
            for widget in footer_widgets:
                footer_layout.addWidget(widget)
                self._optimize_button_widget(widget)
            
            footer_frame.setMaximumHeight(dpi_manager.scale_value(50))
            new_layout.addWidget(footer_frame)
        
        # استبدال التخطيط القديم
        old_layout = parent_widget.layout()
        if old_layout:
            QWidget().setLayout(old_layout)  # حذف التخطيط القديم
        
        parent_widget.setLayout(new_layout)
    
    def _make_widget_compact(self, widget):
        """جعل العنصر مضغوط"""
        if hasattr(widget, 'setMaximumHeight'):
            widget.setMaximumHeight(dpi_manager.scale_value(35))
        
        if isinstance(widget, QFrame) and widget.layout():
            widget.layout().setSpacing(dpi_manager.scale_value(2))
            widget.layout().setContentsMargins(2, 2, 2, 2)
    
    def _optimize_search_widget(self, widget):
        """تحسين عنصر البحث"""
        if isinstance(widget, QLineEdit):
            widget.setMaximumHeight(dpi_manager.scale_value(30))
            widget.setMinimumWidth(dpi_manager.scale_value(200))
        
        if isinstance(widget, QFrame) and widget.layout():
            # ترتيب عناصر البحث أفقياً
            layout = widget.layout()
            if isinstance(layout, QVBoxLayout):
                # تحويل إلى تخطيط أفقي
                new_layout = QHBoxLayout()
                new_layout.setSpacing(dpi_manager.scale_value(5))
                
                while layout.count() > 0:
                    item = layout.takeAt(0)
                    if item.widget():
                        new_layout.addWidget(item.widget())
                
                widget.setLayout(new_layout)
    
    def _optimize_table_widget(self, widget):
        """تحسين عنصر الجدول"""
        if isinstance(widget, QTableWidget):
            # تحسين أعمدة الجدول للشاشة الصغيرة
            header = widget.horizontalHeader()
            header.setStretchLastSection(True)
            
            # تقليل عرض الأعمدة
            for col in range(widget.columnCount()):
                current_width = widget.columnWidth(col)
                new_width = min(current_width, dpi_manager.scale_value(120))
                widget.setColumnWidth(col, new_width)
            
            # تحسين ارتفاع الصفوف
            widget.verticalHeader().setDefaultSectionSize(dpi_manager.scale_value(25))
    
    def _optimize_button_widget(self, widget):
        """تحسين عنصر الأزرار"""
        if isinstance(widget, QPushButton):
            widget.setMaximumHeight(dpi_manager.scale_value(35))
            widget.setMinimumWidth(dpi_manager.scale_value(80))
        
        if isinstance(widget, QFrame) and widget.layout():
            layout = widget.layout()
            layout.setSpacing(dpi_manager.scale_value(3))
    
    def _apply_responsive_sizes(self, widget):
        """تطبيق أحجام متجاوبة"""
        
        # تطبيق خط متجاوب
        font = widget.font()
        if self.is_very_small_screen:
            font.setPointSize(dpi_manager.get_scaled_font_size(9))
        elif self.is_small_screen:
            font.setPointSize(dpi_manager.get_scaled_font_size(10))
        else:
            font.setPointSize(dpi_manager.get_scaled_font_size(11))
        
        widget.setFont(font)
        
        # تطبيق الخط على العناصر الفرعية
        for child in widget.findChildren(QWidget):
            child.setFont(font)
    
    def setup_responsive_dashboard(self, widget):
        """إعداد لوحة التحكم المتجاوبة"""
        if self.is_small_screen:
            self._reorganize_dashboard_for_small_screen(widget)
    
    def _reorganize_dashboard_for_small_screen(self, widget):
        """إعادة تنظيم لوحة التحكم للشاشات الصغيرة"""
        
        # البحث عن البطاقات والعناصر
        cards = widget.findChildren(QFrame)
        
        for card in cards:
            if card.layout():
                # تقليل المسافات
                card.layout().setSpacing(dpi_manager.scale_value(3))
                card.layout().setContentsMargins(5, 5, 5, 5)
                
                # تقليل حجم البطاقات
                card.setMaximumHeight(dpi_manager.scale_value(100))


# إنشاء مثيل عام
responsive_manager = ResponsiveLayoutManager()
