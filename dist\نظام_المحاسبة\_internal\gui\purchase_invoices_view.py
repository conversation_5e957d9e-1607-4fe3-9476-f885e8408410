from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QTableWidgetItem, QMessageBox,
                             QFrame, QGridLayout, QHeaderView, QDateEdit, QDialog)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QColor
from sqlalchemy.orm import Session
from database.models import Transaction, Supplier, TransactionType, TransactionItem, Product
from datetime import datetime
from utils.advanced_invoice_printer import show_advanced_print_dialog

class PurchaseInvoicesViewWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.load_invoices()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # عنوان الصفحة
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(243, 156, 18, 0.1);
                border: 2px solid white;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 10px;
            }
            QLabel { color: #F39C12; font-weight: bold; }
        """)
        header_layout = QHBoxLayout()
        header_frame.setLayout(header_layout)

        icon_label = QLabel("🧾")
        icon_label.setStyleSheet("font-size: 48px; color: #F39C12;")
        title_label = QLabel("عرض فواتير المشتريات")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #F39C12;")
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        layout.addWidget(header_frame)

        # شريط البحث والفلترة
        filter_frame = QFrame()
        filter_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        filter_layout = QHBoxLayout()
        filter_frame.setLayout(filter_layout)

        filter_layout.addWidget(QLabel("رقم الفاتورة:"))
        self.invoice_search = QLineEdit()
        self.invoice_search.setPlaceholderText("ابحث برقم الفاتورة...")
        self.invoice_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #F39C12;
                border-radius: 5px;
                min-width: 150px;
            }
        """)
        self.invoice_search.textChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.invoice_search)

        filter_layout.addWidget(QLabel("المورد:"))
        self.supplier_search = QLineEdit()
        self.supplier_search.setPlaceholderText("ابحث باسم المورد...")
        self.supplier_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #F39C12;
                border-radius: 5px;
                min-width: 150px;
            }
        """)
        self.supplier_search.textChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.supplier_search)

        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 2px solid #F39C12;
                border-radius: 5px;
            }
        """)
        self.date_from.dateChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.date_from)

        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 2px solid #F39C12;
                border-radius: 5px;
            }
        """)
        self.date_to.dateChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.date_to)

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        refresh_btn.clicked.connect(self.load_invoices)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        layout.addWidget(filter_frame)

        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                font-size: 16px;
                font-weight: bold;
                gridline-color: #D3D3D3;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #0078D4;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C3E50;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                border: none;
            }
        """)
        self.invoices_table.setColumnCount(8)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المورد", "الإجمالي", "المدفوع", "المتبقي", "الحالة", "الإجراءات"
        ])
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        header.setSectionResizeMode(6, QHeaderView.Fixed)
        header.setSectionResizeMode(7, QHeaderView.Fixed)
        header.resizeSection(0, 140)
        header.resizeSection(1, 140)
        header.resizeSection(3, 140)
        header.resizeSection(4, 140)
        header.resizeSection(5, 140)
        header.resizeSection(6, 120)
        header.resizeSection(7, 300)
        self.invoices_table.verticalHeader().setDefaultSectionSize(90)
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        layout.addWidget(self.invoices_table)

    def load_invoices(self):
        try:
            with Session(self.engine) as session:
                invoices = session.query(Transaction).filter(
                    Transaction.type == TransactionType.PURCHASE
                ).order_by(Transaction.id.desc()).all()
                self.invoices_table.setRowCount(len(invoices))
                for row, invoice in enumerate(invoices):
                    self.invoices_table.setItem(row, 0, QTableWidgetItem(f"{invoice.id:06d}"))
                    date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
                    self.invoices_table.setItem(row, 1, QTableWidgetItem(date_str))
                    supplier_name = "-"
                    if invoice.supplier_id:
                        supplier = session.query(Supplier).get(invoice.supplier_id)
                        if supplier:
                            supplier_name = supplier.name
                    self.invoices_table.setItem(row, 2, QTableWidgetItem(supplier_name))
                    total = invoice.total_amount or 0
                    self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{total:,.0f}"))
                    paid = invoice.paid_amount or 0
                    self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{paid:,.0f}"))
                    remaining = total - paid
                    self.invoices_table.setItem(row, 5, QTableWidgetItem(f"{remaining:,.0f}"))
                    if remaining <= 0:
                        status = "✅ مدفوعة"
                        status_color = QColor(46, 204, 113)
                    else:
                        status = "⏳ جزئية"
                        status_color = QColor(241, 196, 15)
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(status_color)
                    status_item.setForeground(QColor(255, 255, 255))
                    self.invoices_table.setItem(row, 6, status_item)
                    actions_widget = QWidget()
                    layout_actions = QHBoxLayout()
                    layout_actions.setContentsMargins(8, 8, 8, 8)
                    layout_actions.setSpacing(8)
                    view_btn = QPushButton("👁️ تفاصيل")
                    view_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #F39C12;
                            color: white;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 6px 10px;
                            border: none;
                            border-radius: 4px;
                            min-width: 80px;
                            min-height: 30px;
                        }
                        QPushButton:hover {
                            background-color: #e67e22;
                        }
                    """)
                    view_btn.clicked.connect(lambda _, inv_id=invoice.id: self.view_invoice_details(inv_id))
                    layout_actions.addWidget(view_btn)
                    
                    # إضافة زر طباعة الفاتورة
                    print_btn = QPushButton("🖨️ طباعة")
                    print_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #28A745;
                            color: white;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 6px 10px;
                            border: none;
                            border-radius: 4px;
                            min-width: 80px;
                            min-height: 30px;
                        }
                        QPushButton:hover {
                            background-color: #218838;
                        }
                    """)
                    print_btn.clicked.connect(lambda _, inv_id=invoice.id: self.print_invoice(inv_id))
                    layout_actions.addWidget(print_btn)
                    
                    actions_widget.setLayout(layout_actions)
                    self.invoices_table.setCellWidget(row, 7, actions_widget)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل فواتير المشتريات:\n{str(e)}")

    def filter_invoices(self):
        invoice_search = self.invoice_search.text().strip()
        supplier_search = self.supplier_search.text().strip().lower()
        date_from = self.date_from.date().toPyDate()
        date_to = self.date_to.date().toPyDate()
        for row in range(self.invoices_table.rowCount()):
            show_row = True
            if invoice_search:
                invoice_num = self.invoices_table.item(row, 0).text()
                if invoice_search not in invoice_num:
                    show_row = False
            if supplier_search and show_row:
                supplier_name = self.invoices_table.item(row, 2).text().lower()
                if supplier_search not in supplier_name:
                    show_row = False
            if show_row:
                date_str = self.invoices_table.item(row, 1).text()
                if date_str:
                    try:
                        invoice_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                        if not (date_from <= invoice_date <= date_to):
                            show_row = False
                    except:
                        show_row = False
            self.invoices_table.setRowHidden(row, not show_row)

    def view_invoice_details(self, invoice_id):
        dialog = PurchaseInvoiceDetailsDialog(self.engine, invoice_id)
        dialog.exec_()
        
    def print_invoice(self, invoice_id):
        """طباعة فاتورة المشتريات"""
        try:
            show_advanced_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء طباعة الفاتورة:\n{str(e)}")

class PurchaseInvoiceDetailsDialog(QDialog):
    def __init__(self, engine, invoice_id):
        super().__init__()
        self.engine = engine
        self.invoice_id = invoice_id
        self.setWindowTitle(f"تفاصيل فاتورة المشتريات رقم {invoice_id:06d}")
        self.setMinimumSize(900, 600)
        self.setup_ui()
        self.load_invoice_details()

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        title_label = QLabel(f"🧾 تفاصيل فاتورة المشتريات رقم {self.invoice_id:06d}")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #F39C12; margin-bottom: 20px;")
        layout.addWidget(title_label)
        self.info_labels = {}
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #FFF3CD; border-radius: 8px; padding: 15px;")
        info_layout = QGridLayout()
        info_frame.setLayout(info_layout)
        labels = [
            ("التاريخ:", 'date'),
            ("المورد:", 'supplier'),
            ("الإجمالي:", 'total'),
            ("المدفوع:", 'paid'),
            ("المتبقي:", 'remaining'),
            ("الحالة:", 'status'),
        ]
        for i, (text, key) in enumerate(labels):
            lbl = QLabel(text)
            lbl.setStyleSheet("font-weight: bold; color: #F39C12;")
            info_layout.addWidget(lbl, i // 2, (i % 2) * 2)
            val = QLabel("-")
            val.setStyleSheet("font-size: 15px; color: #2C3E50; background: white; border-radius: 5px; padding: 6px 12px;")
            info_layout.addWidget(val, i // 2, (i % 2) * 2 + 1)
            self.info_labels[key] = val
        layout.addWidget(info_frame)
        products_label = QLabel("📦 المنتجات المشتراة")
        products_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #28A745; margin-top: 20px; margin-bottom: 10px;")
        layout.addWidget(products_label)
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(4)
        self.products_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي"])
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.resizeSection(1, 100)
        header.resizeSection(2, 120)
        header.resizeSection(3, 140)
        self.products_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
                gridline-color: #E9ECEF;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #E9ECEF;
                text-align: center;
            }
            QHeaderView::section {
                background-color: #28A745;
                color: white;
                padding: 15px;
                border: none;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
            }
            QHeaderView::section:hover {
                background-color: #218838;
            }
        """)
        self.products_table.verticalHeader().setDefaultSectionSize(90)
        self.products_table.verticalHeader().setVisible(False)
        self.products_table.setEditTriggers(QTableWidget.NoEditTriggers)
        layout.addWidget(self.products_table)
        btns_layout = QHBoxLayout()
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("background-color: #DC3545; color: white; font-size: 16px; font-weight: bold; padding: 12px 25px; border: none; border-radius: 8px; min-width: 150px;")
        close_btn.clicked.connect(self.close)
        btns_layout.addStretch()
        btns_layout.addWidget(close_btn)
        layout.addLayout(btns_layout)

    def load_invoice_details(self):
        try:
            with Session(self.engine) as session:
                invoice = session.query(Transaction).get(self.invoice_id)
                if not invoice:
                    QMessageBox.warning(self, "خطأ", "الفاتورة غير موجودة")
                    return
                self.info_labels["date"].setText(invoice.date.strftime("%Y-%m-%d %H:%M") if invoice.date else "-")
                supplier_name = "-"
                if invoice.supplier_id:
                    supplier = session.query(Supplier).get(invoice.supplier_id)
                    if supplier:
                        supplier_name = supplier.name
                self.info_labels["supplier"].setText(supplier_name)
                total = invoice.total_amount or 0
                paid = invoice.paid_amount or 0
                remaining = total - paid
                self.info_labels["total"].setText(f"{total:,.0f}")
                self.info_labels["paid"].setText(f"{paid:,.0f}")
                self.info_labels["remaining"].setText(f"{remaining:,.0f}")
                status = "✅ مدفوعة بالكامل" if remaining <= 0 else "⏳ دفع جزئي"
                self.info_labels["status"].setText(status)
                items = session.query(TransactionItem).filter(TransactionItem.transaction_id == self.invoice_id).all()
                self.products_table.setRowCount(len(items))
                for row, item in enumerate(items):
                    product = session.query(Product).get(item.product_id)
                    product_name = product.name if product else f"منتج رقم {item.product_id}"
                    self.products_table.setItem(row, 0, QTableWidgetItem(product_name))
                    self.products_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))
                    self.products_table.setItem(row, 2, QTableWidgetItem(f"{item.price:,.0f}"))
                    item_total = item.quantity * item.price
                    self.products_table.setItem(row, 3, QTableWidgetItem(f"{item_total:,.0f}"))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل تفاصيل الفاتورة:\n{str(e)}") 