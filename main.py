from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from database.users import init_db

# إصلاح مشكلة Qt platform plugins
def fix_qt_plugin_path():
    """إصلاح مسار Qt plugins للتشغيل الصحيح"""
    try:
        import PyQt5
        qt_plugin_path = os.path.join(os.path.dirname(PyQt5.__file__), 'Qt5', 'plugins')
        if os.path.exists(qt_plugin_path):
            os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugin_path
            print(f"🔧 تم تعيين مسار Qt plugins: {qt_plugin_path}")

        # جرب مسارات بديلة
        platforms_path = os.path.join(qt_plugin_path, 'platforms')
        if os.path.exists(platforms_path):
            # تأكد من وجود qwindows.dll
            qwindows_dll = os.path.join(platforms_path, 'qwindows.dll')
            if os.path.exists(qwindows_dll):
                print(f"✅ تم العثور على qwindows.dll في: {qwindows_dll}")
            else:
                print(f"❌ لم يتم العثور على qwindows.dll")

        # جرب تعيين QT_PLUGIN_PATH أيضاً
        os.environ['QT_PLUGIN_PATH'] = qt_plugin_path

    except Exception as e:
        print(f"⚠️ خطأ في إعداد Qt plugins: {e}")

# تطبيق الإصلاح قبل إنشاء QApplication
fix_qt_plugin_path()

# استيراد نظام التراخيص
try:
    from license_ui import check_license_and_show_dialog
    # from license_manager import LicenseManager  # تم التعليق لأنه غير مستخدم
    LICENSE_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: نظام التراخيص غير متوفر - {e}")
    LICENSE_SYSTEM_AVAILABLE = False

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.dirname(__file__), relative_path)



def main():
    app = None
    try:
        # تهيئة قاعدة البيانات أولاً قبل إنشاء التطبيق
        if getattr(sys, 'frozen', False):
            # إذا كان البرنامج مصدر (PyInstaller)
            base_dir = os.path.dirname(sys.executable)
            db_path = os.path.join(base_dir, 'accounting.db')
        else:
            # إذا كان البرنامج يعمل من الكود المصدري
            db_path = resource_path('accounting.db')
        
        print(f"🔍 مسار قاعدة البيانات الفعلي: {db_path}")
        db_exists = os.path.exists(db_path)

        # إنشاء محرك قاعدة البيانات مع إعدادات محسنة
        engine = create_engine(
            f'sqlite:///{db_path}',
            echo=False,  # تقليل الرسائل لتجنب مشاكل الذاكرة
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={'check_same_thread': False}  # إضافة هذا للـ SQLite
        )

        if not db_exists:
            print("📦 قاعدة البيانات غير موجودة، سيتم إنشاؤها الآن...")
            init_db(engine)
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
        else:
            print("✅ قاعدة البيانات موجودة بالفعل.")

        # إنشاء جلسة قاعدة البيانات
        Session = scoped_session(sessionmaker(bind=engine))

        # إنشاء التطبيق بعد تهيئة قاعدة البيانات
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)

        # تعيين أيقونة البرنامج على مستوى التطبيق
        try:
            icon_path = resource_path(os.path.join('assets', 'icons.ico'))
            if os.path.exists(icon_path):
                app_icon = QIcon(icon_path)
                app.setWindowIcon(app_icon)
        except Exception as e:
            print(f"تحذير: لا يمكن تحميل الأيقونة: {e}")

        # تطبيق التصميم العصري الجديد
        try:
            style_path = resource_path(os.path.join('gui', 'modern_style.qss'))
            if not os.path.exists(style_path):
                style_path = resource_path(os.path.join('gui', 'style.qss'))
            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as style_file:
                    app.setStyleSheet(style_file.read())
        except Exception as e:
            print(f"تحذير: لا يمكن تحميل التصميم: {e}")

        # فحص الترخيص أولاً
        if LICENSE_SYSTEM_AVAILABLE:
            print("🔐 فحص الترخيص...")
            if not check_license_and_show_dialog():
                print("❌ لم يتم تفعيل الترخيص - إغلاق البرنامج")
                QMessageBox.critical(
                    None,
                    "خطأ في الترخيص",
                    "لم يتم تفعيل الترخيص.\n\n"
                    "للتجديد، راسل:\n"
                    "📧 <EMAIL>\n\n"
                    "أرسل كود العميل ورقم الجهاز مع إثبات الدفع"
                )
                return 1
            print("✅ تم التحقق من الترخيص بنجاح")
        else:
            print("⚠️ تحذير: نظام التراخيص غير متوفر - تشغيل البرنامج بدون حماية")

        # عرض نافذة تسجيل الدخول
        from gui.login import LoginDialog
        login_dialog = LoginDialog(engine)
        if login_dialog.exec_() != QDialog.Accepted:
            print("❌ تم إلغاء تسجيل الدخول")
            return 1

        # الحصول على المستخدم المسجل
        current_user = login_dialog.user

        # استيراد MainWindow هنا بعد تهيئة قاعدة البيانات
        print("🔄 تحميل النافذة الرئيسية...")
        from gui.main_window import MainWindow
        window = MainWindow(engine=engine, user=current_user)
        window.show()

        # تم حذف نافذة الإعداد الأولي - يمكن استخدام إعدادات الشركة من تبويب النظام

        print("🚀 تم تشغيل البرنامج بنجاح!")
        return app.exec_()

    except Exception as e:
        error_msg = f"حدث خطأ أثناء تشغيل البرنامج: {str(e)}"
        print(f"❌ {error_msg}")
        if app:
            QMessageBox.critical(None, "خطأ", error_msg)
        return 1
    finally:
        if 'Session' in locals():
            try:
                Session.remove()
            except:
                pass


if __name__ == "__main__":
    sys.exit(main())