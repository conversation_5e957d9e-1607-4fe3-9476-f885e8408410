# 🎛️ دليل التحكم في البار الجانبي

## 📋 نظرة عامة

تم إضافة نظام تحكم متقدم في البار الجانبي يتيح للمستخدم:
- **تفعيل/إلغاء تفعيل** البار الجانبي كلياً
- **اختصار لوحة مفاتيح** للتحكم السريع
- **إعدادات محفوظة** تلقائياً

---

## 🎯 المميزات الجديدة

### 1. 🔧 **إعداد التفعيل/الإلغاء**
- البار الجانبي **مطفي افتراضياً**
- يمكن تفعيله من إعدادات النظام
- **لا يظهر بتحريك الماوس** عندما يكون مطفي

### 2. ⌨️ **اختصار لوحة المفاتيح**
- **Ctrl+B**: إظهار/إخفاء البار الجانبي
- يعمل في جميع صفحات النظام
- رسالة تنبيه إذا كان البار مطفي

### 3. 💾 **حفظ الإعدادات**
- الإعدادات محفوظة في `user_settings.json`
- تحميل تلقائي عند بدء البرنامج
- تطبيق فوري للتغييرات

---

## 🛠️ طريقة الاستخدام

### 📱 **من إعدادات النظام:**

1. **افتح إعدادات النظام:**
   ```
   القائمة الرئيسية ← ⚙️ إعدادات النظام
   ```

2. **اذهب لتبويب اللغة والمظهر:**
   ```
   🎨 اللغة والمظهر
   ```

3. **فعل/ألغي تفعيل البار الجانبي:**
   ```
   ☑️ تفعيل البار الجانبي
   ```

4. **احفظ الإعدادات:**
   ```
   💾 حفظ الإعدادات
   ```

### ⌨️ **باستخدام الاختصار:**

```
Ctrl + B = إظهار/إخفاء البار الجانبي
```

---

## 🎨 السلوك المتوقع

### ✅ **عندما يكون البار مفعل:**
- يظهر البار الجانبي في الصفحة الرئيسية
- يتوسع عند تمرير الماوس عليه
- يمكن إخفاؤه مؤقتاً بـ Ctrl+B
- يعود للظهور بـ Ctrl+B مرة أخرى

### ❌ **عندما يكون البار مطفي:**
- البار الجانبي مخفي تماماً
- لا يظهر بتحريك الماوس
- Ctrl+B يعرض رسالة تنبيه
- يجب تفعيله من الإعدادات أولاً

---

## 📁 الملفات المعدلة

### 🔧 **الملفات الرئيسية:**
- `gui/main_window.py` - النافذة الرئيسية
- `gui/settings_dialog.py` - نافذة إعدادات النظام
- `database/settings_models.py` - نموذج إعدادات قاعدة البيانات

### 📄 **ملفات الإعدادات:**
- `accounting.db` - قاعدة البيانات (جدول app_settings)

### 🧪 **ملفات الاختبار:**
- `test_sidebar_settings.py` - اختبار المميزات الجديدة

---

## 🔍 التفاصيل التقنية

### 📊 **بنية الإعدادات:**
```sql
-- جدول app_settings في قاعدة البيانات
INSERT INTO app_settings (setting_key, setting_value, setting_type, category)
VALUES ('sidebar_enabled', 'false', 'boolean', 'appearance');
```

### 🎛️ **الدوال الجديدة:**
- `load_sidebar_setting()` - تحميل إعداد البار
- `save_sidebar_setting()` - حفظ إعداد البار
- `toggle_sidebar_visibility()` - تبديل ظهور البار
- `update_sidebar_visibility()` - تحديث حالة البار

### ⌨️ **الاختصارات:**
- `QShortcut(Ctrl+B)` - اختصار التبديل

---

## 🧪 الاختبار

### 🚀 **تشغيل الاختبار:**
```bash
python test_sidebar_settings.py
```

### ✅ **ما يتم اختباره:**
- تحميل وحفظ الإعدادات
- إنشاء نافذة الإعدادات
- عمل النافذة الرئيسية
- اختصار لوحة المفاتيح

---

## 🎯 الاستخدام العملي

### 👥 **للمستخدمين العاديين:**
- البار مطفي افتراضياً لتوفير مساحة الشاشة
- يمكن تفعيله عند الحاجة
- اختصار سريع للتحكم

### 🏢 **للشركات:**
- توفير مساحة أكبر للعمل
- تخصيص الواجهة حسب الحاجة
- تحسين تجربة المستخدم

### 💻 **للمطورين:**
- نظام إعدادات قابل للتوسع
- حفظ تلقائي للتفضيلات
- واجهة برمجية واضحة

---

## 🔮 التطوير المستقبلي

### 🎨 **مميزات مقترحة:**
- إعدادات إضافية للبار الجانبي
- تخصيص موقع البار (يمين/يسار)
- ثيمات مختلفة للبار
- اختصارات إضافية

### 🛠️ **تحسينات تقنية:**
- تحسين الأداء
- دعم شاشات متعددة
- حفظ حالة التوسع
- رسوم متحركة محسنة

---

**🎉 تم تطوير هذه المميزات لتحسين تجربة المستخدم وإعطاء مرونة أكبر في التحكم بالواجهة!**
