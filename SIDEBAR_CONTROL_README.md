# 🎛️ دليل التحكم في البار الجانبي - جميع الواجهات

## 📋 نظرة عامة

تم إضافة نظام تحكم متقدم في البار الجانبي يعمل على **جميع واجهات النظام** ويتيح للمستخدم:
- **تفعيل/إلغاء تفعيل** البار الجانبي في جميع الصفحات
- **اختصار لوحة مفاتيح** للتحكم السريع في أي واجهة
- **إعدادات محفوظة** تطبق على جميع الواجهات تلقائياً

---

## 🎯 المميزات الجديدة

### 1. 🔧 **إعداد التفعيل/الإلغاء**
- البار الجانبي **مطفي افتراضياً** في جميع الواجهات
- يمكن تفعيله من إعدادات النظام
- **لا يظهر بتحريك الماوس** عندما يكون مطفي
- يطبق على جميع الصفحات فوراً

### 2. ⌨️ **اختصار لوحة المفاتيح**
- **Ctrl+B**: إظهار/إخفاء البار الجانبي
- يعمل في **جميع واجهات النظام**:
  - 🏠 الصفحة الرئيسية
  - 📄 فواتير المبيعات
  - 📦 إدارة المخزون
  - 👥 العملاء والموردين
  - 📊 التقارير
  - 🛒 المشتريات
- رسالة تنبيه إذا كان البار مطفي

### 3. 💾 **حفظ الإعدادات**
- الإعدادات محفوظة في قاعدة البيانات
- تحميل تلقائي عند بدء البرنامج
- تطبيق فوري على جميع الواجهات المفتوحة

---

## 🛠️ طريقة الاستخدام

### 📱 **من إعدادات النظام:**

1. **افتح إعدادات النظام:**
   ```
   القائمة الرئيسية ← ⚙️ إعدادات النظام
   ```

2. **اذهب لتبويب اللغة والمظهر:**
   ```
   🎨 اللغة والمظهر
   ```

3. **فعل/ألغي تفعيل البار الجانبي:**
   ```
   ☑️ تفعيل البار الجانبي
   ```

4. **احفظ الإعدادات:**
   ```
   💾 حفظ الإعدادات
   ```

### ⌨️ **باستخدام الاختصار:**

```
Ctrl + B = إظهار/إخفاء البار الجانبي
```

---

## 🎨 السلوك المتوقع

### ✅ **عندما يكون البار مفعل:**
- يظهر البار الجانبي في **جميع الواجهات**:
  - **الصفحة الرئيسية**: بار جانبي كامل مرئي
  - **الواجهات الأخرى**: بار جانبي مخفي (5 بكسل)
- يتوسع عند تمرير الماوس عليه في أي واجهة
- يمكن إخفاؤه مؤقتاً بـ Ctrl+B من أي مكان
- يعود للظهور بـ Ctrl+B مرة أخرى

### ❌ **عندما يكون البار مطفي:**
- البار الجانبي مخفي تماماً في **جميع الواجهات**
- لا يظهر بتحريك الماوس في أي صفحة
- Ctrl+B يعرض رسالة تنبيه من أي واجهة
- يجب تفعيله من الإعدادات أولاً

---

## 📁 الملفات المعدلة

### 🔧 **الملفات الرئيسية:**
- `gui/main_window.py` - النافذة الرئيسية
- `gui/settings_dialog.py` - نافذة إعدادات النظام
- `database/settings_models.py` - نموذج إعدادات قاعدة البيانات

### 📄 **ملفات الإعدادات:**
- `accounting.db` - قاعدة البيانات (جدول app_settings)

### 🧪 **ملفات الاختبار:**
- `test_sidebar_settings.py` - اختبار المميزات الأساسية
- `test_all_sidebars.py` - اختبار شامل لجميع الواجهات

---

## 🔍 التفاصيل التقنية

### 📊 **بنية الإعدادات:**
```sql
-- جدول app_settings في قاعدة البيانات
INSERT INTO app_settings (setting_key, setting_value, setting_type, category)
VALUES ('sidebar_enabled', 'false', 'boolean', 'appearance');
```

### 🎛️ **الدوال الجديدة:**
- `load_sidebar_setting()` - تحميل إعداد البار
- `save_sidebar_setting()` - حفظ إعداد البار
- `toggle_sidebar_visibility()` - تبديل ظهور البار
- `update_sidebar_visibility()` - تحديث حالة البار

### ⌨️ **الاختصارات:**
- `QShortcut(Ctrl+B)` - اختصار التبديل

---

## 🧪 الاختبار

### 🚀 **تشغيل الاختبارات:**

#### 🔧 **الاختبار الأساسي:**
```bash
python test_sidebar_settings.py
```

#### 🌐 **الاختبار الشامل (جميع الواجهات):**
```bash
python test_all_sidebars.py
```

### ✅ **ما يتم اختباره:**

#### 📋 **الاختبار الأساسي:**
- تحميل وحفظ الإعدادات
- إنشاء نافذة الإعدادات
- عمل النافذة الرئيسية
- اختصار لوحة المفاتيح

#### 🌐 **الاختبار الشامل:**
- البار الجانبي الرئيسي
- البارات الجانبية المخفية في جميع التبويبات
- اختبار تبويب المبيعات
- اختبار تبويبات: المخزون، العملاء، التقارير، المشتريات
- نظام التحكم الموحد
- الاختصارات في جميع الواجهات

---

## 🎯 الاستخدام العملي

### 👥 **للمستخدمين العاديين:**
- البار مطفي افتراضياً لتوفير مساحة الشاشة
- يمكن تفعيله عند الحاجة
- اختصار سريع للتحكم

### 🏢 **للشركات:**
- توفير مساحة أكبر للعمل
- تخصيص الواجهة حسب الحاجة
- تحسين تجربة المستخدم

### 💻 **للمطورين:**
- نظام إعدادات قابل للتوسع
- حفظ تلقائي للتفضيلات
- واجهة برمجية واضحة

---

## 🔮 التطوير المستقبلي

### 🎨 **مميزات مقترحة:**
- إعدادات إضافية للبار الجانبي
- تخصيص موقع البار (يمين/يسار)
- ثيمات مختلفة للبار
- اختصارات إضافية

### 🛠️ **تحسينات تقنية:**
- تحسين الأداء
- دعم شاشات متعددة
- حفظ حالة التوسع
- رسوم متحركة محسنة

---

**🎉 تم تطوير هذه المميزات لتحسين تجربة المستخدم وإعطاء مرونة أكبر في التحكم بالواجهة!**
