/* 
===========================================
    نظام المحاسبة - التصميم العصري المحسن
    متوافق مع PyQt5
===========================================
*/

/* الإعدادات العامة */
* {
    font-family: "Segoe UI", "Cairo", "Tajawal", "Arial", sans-serif;
    font-size: 14px;
    outline: none;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
    color: #2d3748;
}

/* ========================================
   شريط القوائم العصري
======================================== */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #1a202c, stop:1 #2d3748);
    color: white;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #4a5568;
}

QMenuBar::item {
    background-color: transparent;
    padding: 12px 20px;
    margin: 0 5px;
    border-radius: 10px;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
}

QMenuBar::item:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #5a67d8, stop:1 #553c9a);
}

/* ========================================
   شريط الأدوات العصري
======================================== */
QToolBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #1a202c, stop:1 #2d3748);
    border: none;
    spacing: 8px;
    padding: 10px 15px;
    min-height: 60px;
    max-height: 60px;
    border-bottom: 2px solid #4a5568;
}

QToolButton {
    background-color: transparent;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 10px 18px;
    font-size: 14px;
    font-weight: 600;
    min-width: 120px;
    min-height: 40px;
    margin: 2px;
}

QToolButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
}

QToolButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #5a67d8, stop:1 #553c9a);
}

QToolBar::separator {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 transparent, stop:0.5 #718096, stop:1 transparent);
    width: 1px;
    margin: 10px 8px;
}

/* ========================================
   القوائم المنسدلة العصرية
======================================== */
QMenu {
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 10px;
    font-size: 14px;
}

QMenu::item {
    padding: 12px 20px;
    border-radius: 10px;
    margin: 2px;
    color: #2d3748;
    font-weight: 500;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
}

QMenu::separator {
    height: 1px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 transparent, stop:0.5 #e2e8f0, stop:1 transparent);
    margin: 8px 15px;
}

/* ========================================
   الأزرار العصرية
======================================== */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    min-height: 45px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #5a67d8, stop:1 #553c9a);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #4c51bf, stop:1 #44337a);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #cbd5e0, stop:1 #a0aec0);
    color: #718096;
}

/* أزرار ملونة خاصة */
QPushButton[class="success"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #48bb78, stop:1 #38a169);
}

QPushButton[class="success"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #38a169, stop:1 #2f855a);
}

QPushButton[class="danger"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #f56565, stop:1 #e53e3e);
}

QPushButton[class="danger"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #e53e3e, stop:1 #c53030);
}

QPushButton[class="warning"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #ed8936, stop:1 #dd6b20);
}

QPushButton[class="warning"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #dd6b20, stop:1 #c05621);
}

/* ========================================
   حقول الإدخال العصرية
======================================== */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 14px 18px;
    font-size: 14px;
    color: #2d3748;
    selection-background-color: #667eea;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #667eea;
    background-color: #ffffff;
}

QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
    border-color: #cbd5e0;
}

/* ========================================
   القوائم المنسدلة والحقول
======================================== */
QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 14px 18px;
    font-size: 14px;
    min-height: 20px;
    color: #2d3748;
}

QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border-color: #667eea;
}

QComboBox:hover, QSpinBox:hover, QDoubleSpinBox:hover, QDateEdit:hover {
    border-color: #cbd5e0;
}

QComboBox::drop-down {
    border: none;
    width: 40px;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 transparent, stop:1 #f7fafc);
}

QComboBox::down-arrow {
    width: 14px;
    height: 8px;
    background: #667eea;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    selection-background-color: #667eea;
    outline: none;
    padding: 5px;
}

/* ========================================
   التسميات العصرية
======================================== */
QLabel {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    padding: 4px;
}

/* ========================================
   مربعات الاختيار والراديو
======================================== */
QCheckBox, QRadioButton {
    font-size: 14px;
    spacing: 10px;
    color: #2d3748;
    font-weight: 500;
}

QCheckBox::indicator, QRadioButton::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e0;
    background-color: #ffffff;
}

QCheckBox::indicator {
    border-radius: 6px;
}

QRadioButton::indicator {
    border-radius: 10px;
}

QCheckBox::indicator:checked, QRadioButton::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                stop:0 #667eea, stop:1 #764ba2);
    border-color: #667eea;
}

QCheckBox::indicator:hover, QRadioButton::indicator:hover {
    border-color: #667eea;
}

/* ========================================
   النوافذ والحوارات
======================================== */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f7fafc, stop:1 #edf2f7);
    font-size: 14px;
    border-radius: 20px;
}

QMessageBox {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f7fafc, stop:1 #edf2f7);
    font-size: 14px;
}

/* ========================================
   التلميحات
======================================== */
QToolTip {
    background-color: #1a202c;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 12px;
    font-weight: 500;
}

/* ========================================
   الجداول العصرية
======================================== */
QTableWidget {
    background-color: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 20px;
    gridline-color: #f1f5f9;
    font-size: 14px;
    show-decoration-selected: 1;
    outline: 0;
}

QTableWidget::item {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f5f9;
    color: #2d3748;
    font-weight: 500;
    min-height: 45px;
    text-align: center;
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(102, 126, 234, 0.1), stop:1 rgba(118, 75, 162, 0.1));
    color: #2d3748;
}

QTableWidget::item:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
    padding: 18px 12px;
    border: none;
    font-weight: 600;
    font-size: 14px;
    min-height: 50px;
    text-align: center;
}

QHeaderView::section:first {
    border-top-right-radius: 20px;
}

QHeaderView::section:last {
    border-top-left-radius: 20px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
}

/* أزرار الجداول */
QTableWidget QPushButton {
    margin: 4px;
    padding: 8px 16px;
    min-width: 80px;
    min-height: 35px;
    font-size: 12px;
    border-radius: 8px;
    font-weight: 600;
}

/* ========================================
   أشرطة التمرير العصرية
======================================== */
QScrollBar:vertical {
    background-color: #f1f5f9;
    width: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    border-radius: 7px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f1f5f9;
    height: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
    border-radius: 7px;
    min-width: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5a67d8, stop:1 #553c9a);
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* ========================================
   التبويبات العصرية
======================================== */
QTabWidget::pane {
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #ecf0f1;
    padding: 15px 30px;
    margin-left: 3px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    min-width: 120px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
}

QTabBar::tab:hover {
    background-color: #cbd5e0;
}

/* ========================================
   المجموعات والإطارات
======================================== */
QGroupBox {
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    margin-top: 1.5em;
    padding-top: 25px;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 15px;
    font-size: 16px;
    font-weight: 700;
    color: #667eea;
}

/* ========================================
   شريط الحالة
======================================== */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1a202c, stop:1 #2d3748);
    color: white;
    font-size: 12px;
    min-height: 30px;
    border-top: 2px solid #4a5568;
}

/* ========================================
   أنماط خاصة للبطاقات
======================================== */
QFrame[class="stat_card"] {
    border-radius: 20px;
    padding: 30px;
    min-height: 160px;
}

QFrame[class="stat_card"] QLabel {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

QLabel[class="card_title"] {
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

QLabel[class="card_value"] {
    font-size: 32px;
    font-weight: 700;
    color: white;
}

/* ========================================
   أزرار خاصة للجداول
======================================== */
QPushButton[toolTip*="حذف"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f56565, stop:1 #e53e3e);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    min-width: 70px;
    min-height: 35px;
    margin: 2px;
    font-weight: 600;
}

QPushButton[toolTip*="حذف"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e53e3e, stop:1 #c53030);
}

QPushButton[toolTip*="تعديل"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ed8936, stop:1 #dd6b20);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    min-width: 70px;
    min-height: 35px;
    margin: 2px;
    font-weight: 600;
}

QPushButton[toolTip*="تعديل"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dd6b20, stop:1 #c05621);
}

/* ========================================
   فئات الألوان للنصوص
======================================== */
.success-text {
    color: #48bb78;
    font-weight: 600;
}

.error-text {
    color: #f56565;
    font-weight: 600;
}

.warning-text {
    color: #ed8936;
    font-weight: 600;
}

.info-text {
    color: #667eea;
    font-weight: 600;
}

/* ========================================
   أنماط العناصر المنبثقة المحسنة
======================================== */
QCompleter QAbstractItemView {
    background-color: white;
    border: 2px solid #3498DB;
    border-radius: 8px;
    selection-background-color: #3498DB;
    selection-color: white;
    outline: none;
    font-size: 14px;
    padding: 4px;
    max-height: 300px;
    min-width: 300px;
    max-width: 500px;
}

QCompleter QAbstractItemView::item {
    padding: 8px 12px;
    border-bottom: 1px solid #ECF0F1;
    min-height: 25px;
    max-height: 35px;
}

QCompleter QAbstractItemView::item:hover {
    background-color: #EBF3FD;
    border-radius: 4px;
}

QCompleter QAbstractItemView::item:selected {
    background-color: #3498DB;
    color: white;
    border-radius: 4px;
}

/* تحسين QListView العام */
QListView {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    selection-background-color: #3498DB;
    selection-color: white;
    outline: none;
    font-size: 14px;
}

QListView::item {
    padding: 6px 10px;
    border-bottom: 1px solid #f0f0f0;
    min-height: 20px;
}

QListView::item:hover {
    background-color: #f8f9fa;
}

QListView::item:selected {
    background-color: #3498DB;
    color: white;
}

/* تحسين العناصر المنبثقة للحوارات */
QDialog QListWidget {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    max-height: 250px;
    min-width: 250px;
}

QDialog QListWidget::item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    min-height: 25px;
}

QDialog QListWidget::item:hover {
    background-color: #f8f9fa;
}

QDialog QListWidget::item:selected {
    background-color: #3498DB;
    color: white;
}


/* ========================================
   DPI Responsive Fixes - إصلاحات DPI المتجاوبة
======================================== */

/* إصلاح العناصر المنبثقة */
QCompleter QAbstractItemView {
    max-height: 250px;
    min-width: 200px;
    max-width: 500px;
    font-size: 12px;
}

QCompleter QAbstractItemView::item {
    min-height: 25px;
    max-height: 35px;
    padding: 6px 10px;
}

/* إصلاح الحوارات */
QDialog {
    min-width: 300px;
    min-height: 200px;
}

QDialog QListWidget {
    max-height: 200px;
    min-width: 250px;
}

/* إصلاح الجداول */
QTableWidget {
    font-size: 12px;
}

QHeaderView::section {
    min-height: 25px;
    padding: 5px;
    font-size: 12px;
}

/* إصلاح الأزرار */
QPushButton {
    min-height: 25px;
    padding: 5px 10px;
    font-size: 12px;
}

/* إصلاح حقول الإدخال */
QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
    min-height: 20px;
    padding: 3px;
    font-size: 12px;
}

/* إصلاح التسميات */
QLabel {
    font-size: 12px;
}

/* إصلاحات للشاشات عالية الدقة */
@media (min-resolution: 144dpi) {
    QCompleter QAbstractItemView {
        font-size: 14px;
        max-height: 300px;
    }
    
    QCompleter QAbstractItemView::item {
        min-height: 30px;
        padding: 8px 12px;
    }
    
    QPushButton {
        min-height: 30px;
        font-size: 14px;
    }
    
    QLineEdit, QComboBox {
        min-height: 25px;
        font-size: 14px;
    }
}
