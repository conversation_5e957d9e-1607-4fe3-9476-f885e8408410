#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إعدادات التنبيهات المتقدمة
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QCheckBox, QSpinBox, QPushButton, QGroupBox,
                            QGridLayout, QComboBox, QMessageBox, QTabWidget,
                            QWidget, QSlider, QTimeEdit)
from PyQt5.QtCore import Qt, QTime
from PyQt5.QtGui import QFont
import json
import os

class NotificationsSettingsDialog(QDialog):
    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.settings_file = "notifications_settings.json"
        self.load_settings()
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("إعدادات التنبيهات المتقدمة")
        self.setMinimumSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title = QLabel("⚙️ إعدادات التنبيهات المتقدمة")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # التبويبات
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # تبويب الإعدادات العامة
        general_tab = self.create_general_tab()
        tabs.addTab(general_tab, "🔔 الإعدادات العامة")
        
        # تبويب إعدادات المخزون
        inventory_tab = self.create_inventory_tab()
        tabs.addTab(inventory_tab, "📦 إعدادات المخزون")
        
        # تبويب إعدادات المدفوعات
        payments_tab = self.create_payments_tab()
        tabs.addTab(payments_tab, "💰 إعدادات المدفوعات")
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ الإعدادات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
    def create_general_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة التنبيهات المفعلة
        enabled_group = QGroupBox("التنبيهات المفعلة")
        enabled_layout = QVBoxLayout(enabled_group)
        
        self.inventory_enabled = QCheckBox("تنبيهات المخزون المنخفض")
        self.inventory_enabled.setChecked(self.settings.get('inventory_enabled', True))
        
        self.payments_enabled = QCheckBox("تنبيهات المدفوعات المستحقة")
        self.payments_enabled.setChecked(self.settings.get('payments_enabled', True))
        
        self.expiry_enabled = QCheckBox("تنبيهات صلاحية المنتجات")
        self.expiry_enabled.setChecked(self.settings.get('expiry_enabled', True))
        
        enabled_layout.addWidget(self.inventory_enabled)
        enabled_layout.addWidget(self.payments_enabled)
        enabled_layout.addWidget(self.expiry_enabled)
        layout.addWidget(enabled_group)
        
        # مجموعة التوقيت
        timing_group = QGroupBox("إعدادات التوقيت")
        timing_layout = QGridLayout(timing_group)
        
        timing_layout.addWidget(QLabel("فترة الفحص (بالدقائق):"), 0, 0)
        self.check_interval = QSpinBox()
        self.check_interval.setRange(1, 1440)  # من دقيقة إلى يوم كامل
        self.check_interval.setValue(self.settings.get('check_interval', 5))
        timing_layout.addWidget(self.check_interval, 0, 1)
        
        timing_layout.addWidget(QLabel("وقت بداية العمل:"), 1, 0)
        self.work_start = QTimeEdit()
        self.work_start.setTime(QTime.fromString(self.settings.get('work_start', '08:00'), 'hh:mm'))
        timing_layout.addWidget(self.work_start, 1, 1)
        
        timing_layout.addWidget(QLabel("وقت نهاية العمل:"), 2, 0)
        self.work_end = QTimeEdit()
        self.work_end.setTime(QTime.fromString(self.settings.get('work_end', '18:00'), 'hh:mm'))
        timing_layout.addWidget(self.work_end, 2, 1)
        
        layout.addWidget(timing_group)
        
        return widget
        
    def create_inventory_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # إعدادات المخزون
        inventory_group = QGroupBox("إعدادات تنبيهات المخزون")
        inventory_layout = QGridLayout(inventory_group)
        
        inventory_layout.addWidget(QLabel("الحد الأدنى العام للمخزون:"), 0, 0)
        self.min_stock_general = QSpinBox()
        self.min_stock_general.setRange(0, 1000)
        self.min_stock_general.setValue(self.settings.get('min_stock_general', 10))
        inventory_layout.addWidget(self.min_stock_general, 0, 1)
        
        inventory_layout.addWidget(QLabel("تنبيه عند الوصول لـ:"), 1, 0)
        self.stock_warning_level = QComboBox()
        self.stock_warning_level.addItems(["الحد الأدنى", "ضعف الحد الأدنى", "ثلاثة أضعاف الحد الأدنى"])
        self.stock_warning_level.setCurrentText(self.settings.get('stock_warning_level', 'الحد الأدنى'))
        inventory_layout.addWidget(self.stock_warning_level, 1, 1)
        
        layout.addWidget(inventory_group)
        
        return widget
        
    def create_payments_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # إعدادات المدفوعات
        payments_group = QGroupBox("إعدادات تنبيهات المدفوعات")
        payments_layout = QGridLayout(payments_group)
        
        payments_layout.addWidget(QLabel("تنبيه بعد (أيام):"), 0, 0)
        self.payment_due_days = QSpinBox()
        self.payment_due_days.setRange(1, 365)
        self.payment_due_days.setValue(self.settings.get('payment_due_days', 30))
        payments_layout.addWidget(self.payment_due_days, 0, 1)
        
        payments_layout.addWidget(QLabel("الحد الأدنى للمبلغ:"), 1, 0)
        self.min_payment_amount = QSpinBox()
        self.min_payment_amount.setRange(0, 1000000)
        self.min_payment_amount.setValue(self.settings.get('min_payment_amount', 100))
        payments_layout.addWidget(self.min_payment_amount, 1, 1)
        
        layout.addWidget(payments_group)
        
        return widget
        
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.settings = json.load(f)
            else:
                self.settings = self.get_default_settings()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات التنبيهات: {e}")
            self.settings = self.get_default_settings()
            
    def get_default_settings(self):
        """الإعدادات الافتراضية"""
        return {
            'inventory_enabled': True,
            'payments_enabled': True,
            'expiry_enabled': True,
            'check_interval': 5,
            'work_start': '08:00',
            'work_end': '18:00',
            'min_stock_general': 10,
            'stock_warning_level': 'الحد الأدنى',
            'payment_due_days': 30,
            'min_payment_amount': 100
        }
        
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # جمع الإعدادات من الواجهة
            self.settings = {
                'inventory_enabled': self.inventory_enabled.isChecked(),
                'payments_enabled': self.payments_enabled.isChecked(),
                'expiry_enabled': self.expiry_enabled.isChecked(),
                'check_interval': self.check_interval.value(),
                'work_start': self.work_start.time().toString('hh:mm'),
                'work_end': self.work_end.time().toString('hh:mm'),
                'min_stock_general': self.min_stock_general.value(),
                'stock_warning_level': self.stock_warning_level.currentText(),
                'payment_due_days': self.payment_due_days.value(),
                'min_payment_amount': self.min_payment_amount.value()
            }
            
            # حفظ في الملف
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
                
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات التنبيهات بنجاح!")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")
