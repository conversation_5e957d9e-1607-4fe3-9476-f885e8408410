%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(TM) 5.5
%%For: (Bud Northern) (<PERSON>)
%%Title: (TCL/TK LOGO.ILLUS)
%%CreationDate: (8/1/96) (4:58 PM)
%%BoundingBox: 251 331 371 512
%%HiResBoundingBox: 251.3386 331.5616 370.5213 511.775
%%DocumentProcessColors: <PERSON>an <PERSON>
%%DocumentSuppliedResources: procset Adobe_level2_AI5 1.0 0
%%+ procset Adobe_IllustratorA_AI5 1.0 0
%AI5_FileFormat 1.2
%AI3_ColorUsage: Color
%%DocumentCustomColors: (TCL RED)
%%CMYKCustomColor: 0 0.45 1 0 (Orange)
%%+ 0 0.25 1 0 (Orange Yellow)
%%+ 0 0.79 0.91 0 (TCL RED)
%AI3_TemplateBox: 306 396 306 396
%AI3_TileBox: 12 12 600 780
%AI3_DocumentPreview: Macintosh_ColorPic
%AI5_ArtSize: 612 792
%AI5_RulerUnits: 0
%AI5_ArtFlags: 1 0 0 1 0 0 1 1 0
%AI5_TargetResolution: 800
%AI5_NumLayers: 1
%AI5_OpenToView: 90 576 2 938 673 18 1 1 2 40
%AI5_OpenViewLayers: 7
%%EndComments
%%BeginProlog
%%BeginResource: procset Adobe_level2_AI5 1.0 0
%%Title: (Adobe Illustrator (R) Version 5.0 Level 2 Emulation)
%%Version: 1.0 
%%CreationDate: (04/10/93) ()
%%Copyright: ((C) 1987-1993 Adobe Systems Incorporated All Rights Reserved)
userdict /Adobe_level2_AI5 21 dict dup begin
	put
	/packedarray where not
	{
		userdict begin
		/packedarray
		{
			array astore readonly
		} bind def
		/setpacking /pop load def
		/currentpacking false def
	 end
		0
	} if
	pop
	userdict /defaultpacking currentpacking put true setpacking
	/initialize
	{
		Adobe_level2_AI5 begin
	} bind def
	/terminate
	{
		currentdict Adobe_level2_AI5 eq
		{
		 end
		} if
	} bind def
	mark
	/setcustomcolor where not
	{
		/findcmykcustomcolor
		{
			5 packedarray
		} bind def
		/setcustomcolor
		{
			exch aload pop pop
			4
			{
				4 index mul 4 1 roll
			} repeat
			5 -1 roll pop
			setcmykcolor
		}
		def
	} if
	
	/gt38? mark {version cvx exec} stopped {cleartomark true} {38 gt exch pop} ifelse def
	userdict /deviceDPI 72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt put
	userdict /level2?
	systemdict /languagelevel known dup
	{
		pop systemdict /languagelevel get 2 ge
	} if
	put
	level2? not
	{
		/setcmykcolor where not
		{
			/setcmykcolor
			{
				exch .11 mul add exch .59 mul add exch .3 mul add
				1 exch sub setgray
			} def
		} if
		/currentcmykcolor where not
		{
			/currentcmykcolor
			{
				0 0 0 1 currentgray sub
			} def
		} if
		/setoverprint where not
		{
			/setoverprint /pop load def
		} if
		/selectfont where not
		{
			/selectfont
			{
				exch findfont exch
				dup type /arraytype eq
				{
					makefont
				}
				{
					scalefont
				} ifelse
				setfont
			} bind def
		} if
		/cshow where not
		{
			/cshow
			{
				[
				0 0 5 -1 roll aload pop
				] cvx bind forall
			} bind def
		} if
	} if
	cleartomark
	/anyColor?
	{
		add add add 0 ne
	} bind def
	/testColor
	{
		gsave
		setcmykcolor currentcmykcolor
		grestore
	} bind def
	/testCMYKColorThrough
	{
		testColor anyColor?
	} bind def
	userdict /composite?
	level2?
	{
		gsave 1 1 1 1 setcmykcolor currentcmykcolor grestore
		add add add 4 eq
	}
	{
		1 0 0 0 testCMYKColorThrough
		0 1 0 0 testCMYKColorThrough
		0 0 1 0 testCMYKColorThrough
		0 0 0 1 testCMYKColorThrough
		and and and
	} ifelse
	put
	composite? not
	{
		userdict begin
		gsave
		/cyan? 1 0 0 0 testCMYKColorThrough def
		/magenta? 0 1 0 0 testCMYKColorThrough def
		/yellow? 0 0 1 0 testCMYKColorThrough def
		/black? 0 0 0 1 testCMYKColorThrough def
		grestore
		/isCMYKSep? cyan? magenta? yellow? black? or or or def
		/customColor? isCMYKSep? not def
	 end
	} if
 end defaultpacking setpacking
%%EndResource
%%BeginResource: procset Adobe_IllustratorA_AI5 1.1 0
%%Title: (Adobe Illustrator (R) Version 5.0 Abbreviated Prolog)
%%Version: 1.1 
%%CreationDate: (3/7/1994) ()
%%Copyright: ((C) 1987-1994 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_IllustratorA_AI5_vars 70 dict dup begin
put
/_lp /none def
/_pf
{
} def
/_ps
{
} def
/_psf
{
} def
/_pss
{
} def
/_pjsf
{
} def
/_pjss
{
} def
/_pola 0 def
/_doClip 0 def
/cf currentflat def
/_tm matrix def
/_renderStart
[
/e0 /r0 /a0 /o0 /e1 /r1 /a1 /i0
] def
/_renderEnd
[
null null null null /i1 /i1 /i1 /i1
] def
/_render -1 def
/_rise 0 def
/_ax 0 def
/_ay 0 def
/_cx 0 def
/_cy 0 def
/_leading
[
0 0
] def
/_ctm matrix def
/_mtx matrix def
/_sp 16#020 def
/_hyphen (-) def
/_fScl 0 def
/_cnt 0 def
/_hs 1 def
/_nativeEncoding 0 def
/_useNativeEncoding 0 def
/_tempEncode 0 def
/_pntr 0 def
/_tDict 2 dict def
/_wv 0 def
/Tx
{
} def
/Tj
{
} def
/CRender
{
} def
/_AI3_savepage
{
} def
/_gf null def
/_cf 4 array def
/_if null def
/_of false def
/_fc
{
} def
/_gs null def
/_cs 4 array def
/_is null def
/_os false def
/_sc
{
} def
/discardSave null def
/buffer 256 string def
/beginString null def
/endString null def
/endStringLength null def
/layerCnt 1 def
/layerCount 1 def
/perCent (%) 0 get def
/perCentSeen? false def
/newBuff null def
/newBuffButFirst null def
/newBuffLast null def
/clipForward? false def
end
userdict /Adobe_IllustratorA_AI5 74 dict dup begin
put
/initialize
{
	Adobe_IllustratorA_AI5 dup begin
	Adobe_IllustratorA_AI5_vars begin
	discardDict
	{
		bind pop pop
	} forall
	dup /nc get begin
	{
		dup xcheck 1 index type /operatortype ne and
		{
			bind
		} if
		pop pop
	} forall
 end
	newpath
} def
/terminate
{
 end
 end
} def
/_
null def
/ddef
{
	Adobe_IllustratorA_AI5_vars 3 1 roll put
} def
/xput
{
	dup load dup length exch maxlength eq
	{
		dup dup load dup
		length 2 mul dict copy def
	} if
	load begin
	def
 end
} def
/npop
{
	{
		pop
	} repeat
} def
/sw
{
	dup length exch stringwidth
	exch 5 -1 roll 3 index mul add
	4 1 roll 3 1 roll mul add
} def
/swj
{
	dup 4 1 roll
	dup length exch stringwidth
	exch 5 -1 roll 3 index mul add
	4 1 roll 3 1 roll mul add
	6 2 roll /_cnt 0 ddef
	{
		1 index eq
		{
			/_cnt _cnt 1 add ddef
		} if
	} forall
	pop
	exch _cnt mul exch _cnt mul 2 index add 4 1 roll 2 index add 4 1 roll pop pop
} def
/ss
{
	4 1 roll
	{
		2 npop
		(0) exch 2 copy 0 exch put pop
		gsave
		false charpath currentpoint
		4 index setmatrix
		stroke
		grestore
		moveto
		2 copy rmoveto
	} exch cshow
	3 npop
} def
/jss
{
	4 1 roll
	{
		2 npop
		(0) exch 2 copy 0 exch put
		gsave
		_sp eq
		{
			exch 6 index 6 index 6 index 5 -1 roll widthshow
			currentpoint
		}
		{
			false charpath currentpoint
			4 index setmatrix stroke
		} ifelse
		grestore
		moveto
		2 copy rmoveto
	} exch cshow
	6 npop
} def
/sp
{
	{
		2 npop (0) exch
		2 copy 0 exch put pop
		false charpath
		2 copy rmoveto
	} exch cshow
	2 npop
} def
/jsp
{
	{
		2 npop
		(0) exch 2 copy 0 exch put
		_sp eq
		{
			exch 5 index 5 index 5 index 5 -1 roll widthshow
		}
		{
			false charpath
		} ifelse
		2 copy rmoveto
	} exch cshow
	5 npop
} def
/pl
{
	transform
	0.25 sub round 0.25 add exch
	0.25 sub round 0.25 add exch
	itransform
} def
/setstrokeadjust where
{
	pop true setstrokeadjust
	/c
	{
		curveto
	} def
	/C
	/c load def
	/v
	{
		currentpoint 6 2 roll curveto
	} def
	/V
	/v load def
	/y
	{
		2 copy curveto
	} def
	/Y
	/y load def
	/l
	{
		lineto
	} def
	/L
	/l load def
	/m
	{
		moveto
	} def
}
{
	/c
	{
		pl curveto
	} def
	/C
	/c load def
	/v
	{
		currentpoint 6 2 roll pl curveto
	} def
	/V
	/v load def
	/y
	{
		pl 2 copy curveto
	} def
	/Y
	/y load def
	/l
	{
		pl lineto
	} def
	/L
	/l load def
	/m
	{
		pl moveto
	} def
} ifelse
/d
{
	setdash
} def
/cf
{
} def
/i
{
	dup 0 eq
	{
		pop cf
	} if
	setflat
} def
/j
{
	setlinejoin
} def
/J
{
	setlinecap
} def
/M
{
	setmiterlimit
} def
/w
{
	setlinewidth
} def
/H
{
} def
/h
{
	closepath
} def
/N
{
	_pola 0 eq
	{
		_doClip 1 eq
		{
			clip /_doClip 0 ddef
		} if
		newpath
	}
	{
		/CRender
		{
			N
		} ddef
	} ifelse
} def
/n
{
	N
} def
/F
{
	_pola 0 eq
	{
		_doClip 1 eq
		{
			gsave _pf grestore clip newpath /_lp /none ddef _fc
			/_doClip 0 ddef
		}
		{
			_pf
		} ifelse
	}
	{
		/CRender
		{
			F
		} ddef
	} ifelse
} def
/f
{
	closepath
	F
} def
/S
{
	_pola 0 eq
	{
		_doClip 1 eq
		{
			gsave _ps grestore clip newpath /_lp /none ddef _sc
			/_doClip 0 ddef
		}
		{
			_ps
		} ifelse
	}
	{
		/CRender
		{
			S
		} ddef
	} ifelse
} def
/s
{
	closepath
	S
} def
/B
{
	_pola 0 eq
	{
		_doClip 1 eq
		gsave F grestore
		{
			gsave S grestore clip newpath /_lp /none ddef _sc
			/_doClip 0 ddef
		}
		{
			S
		} ifelse
	}
	{
		/CRender
		{
			B
		} ddef
	} ifelse
} def
/b
{
	closepath
	B
} def
/W
{
	/_doClip 1 ddef
} def
/*
{
	count 0 ne
	{
		dup type /stringtype eq
		{
			pop
		} if
	} if
	newpath
} def
/u
{
} def
/U
{
} def
/q
{
	_pola 0 eq
	{
		gsave
	} if
} def
/Q
{
	_pola 0 eq
	{
		grestore
	} if
} def
/*u
{
	_pola 1 add /_pola exch ddef
} def
/*U
{
	_pola 1 sub /_pola exch ddef
	_pola 0 eq
	{
		CRender
	} if
} def
/D
{
	pop
} def
/*w
{
} def
/*W
{
} def
/`
{
	/_i save ddef
	clipForward?
	{
		nulldevice
	} if
	6 1 roll 4 npop
	concat pop
	userdict begin
	/showpage
	{
	} def
	0 setgray
	0 setlinecap
	1 setlinewidth
	0 setlinejoin
	10 setmiterlimit
	[] 0 setdash
	/setstrokeadjust where {pop false setstrokeadjust} if
	newpath
	0 setgray
	false setoverprint
} def
/~
{
 end
	_i restore
} def
/O
{
	0 ne
	/_of exch ddef
	/_lp /none ddef
} def
/R
{
	0 ne
	/_os exch ddef
	/_lp /none ddef
} def
/g
{
	/_gf exch ddef
	/_fc
	{
		_lp /fill ne
		{
			_of setoverprint
			_gf setgray
			/_lp /fill ddef
		} if
	} ddef
	/_pf
	{
		_fc
		fill
	} ddef
	/_psf
	{
		_fc
		ashow
	} ddef
	/_pjsf
	{
		_fc
		awidthshow
	} ddef
	/_lp /none ddef
} def
/G
{
	/_gs exch ddef
	/_sc
	{
		_lp /stroke ne
		{
			_os setoverprint
			_gs setgray
			/_lp /stroke ddef
		} if
	} ddef
	/_ps
	{
		_sc
		stroke
	} ddef
	/_pss
	{
		_sc
		ss
	} ddef
	/_pjss
	{
		_sc
		jss
	} ddef
	/_lp /none ddef
} def
/k
{
	_cf astore pop
	/_fc
	{
		_lp /fill ne
		{
			_of setoverprint
			_cf aload pop setcmykcolor
			/_lp /fill ddef
		} if
	} ddef
	/_pf
	{
		_fc
		fill
	} ddef
	/_psf
	{
		_fc
		ashow
	} ddef
	/_pjsf
	{
		_fc
		awidthshow
	} ddef
	/_lp /none ddef
} def
/K
{
	_cs astore pop
	/_sc
	{
		_lp /stroke ne
		{
			_os setoverprint
			_cs aload pop setcmykcolor
			/_lp /stroke ddef
		} if
	} ddef
	/_ps
	{
		_sc
		stroke
	} ddef
	/_pss
	{
		_sc
		ss
	} ddef
	/_pjss
	{
		_sc
		jss
	} ddef
	/_lp /none ddef
} def
/x
{
	/_gf exch ddef
	findcmykcustomcolor
	/_if exch ddef
	/_fc
	{
		_lp /fill ne
		{
			_of setoverprint
			_if _gf 1 exch sub setcustomcolor
			/_lp /fill ddef
		} if
	} ddef
	/_pf
	{
		_fc
		fill
	} ddef
	/_psf
	{
		_fc
		ashow
	} ddef
	/_pjsf
	{
		_fc
		awidthshow
	} ddef
	/_lp /none ddef
} def
/X
{
	/_gs exch ddef
	findcmykcustomcolor
	/_is exch ddef
	/_sc
	{
		_lp /stroke ne
		{
			_os setoverprint
			_is _gs 1 exch sub setcustomcolor
			/_lp /stroke ddef
		} if
	} ddef
	/_ps
	{
		_sc
		stroke
	} ddef
	/_pss
	{
		_sc
		ss
	} ddef
	/_pjss
	{
		_sc
		jss
	} ddef
	/_lp /none ddef
} def
/A
{
	pop
} def
/annotatepage
{
userdict /annotatepage 2 copy known {get exec} {pop pop} ifelse
} def
/discard
{
	save /discardSave exch store
	discardDict begin
	/endString exch store
	gt38?
	{
		2 add
	} if
	load
	stopped
	pop
 end
	discardSave restore
} bind def
userdict /discardDict 7 dict dup begin
put
/pre38Initialize
{
	/endStringLength endString length store
	/newBuff buffer 0 endStringLength getinterval store
	/newBuffButFirst newBuff 1 endStringLength 1 sub getinterval store
	/newBuffLast newBuff endStringLength 1 sub 1 getinterval store
} def
/shiftBuffer
{
	newBuff 0 newBuffButFirst putinterval
	newBuffLast 0
	currentfile read not
	{
	stop
	} if
	put
} def
0
{
	pre38Initialize
	mark
	currentfile newBuff readstring exch pop
	{
		{
			newBuff endString eq
			{
				cleartomark stop
			} if
			shiftBuffer
		} loop
	}
	{
	stop
	} ifelse
} def
1
{
	pre38Initialize
	/beginString exch store
	mark
	currentfile newBuff readstring exch pop
	{
		{
			newBuff beginString eq
			{
				/layerCount dup load 1 add store
			}
			{
				newBuff endString eq
				{
					/layerCount dup load 1 sub store
					layerCount 0 eq
					{
						cleartomark stop
					} if
				} if
			} ifelse
			shiftBuffer
		} loop
	}
	{
	stop
	} ifelse
} def
2
{
	mark
	{
		currentfile buffer readline not
		{
		stop
		} if
		endString eq
		{
			cleartomark stop
		} if
	} loop
} def
3
{
	/beginString exch store
	/layerCnt 1 store
	mark
	{
		currentfile buffer readline not
		{
		stop
		} if
		dup beginString eq
		{
			pop /layerCnt dup load 1 add store
		}
		{
			endString eq
			{
				layerCnt 1 eq
				{
					cleartomark stop
				}
				{
					/layerCnt dup load 1 sub store
				} ifelse
			} if
		} ifelse
	} loop
} def
end
userdict /clipRenderOff 15 dict dup begin
put
{
	/n /N /s /S /f /F /b /B
}
{
	{
		_doClip 1 eq
		{
			/_doClip 0 ddef clip
		} if
		newpath
	} def
} forall
/Tr /pop load def
/Bb {} def
/BB /pop load def
/Bg {12 npop} def
/Bm {6 npop} def
/Bc /Bm load def
/Bh {4 npop} def
end
/Lb
{
	4 npop
	6 1 roll
	pop
	4 1 roll
	pop pop pop
	0 eq
	{
		0 eq
		{
			(%AI5_BeginLayer) 1 (%AI5_EndLayer--) discard
		}
		{
			/clipForward? true def
			
			/Tx /pop load def
			/Tj /pop load def
			currentdict end clipRenderOff begin begin
		} ifelse
	}
	{
		0 eq
		{
			save /discardSave exch store
		} if
	} ifelse
} bind def
/LB
{
	discardSave dup null ne
	{
		restore
	}
	{
		pop
		clipForward?
		{
			currentdict
		 end
		 end
		 begin
			
			/clipForward? false ddef
		} if
	} ifelse
} bind def
/Pb
{
	pop pop
	0 (%AI5_EndPalette) discard
} bind def
/Np
{
	0 (%AI5_End_NonPrinting--) discard
} bind def
/Ln /pop load def
/Ap
/pop load def
/Ar
{
	72 exch div
	0 dtransform dup mul exch dup mul add sqrt
	dup 1 lt
	{
		pop 1
	} if
	setflat
} def
/Mb
{
	q
} def
/Md
{
} def
/MB
{
	Q
} def
/nc 3 dict def
nc begin
/setgray
{
	pop
} bind def
/setcmykcolor
{
	4 npop
} bind def
/setcustomcolor
{
	2 npop
} bind def
currentdict readonly pop
end
currentdict readonly pop
end
setpacking
%%EndResource
%%EndProlog
%%BeginSetup
Adobe_level2_AI5 /initialize get exec
Adobe_IllustratorA_AI5 /initialize get exec
%AI5_Begin_NonPrinting
Np
%AI3_BeginPattern: (Yellow Stripe)
(Yellow Stripe) 8.4499 4.6 80.4499 76.6 [
%AI3_Tile
(0 O 0 R 0 0.4 1 0 k 0 0.4 1 0 K) @
(
800 Ar
0 J 0 j 3.6 w 4 M []0 d
%AI3_Note:
0 D
8.1999 8.1999 m
80.6999 8.1999 L
S
8.1999 22.6 m
80.6999 22.6 L
S
8.1999 37.0001 m
80.6999 37.0001 L
S
8.1999 51.3999 m
80.6999 51.3999 L
S
8.1999 65.8 m
80.6999 65.8 L
S
8.1999 15.3999 m
80.6999 15.3999 L
S
8.1999 29.8 m
80.6999 29.8 L
S
8.1999 44.1999 m
80.6999 44.1999 L
S
8.1999 58.6 m
80.6999 58.6 L
S
8.1999 73.0001 m
80.6999 73.0001 L
S
) &
] E
%AI3_EndPattern
%AI5_End_NonPrinting--
%AI5_Begin_NonPrinting
Np
3 Bn
%AI5_BeginGradient: (Black & White)
(Black & White) 0 2 Bd
[
<
FFFEFDFCFBFAF9F8F7F6F5F4F3F2F1F0EFEEEDECEBEAE9E8E7E6E5E4E3E2E1E0DFDEDDDCDBDAD9D8
D7D6D5D4D3D2D1D0CFCECDCCCBCAC9C8C7C6C5C4C3C2C1C0BFBEBDBCBBBAB9B8B7B6B5B4B3B2B1B0
AFAEADACABAAA9A8A7A6A5A4A3A2A1A09F9E9D9C9B9A999897969594939291908F8E8D8C8B8A8988
87868584838281807F7E7D7C7B7A797877767574737271706F6E6D6C6B6A69686766656463626160
5F5E5D5C5B5A595857565554535251504F4E4D4C4B4A494847464544434241403F3E3D3C3B3A3938
37363534333231302F2E2D2C2B2A292827262524232221201F1E1D1C1B1A19181716151413121110
0F0E0D0C0B0A09080706050403020100
>
0 %_Br
[
0 0 50 100 %_Bs
1 0 50 0 %_Bs
BD
%AI5_EndGradient
%AI5_BeginGradient: (Red & Yellow)
(Red & Yellow) 0 2 Bd
[
0
<
000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
28292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F
505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071727374757677
78797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9F
A0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
C8C9CACBCCCDCECFD0D1D2D3D4D5D6D7D8D9DADBDCDDDEDFE0E1E2E3E4E5E6E7E8E9EAEBECEDEEEF
F0F1F2F3F4F5F6F7F8F9FAFBFCFDFEFF
>
<
FFFFFEFEFDFDFDFCFCFBFBFBFAFAF9F9F9F8F8F7F7F7F6F6F5F5F5F4F4F3F3F3F2F2F1F1F1F0F0EF
EFEFEEEEEDEDEDECECEBEBEBEAEAE9E9E9E8E8E7E7E7E6E6E5E5E5E4E4E3E3E3E2E2E1E1E1E0E0DF
DFDFDEDEDDDDDDDCDCDBDBDBDADAD9D9D9D8D8D7D7D7D6D6D5D5D5D4D4D3D3D3D2D2D1D1D1D0D0CF
CFCFCECECDCDCDCCCCCBCBCBCACAC9C9C9C8C8C7C7C7C6C6C5C5C5C4C4C3C3C3C2C2C1C1C1C0C0BF
BFBFBEBEBDBDBDBCBCBBBBBBBABAB9B9B9B8B8B7B7B7B6B6B5B5B5B4B4B3B3B3B2B2B1B1B1B0B0AF
AFAFAEAEADADADACACABABABAAAAA9A9A9A8A8A7A7A7A6A6A5A5A5A4A4A3A3A3A2A2A1A1A1A0A09F
9F9F9E9E9D9D9D9C9C9B9B9B9A9A9999
>
0
1 %_Br
[
0 1 0.6 0 1 50 100 %_Bs
0 0 1 0 1 50 0 %_Bs
BD
%AI5_EndGradient
%AI5_BeginGradient: (Yellow & Blue Radial)
(Yellow & Blue Radial) 1 2 Bd
[
<
000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
28292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F
505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071727374757677
78797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9F
A0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
C8C9CACBCCCDCECFD0D1D2D3D4D5D6D7D8D9DADBDCDDDEDFE0E1E2E3E4E5E6E7E8E9EAEBECEDEEEF
F0F1F2F3F4F5F6F7F8F9FAFBFCFDFEFF
>
<
1415161718191A1B1C1D1E1F1F202122232425262728292A2A2B2C2D2E2F30313233343536363738
393A3B3C3D3E3F40414142434445464748494A4B4C4D4D4E4F50515253545556575858595A5B5C5D
5E5F60616263646465666768696A6B6C6D6E6F6F707172737475767778797A7B7B7C7D7E7F808182
83848586868788898A8B8C8D8E8F90919292939495969798999A9B9C9D9D9E9FA0A1A2A3A4A5A6A7
A8A9A9AAABACADAEAFB0B1B2B3B4B4B5B6B7B8B9BABBBCBDBEBFC0C0C1C2C3C4C5C6C7C8C9CACBCB
CCCDCECFD0D1D2D3D4D5D6D7D7D8D9DADBDCDDDEDFE0E1E2E2E3E4E5E6E7E8E9EAEBECEDEEEEEFF0
F1F2F3F4F5F6F7F8F9F9FAFBFCFDFEFF
>
<
ABAAAAA9A8A7A7A6A5A5A4A3A3A2A1A1A09F9F9E9D9D9C9B9B9A9999989797969595949393929191
908F8F8E8D8D8C8B8B8A8989888787868585848383828181807F7F7E7D7D7C7B7B7A797978777776
7575747373727171706F6F6E6D6D6C6B6B6A6969686767666565646362626160605F5E5E5D5C5C5B
5A5A5958585756565554545352525150504F4E4E4D4C4C4B4A4A4948484746464544444342424140
403F3E3E3D3C3C3B3A3A3938383736363534343332323130302F2E2E2D2C2C2B2A2A292828272626
25242423222121201F1F1E1D1D1C1B1B1A1919181717161515141313121111100F0F0E0D0D0C0B0B
0A090908070706050504030302010100
>
0
1 %_Br
[
0 0.08 0.67 0 1 50 14 %_Bs
1 1 0 0 1 50 100 %_Bs
BD
%AI5_EndGradient
%AI5_End_NonPrinting--
%AI5_BeginPalette
144 170 Pb
Pn
Pc
1 g
Pc
0 g
Pc
0 0 0 0 k
Pc
0.75 g
Pc
0.5 g
Pc
0.25 g
Pc
0 g
Pc
Bb
2 (Black & White) -4014 4716 0 0 1 0 0 1 0 0 Bg
0 BB
Pc
0.25 0 0 0 k
Pc
0.5 0 0 0 k
Pc
0.75 0 0 0 k
Pc
1 0 0 0 k
Pc
0.25 0.25 0 0 k
Pc
0.5 0.5 0 0 k
Pc
0.75 0.75 0 0 k
Pc
1 1 0 0 k
Pc
Bb
2 (Red & Yellow) -4014 4716 0 0 1 0 0 1 0 0 Bg
0 BB
Pc
0 0.25 0 0 k
Pc
0 0.5 0 0 k
Pc
0 0.75 0 0 k
Pc
0 1 0 0 k
Pc
0 0.25 0.25 0 k
Pc
0 0.5 0.5 0 k
Pc
0 0.75 0.75 0 k
Pc
0 1 1 0 k
Pc
Bb
0 0 0 0 Bh
2 (Yellow & Blue Radial) -4014 4716 0 0 1 0 0 1 0 0 Bg
0 BB
Pc
0 0 0.25 0 k
Pc
0 0 0.5 0 k
Pc
0 0 0.75 0 k
Pc
0 0 1 0 k
Pc
0.25 0 0.25 0 k
Pc
0.5 0 0.5 0 k
Pc
0.75 0 0.75 0 k
Pc
1 0 1 0 k
Pc
(Yellow Stripe) 0 0 1 1 0 0 0 0 0 [1 0 0 1 0 0] p
Pc
0.25 0.125 0 0 k
Pc
0.5 0.25 0 0 k
Pc
0.75 0.375 0 0 k
Pc
1 0.5 0 0 k
Pc
0.125 0.25 0 0 k
Pc
0.25 0.5 0 0 k
Pc
0.375 0.75 0 0 k
Pc
0.5 1 0 0 k
Pc
0.375 0.375 0.75 0 k
Pc
0 0.25 0.125 0 k
Pc
0 0.5 0.25 0 k
Pc
0 0.75 0.375 0 k
Pc
0 1 0.5 0 k
Pc
0 0.125 0.25 0 k
Pc
0 0.25 0.5 0 k
Pc
0 0.375 0.75 0 k
Pc
0 0.5 1 0 k
Pc
0 0.79 0.91 0 (TCL RED) 0 x
Pc
0.125 0 0.25 0 k
Pc
0.25 0 0.5 0 k
Pc
0.375 0 0.75 0 k
Pc
0.5 0 1 0 k
Pc
0.25 0 0.125 0 k
Pc
0.5 0 0.25 0 k
Pc
0.75 0 0.375 0 k
Pc
1 0 0.5 0 k
Pc
0.5 1 0 0 k
Pc
0.25 0.125 0.125 0 k
Pc
0.5 0.25 0.25 0 k
Pc
0.75 0.375 0.375 0 k
Pc
1 0.5 0.5 0 k
Pc
0.25 0.25 0.125 0 k
Pc
0.5 0.5 0.25 0 k
Pc
0.75 0.75 0.375 0 k
Pc
1 1 0.5 0 k
Pc
0 1 0.5 0 k
Pc
0.125 0.25 0.125 0 k
Pc
0.25 0.5 0.25 0 k
Pc
0.375 0.75 0.375 0 k
Pc
0.5 1 0.5 0 k
Pc
0.125 0.25 0.25 0 k
Pc
0.25 0.5 0.5 0 k
Pc
0.375 0.75 0.75 0 k
Pc
0.5 1 1 0 k
Pc
0.75 0.75 0.375 0 k
Pc
0.125 0.125 0.25 0 k
Pc
0.25 0.25 0.5 0 k
Pc
0.375 0.375 0.75 0 k
Pc
0.5 0.5 1 0 k
Pc
0.25 0.125 0.25 0 k
Pc
0.5 0.25 0.5 0 k
Pc
0.75 0.375 0.75 0 k
Pc
1 0.5 1 0 k
Pc
0 0.79 0.91 0 (TCL RED) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
1 0.5 0.5 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0.25 1 0 (Orange Yellow) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 1 0.5 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
1 0 0.5 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0.45 1 0 (Orange) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0.375 0.375 0.75 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0.79 0.91 0 (TCL RED) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
1 0.65 0 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0 1 0 k
Pc
PB
%AI5_EndPalette
%%EndSetup
%AI5_BeginLayer
1 1 1 1 0 0 0 79 128 255 Lb
(Layer 1) Ln
0 A
u
1 Ap
0 O
0 0.79 0.91 0 (TCL RED) 0 x
800 Ar
0 J 0 j 1.25 w 4 M []0 d
%AI3_Note:
0 D
294.5207 335.3041 m
368.2181 333.001 L
363.6121 423.9713 L
370.5213 507.1689 L
336.5513 505.4417 L
320.7179 511.775 L
251.3386 508.0325 L
254.7931 425.9866 L
251.3386 331.5616 L
294.5207 335.3041 L
f
u
0 Ap
1 0.65 0 0 k
1 w
318.1366 400.9627 m
311.8663 399.2526 l
315.2864 407.5177 l
318.7064 430.6032 l
314.4314 431.4581 l
319.5616 438.5832 l
325.9526 462.6014 l
314.7164 460.2436 l
320.6412 471.0911 326.9284 478.1557 v
318.7064 484.469 l
292.2183 472.8011 299.3434 434.8954 v
293.8679 435.8542 l
299.1189 396.1175 l
294.6797 394.9775 l
299.2277 385.6974 305.5963 381.2973 v
306.1744 380.8979 297.6162 412.3629 306.7363 443.7133 c
307.5914 441.7183 l
300.3238 408.3015 307.5914 381.2973 v
307.9261 380.656 311.5598 381.0836 v
318.1366 393.4813 318.1366 400.9627 v
f
u
*u
1 g
271.4311 372.5074 m
272.7184 372.5074 L
272.7184 375.1913 L
273.2858 375.1913 273.8313 375.1913 274.3768 375.2786 c
274.3768 372.5074 L
276.2969 372.5074 L
276.2969 372.0056 L
274.3768 372.0056 L
274.3768 365.3286 L
274.3768 364.9359 274.3768 364.3467 275.2059 364.3467 c
275.7733 364.3467 276.0787 364.7395 276.4279 365.1541 c
276.777 364.9141 L
276.3624 364.0849 275.2932 363.583 274.4204 363.583 c
272.8494 363.583 272.6748 364.434 272.6748 365.4814 c
272.6748 372.0056 L
271.4311 372.0056 L
271.4311 372.5074 l
f
*U
*u
290.5617 366.5724 m
290.0598 365.0232 289.187 363.6703 286.9178 363.583 c
283.5356 363.583 282.5101 366.3978 282.5101 367.9034 c
282.5101 371.7874 285.6304 372.7256 286.8741 372.7256 c
288.2924 372.7256 290.2999 372.071 290.2999 370.3909 c
290.2999 369.8018 289.9289 369.2344 289.318 369.2344 c
288.7288 369.2344 288.2924 369.6272 288.2924 370.26 c
288.2924 371.111 288.9907 371.2201 288.9907 371.4601 c
288.9907 372.0492 287.616 372.2892 287.136 372.2892 c
285.0412 372.2892 284.4957 370.7618 284.4957 367.9034 c
284.4957 366.5942 284.823 365.5905 284.9539 365.285 c
285.2812 364.5649 285.9577 364.1067 287.0923 364.0413 c
288.3579 363.9758 289.5798 365.0013 290.1035 366.5724 C
290.5617 366.5724 l
f
*U
*u
296.6 363.8667 m
296.6 364.3686 L
298.2802 364.3686 L
298.2802 378.3989 L
296.6 378.3989 L
296.6 378.9007 L
297.5383 378.9007 L
298.3457 378.9007 299.1966 378.9444 299.9822 379.0971 c
299.9822 364.3686 L
301.6623 364.3686 L
301.6623 363.8667 L
296.6 363.8667 l
f
*U
*u
317.4527 372.5074 m
318.7401 372.5074 L
318.7401 375.1913 L
319.3074 375.1913 319.8529 375.1913 320.3984 375.2786 c
320.3984 372.5074 L
322.3186 372.5074 L
322.3186 372.0056 L
320.3984 372.0056 L
320.3984 365.3286 L
320.3984 364.9359 320.3984 364.3467 321.2276 364.3467 c
321.7949 364.3467 322.1004 364.7395 322.4495 365.1541 c
322.7986 364.9141 L
322.384 364.0849 321.3148 363.583 320.442 363.583 c
318.871 363.583 318.6964 364.434 318.6964 365.4814 c
318.6964 372.0056 L
317.4527 372.0056 L
317.4527 372.5074 l
f
*U
*u
333.7467 372.0056 m
333.7467 372.5074 L
337.3252 372.5074 L
337.3252 372.0056 L
335.9942 372.0056 L
332.983 369.3872 L
337.1288 364.3686 L
338.0453 364.3686 L
338.0453 363.8667 L
333.8995 363.8667 L
333.8995 364.3686 L
334.9905 364.3686 L
331.3465 368.798 L
335.0341 371.9401 L
335.0341 372.0056 L
333.7467 372.0056 l
f
328.4881 363.8667 m
328.4881 364.3686 L
329.6227 364.3686 L
329.6227 378.3989 L
328.4881 378.3989 L
328.4881 378.9007 L
328.8809 378.9007 L
329.6882 378.9007 330.5392 378.9444 331.3247 379.0971 c
331.3247 364.3686 L
332.6339 364.3686 L
332.6339 363.8667 L
328.4881 363.8667 l
f
*U
u
309.5341 446.5364 m
305.6878 429.3874 306.7947 401.5837 v
307.1266 393.2441 308.0387 385.5779 309.1527 378.9301 C
309.1587 378.9297 L
309.8832 373.0923 310.3679 370.9791 312.2568 363.9454 C
312.1466 359.4091 L
297.0216 407.7015 309.5341 446.5364 V
f
318.8187 461.4058 m
322.2203 463.1 327.0966 463.7165 v
332.427 453.9463 319.3087 437.2655 v
327.1346 454.735 325.2889 460.2079 v
323.225 461.4903 318.8187 461.4058 v
f
317.2065 432.0795 m
320.2613 431.3723 321.7279 432.5601 v
318.8383 421.2839 319.5958 415.0813 v
320.3533 408.8787 314.8881 404.9079 y
319.5435 410.7982 318.0802 415.5959 v
317.0657 418.9214 318.2006 427.4326 319.4809 430.1349 c
318.2853 430.3025 317.2065 432.0795 v
f
314.1861 402.3703 m
319.2343 402.9744 319.7646 405.5244 v
320.3824 390.2725 313.3689 383.9873 v
318.7204 392.3347 317.8807 400.9697 v
314.1861 402.3703 l
f
299.9864 396.0219 m
298.3586 394.1986 293.4739 398.2203 v
295.0301 387.9694 304.6978 383.2767 v
298.0444 388.2897 296.2519 393.7045 v
298.6029 394.3966 299.9864 396.0219 v
f
298.4281 399.9096 m
291.8229 416.6749 293.2382 439.3286 v
294.7808 435.2261 299.738 433.7875 v
297.4026 433.3101 296.0372 433.517 v
292.5816 423.9535 298.4281 399.9096 v
f
326.1736 477.812 m
323.6983 496.0028 308.2122 477.6066 v
295.8813 462.9582 297.3508 450.5217 298.1072 443.5831 c
298.3007 441.8079 295.8131 462.1138 309.3231 475.4768 c
322.8328 488.8398 325.8846 478.5879 326.1736 477.812 c
f
U
0 0 1 0 k
303.3623 493.3274 m
291.211 496.7978 287.3437 456.5222 v
284.3599 468.9535 292.0777 486.5353 v
299.7955 504.1172 303.3623 493.3274 y
f
288.2873 496.2718 m
282.0897 486.9502 283.4958 477.0213 v
278.7953 495.712 288.2873 496.2718 v
f
333.8987 470.1328 m
341.2276 472.8361 330.7334 445.5571 v
336.1654 453.5292 339.5844 466.0531 v
341.7789 474.0903 333.8987 470.1328 y
f
345.752 472.2583 m
350.9334 467.5681 347.2615 461.3636 v
356.4779 471.0481 345.752 472.2583 v
f
U
*u
273.1765 354.3318 m
273.1765 353.7507 273.1305 353.2908 272.5159 353.2908 c
271.8846 353.2908 271.8554 353.7674 271.8554 354.3318 c
271.8554 356.485 L
272.148 356.485 L
272.148 354.3486 L
272.148 353.8259 272.1773 353.5751 272.5159 353.5751 c
272.8504 353.5751 272.8839 353.8259 272.8839 354.3486 c
272.8839 356.485 L
273.1765 356.485 L
273.1765 354.3318 l
f
*U
*u
277.1612 356.485 m
276.9062 356.485 L
276.9062 354.3862 l
276.9062 354.2482 276.9271 354.1061 276.9355 353.9681 C
276.9229 353.9681 l
276.8937 354.0768 276.8644 354.1855 276.8268 354.2942 C
276.1035 356.485 L
275.8484 356.485 L
275.8484 353.3326 L
276.1035 353.3326 L
276.1035 355.2474 l
276.1035 355.4523 276.0826 355.653 276.07 355.8579 C
276.0867 355.8579 l
276.1244 355.7241 276.1495 355.5819 276.1954 355.4523 C
276.9062 353.3326 L
277.1612 353.3326 l
277.1612 356.485 L
f
*U
*u
280.1421 353.3326 m
279.8494 353.3326 L
279.8494 356.485 L
280.1421 356.485 L
280.1421 353.3326 l
f
*U
*u
283.5141 353.3326 m
283.2549 353.3326 L
282.6194 356.485 L
282.9205 356.485 L
283.3344 354.1897 L
283.3511 354.1102 283.3678 353.9054 283.3845 353.7632 c
283.4013 353.7632 L
283.4138 353.9054 283.4305 354.1144 283.4431 354.1897 c
283.8528 356.485 L
284.1496 356.485 L
283.5141 353.3326 l
f
*U
*u
287.6238 356.2174 m
286.9256 356.2174 L
286.9256 355.1053 L
287.6029 355.1053 L
287.6029 354.8377 L
286.9256 354.8377 L
286.9256 353.6002 L
287.6238 353.6002 L
287.6238 353.3326 L
286.6329 353.3326 L
286.6329 356.485 L
287.6238 356.485 L
287.6238 356.2174 l
f
*U
*u
290.2278 353.3326 m
290.2278 356.485 L
290.5414 356.485 L
290.9804 356.485 291.4026 356.4515 291.4026 355.6823 c
291.4026 355.2809 291.3148 354.8879 290.8089 354.8712 c
291.5072 353.3326 L
291.1978 353.3326 L
290.5288 354.8753 L
290.5205 354.8753 L
290.5205 353.3326 L
290.2278 353.3326 l
f
290.5205 355.1137 m
290.625 355.1137 L
291.0347 355.1137 291.1016 355.2558 291.1016 355.6697 c
291.1016 356.1672 290.9511 356.2174 290.579 356.2174 c
290.5205 356.2174 L
290.5205 355.1137 l
f
*U
*u
295.0981 355.9875 m
294.9727 356.1296 294.8347 356.2425 294.634 356.2425 c
294.3414 356.2425 294.1783 356 294.1783 355.7324 c
294.1783 355.3645 294.4459 355.1931 294.7176 355.0091 c
294.9852 354.821 295.2528 354.6203 295.2528 354.1855 c
295.2528 353.7256 294.9559 353.2908 294.4626 353.2908 c
294.287 353.2908 294.1072 353.341 293.9651 353.4497 c
293.9651 353.8301 L
294.0989 353.688 294.2745 353.5751 294.4751 353.5751 c
294.7845 353.5751 294.9559 353.8468 294.9518 354.1311 c
294.9559 354.4991 294.6842 354.6621 294.4166 354.8503 c
294.149 355.0342 293.8773 355.2391 293.8773 355.6906 c
293.8773 356.1129 294.1365 356.5268 294.6006 356.5268 c
294.7887 356.5268 294.9476 356.4641 295.0981 356.3596 C
295.0981 355.9875 l
f
*U
*u
299.0865 353.3326 m
298.773 353.3326 L
298.6559 353.9806 L
297.9869 353.9806 L
297.8741 353.3326 L
297.5605 353.3326 L
298.1793 356.485 L
298.4552 356.485 L
299.0865 353.3326 l
f
298.6099 354.2357 m
298.4009 355.444 L
298.3632 355.6572 298.3465 355.8746 298.3214 356.0878 c
298.3047 356.0878 L
298.2754 355.8746 298.2545 355.6572 298.2211 355.444 c
298.0371 354.2357 L
298.6099 354.2357 l
f
*U
*u
301.8124 353.6002 m
302.4981 353.6002 L
302.4981 353.3326 L
301.5198 353.3326 L
301.5198 356.485 L
301.8124 356.485 L
301.8124 353.6002 l
f
*U
*u
309.0754 355.9875 m
308.95 356.1296 308.812 356.2425 308.6114 356.2425 c
308.3187 356.2425 308.1556 356 308.1556 355.7324 c
308.1556 355.3645 308.4232 355.1931 308.695 355.0091 c
308.9626 354.821 309.2301 354.6203 309.2301 354.1855 c
309.2301 353.7256 308.9333 353.2908 308.4399 353.2908 c
308.2643 353.2908 308.0846 353.341 307.9424 353.4497 c
307.9424 353.8301 L
308.0762 353.688 308.2518 353.5751 308.4525 353.5751 c
308.7619 353.5751 308.9333 353.8468 308.9291 354.1311 c
308.9333 354.4991 308.6615 354.6621 308.3939 354.8503 c
308.1264 355.0342 307.8546 355.2391 307.8546 355.6906 c
307.8546 356.1129 308.1138 356.5268 308.5779 356.5268 c
308.766 356.5268 308.9249 356.4641 309.0754 356.3596 C
309.0754 355.9875 l
f
*U
*u
312.9468 353.7172 m
312.8339 353.6378 312.7001 353.5751 312.558 353.5751 c
311.9977 353.5751 311.9977 354.5492 311.9977 354.9172 c
311.9977 355.5025 312.0688 356.2425 312.5789 356.2425 c
312.7252 356.2425 312.8297 356.184 312.9468 356.1045 C
312.9468 356.4265 l
312.8506 356.4975 312.6918 356.5268 312.5747 356.5268 c
311.7134 356.5268 311.6967 355.306 311.6967 354.7959 c
311.6967 354.2566 311.8054 353.2908 312.5454 353.2908 c
312.6834 353.2908 312.8381 353.3451 312.9468 353.4204 c
312.9468 353.7172 L
f
*U
*u
315.5053 353.3326 m
315.5053 356.485 L
315.8188 356.485 L
316.2578 356.485 316.6801 356.4515 316.6801 355.6823 c
316.6801 355.2809 316.5923 354.8879 316.0864 354.8712 c
316.7846 353.3326 L
316.4752 353.3326 L
315.8063 354.8753 L
315.7979 354.8753 L
315.7979 353.3326 L
315.5053 353.3326 l
f
315.7979 355.1137 m
315.9025 355.1137 L
316.3122 355.1137 316.3791 355.2558 316.3791 355.6697 c
316.3791 356.1672 316.2286 356.2174 315.8565 356.2174 c
315.7979 356.2174 L
315.7979 355.1137 l
f
*U
*u
319.5728 353.3326 m
319.2802 353.3326 L
319.2802 356.485 L
319.5728 356.485 L
319.5728 353.3326 l
f
*U
*u
322.2551 353.3326 m
322.2551 356.485 L
322.5812 356.485 L
323.0327 356.485 323.4341 356.4432 323.4341 355.6655 c
323.4341 355.0551 323.2209 354.8419 322.623 354.8419 c
322.5477 354.8419 L
322.5477 353.3326 L
322.2551 353.3326 l
f
322.5477 355.1095 m
322.6606 355.1095 L
323.0703 355.1095 323.1205 355.26 323.1331 355.6655 c
323.1331 356.1004 323.016 356.2174 322.6063 356.2174 c
322.5477 356.2174 L
322.5477 355.1095 l
f
*U
*u
326.9539 356.485 m
325.7164 356.485 L
325.7164 356.2174 L
326.1888 356.2174 L
326.1888 353.3326 L
326.4815 353.3326 L
326.4815 356.2174 L
326.9539 356.2174 l
326.9539 356.485 L
f
*U
*u
329.7077 353.3326 m
329.4151 353.3326 L
329.4151 356.485 L
329.7077 356.485 L
329.7077 353.3326 l
f
*U
*u
333.7028 353.3326 m
333.4477 353.3326 L
332.737 355.4523 L
332.691 355.5819 332.6659 355.7241 332.6283 355.8579 c
332.6116 355.8579 L
332.6241 355.653 332.645 355.4523 332.645 355.2474 c
332.645 353.3326 L
332.39 353.3326 L
332.39 356.485 L
332.645 356.485 L
333.3683 354.2942 L
333.4059 354.1855 333.4352 354.0768 333.4645 353.9681 c
333.477 353.9681 L
333.4686 354.1061 333.4477 354.2482 333.4477 354.3862 c
333.4477 356.485 L
333.7028 356.485 L
333.7028 353.3326 l
f
*U
*u
336.9846 354.9966 m
337.7037 354.9966 L
337.7037 354.4154 L
337.7037 353.9179 337.6787 353.2908 337.0264 353.2908 c
336.3617 353.2908 336.299 353.989 336.299 354.9841 c
336.299 355.7283 336.3868 356.5268 337.0557 356.5268 c
337.432 356.5268 337.6201 356.276 337.6996 355.9331 c
337.4111 355.8202 L
337.3776 356.0084 337.2982 356.2425 337.0682 356.2425 c
336.6334 356.2383 336.6 355.5652 336.6 355.0091 c
336.6 353.8427 336.7463 353.5751 337.0515 353.5751 c
337.3818 353.5751 337.4111 353.8176 337.4111 354.4907 c
337.4111 354.729 L
336.9846 354.729 L
336.9846 354.9966 l
f
*U
U
U
337.6667 -3924 m
(N) *
337.6667 4716 m
(N) *
LB
%AI5_EndLayer--
%%PageTrailer
gsave annotatepage grestore showpage
%%Trailer
Adobe_IllustratorA_AI5 /terminate get exec
Adobe_level2_AI5 /terminate get exec
%%EOF
