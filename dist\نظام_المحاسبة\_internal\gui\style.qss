/*
===========================================
    نظام المحاسبة - التصميم العصري 2024
    مستوحى من أفضل التطبيقات العالمية
===========================================
*/

/* الإعدادات العامة */
* {
    font-family: "Segoe UI", "Cairo", "Tajawal", "Arial", sans-serif;
    font-size: 14px;
    outline: none;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
    color: #2d3748;
}

/* ========================================
   شريط القوائم العصري مع Glass Effect
======================================== */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1a202c, stop:1 #2d3748);
    color: white;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #4a5568;
}

QMenuBar::item {
    background-color: transparent;
    padding: 12px 20px;
    margin: 0 5px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
}

QMenuBar::item:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
}

/* ========================================
   شريط الأدوات العصري
======================================== */
QToolBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(26, 32, 44, 0.95), stop:1 rgba(45, 55, 72, 0.95));
    border: none;
    spacing: 8px;
    padding: 10px 15px;
    min-height: 60px;
    max-height: 60px;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

QToolButton {
    background-color: transparent;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 10px 18px;
    font-size: 14px;
    font-weight: 600;
    min-width: 120px;
    min-height: 40px;
    margin: 2px;
}

QToolButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

QToolButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
}

QToolBar::separator {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 transparent, stop:0.5 rgba(255, 255, 255, 0.3), stop:1 transparent);
    width: 1px;
    margin: 10px 8px;
}

/* ========================================
   القوائم المنسدلة العصرية
======================================== */
QMenu {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 15px;
    padding: 10px;
    font-size: 14px;
    backdrop-filter: blur(30px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

QMenu::item {
    padding: 12px 20px;
    border-radius: 10px;
    margin: 2px;
    color: #2d3748;
    font-weight: 500;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
}

QMenu::separator {
    height: 1px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent, stop:0.5 #e2e8f0, stop:1 transparent);
    margin: 8px 15px;
}

/* ========================================
   الأزرار العصرية مع تأثيرات متقدمة
======================================== */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    min-height: 45px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    transform: translateY(-2px);
}

QPushButton:pressed {
    transform: translateY(0px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #cbd5e0, stop:1 #a0aec0);
    color: #718096;
    box-shadow: none;
}

/* أزرار ملونة خاصة */
QPushButton[class="success"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #48bb78, stop:1 #38a169);
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
}

QPushButton[class="success"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #38a169, stop:1 #2f855a);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.6);
}

QPushButton[class="danger"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f56565, stop:1 #e53e3e);
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
}

QPushButton[class="danger"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e53e3e, stop:1 #c53030);
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.6);
}

QPushButton[class="warning"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ed8936, stop:1 #dd6b20);
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
}

QPushButton[class="warning"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dd6b20, stop:1 #c05621);
    box-shadow: 0 8px 25px rgba(237, 137, 54, 0.6);
}

/* ========================================
   حقول الإدخال العصرية مع Glass Effect
======================================== */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 14px 18px;
    font-size: 14px;
    color: #2d3748;
    selection-background-color: #667eea;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #667eea;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
                0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
    border-color: #cbd5e0;
    background-color: rgba(255, 255, 255, 0.95);
}

/* ========================================
   القوائم المنسدلة العصرية
======================================== */
QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
    background-color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    padding: 14px 18px;
    font-size: 14px;
    min-height: 20px;
    color: #2d3748;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border-color: #667eea;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
                0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

QComboBox:hover, QSpinBox:hover, QDoubleSpinBox:hover, QDateEdit:hover {
    border-color: #cbd5e0;
    background-color: rgba(255, 255, 255, 0.95);
}

QComboBox::drop-down {
    border: none;
    width: 40px;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent, stop:1 rgba(102, 126, 234, 0.1));
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDE0IDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNyA3TDEzIDEiIHN0cm9rZT0iIzY2N2VlYSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 14px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    selection-background-color: #667eea;
    outline: none;
    backdrop-filter: blur(30px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    padding: 5px;
}

/* ========================================
   التسميات العصرية
======================================== */
QLabel {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    padding: 4px;
}

/* ========================================
   مربعات الاختيار والراديو العصرية
======================================== */
QCheckBox, QRadioButton {
    font-size: 14px;
    spacing: 10px;
    color: #2d3748;
    font-weight: 500;
}

QCheckBox::indicator, QRadioButton::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e0;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

QCheckBox::indicator {
    border-radius: 6px;
}

QRadioButton::indicator {
    border-radius: 10px;
}

QCheckBox::indicator:checked, QRadioButton::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    border-color: #667eea;
}

QCheckBox::indicator:hover, QRadioButton::indicator:hover {
    border-color: #667eea;
    background-color: rgba(255, 255, 255, 1);
}

/* ========================================
   النوافذ والحوارات العصرية
======================================== */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f7fafc, stop:1 #edf2f7);
    font-size: 14px;
    border-radius: 20px;
}

QMessageBox {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f7fafc, stop:1 #edf2f7);
    font-size: 14px;
    border-radius: 15px;
}

/* ========================================
   التلميحات العصرية
======================================== */
QToolTip {
    background-color: rgba(26, 32, 44, 0.95);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ========================================
   الجداول العصرية مع Glass Effect
======================================== */
QTableWidget {
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 20px;
    gridline-color: rgba(241, 245, 249, 0.8);
    font-size: 14px;
    backdrop-filter: blur(30px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
    show-decoration-selected: 1;
    outline: 0;
}

QTableWidget::item {
    padding: 15px 12px;
    border-bottom: 1px solid rgba(241, 245, 249, 0.8);
    color: #2d3748;
    font-weight: 500;
    min-height: 45px;
    text-align: center;
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(102, 126, 234, 0.1), stop:1 rgba(118, 75, 162, 0.1));
    color: #2d3748;
    border-radius: 8px;
}

QTableWidget::item:hover {
    background-color: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
    padding: 18px 12px;
    border: none;
    font-weight: 600;
    font-size: 14px;
    min-height: 50px;
    text-align: center;
}

QHeaderView::section:first {
    border-top-right-radius: 20px;
}

QHeaderView::section:last {
    border-top-left-radius: 20px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
}

/* أزرار الجداول العصرية */
QTableWidget QPushButton {
    margin: 4px;
    padding: 8px 16px;
    min-width: 80px;
    min-height: 35px;
    font-size: 12px;
    border-radius: 8px;
    font-weight: 600;
}

/* ========================================
   أشرطة التمرير العصرية
======================================== */
QScrollBar:vertical {
    background-color: rgba(241, 245, 249, 0.8);
    width: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    border-radius: 7px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5a67d8, stop:1 #553c9a);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: rgba(241, 245, 249, 0.8);
    height: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
    border-radius: 7px;
    min-width: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5a67d8, stop:1 #553c9a);
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* ========================================
   التبويبات العصرية
======================================== */
QTabWidget::pane {
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 15px;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

QTabBar::tab {
    background-color: rgba(236, 240, 241, 0.8);
    padding: 15px 30px;
    margin-left: 3px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    min-width: 120px;
    backdrop-filter: blur(10px);
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

QTabBar::tab:hover {
    background-color: rgba(203, 213, 224, 0.8);
}

/* ========================================
   المجموعات والإطارات العصرية
======================================== */
QGroupBox {
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 15px;
    margin-top: 1.5em;
    padding-top: 25px;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 15px;
    font-size: 16px;
    font-weight: 700;
    color: #667eea;
}

/* ========================================
   شريط الحالة العصري
======================================== */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(26, 32, 44, 0.95), stop:1 rgba(45, 55, 72, 0.95));
    color: white;
    font-size: 12px;
    min-height: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

/* ========================================
   أنماط خاصة للبطاقات الملونة
======================================== */
QFrame[class="stat_card"] {
    border-radius: 20px;
    padding: 30px;
    min-height: 160px;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

QFrame[class="stat_card"] QLabel {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

QLabel[class="card_title"] {
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

QLabel[class="card_value"] {
    font-size: 32px;
    font-weight: 700;
    color: white;
}

/* ========================================
   أزرار خاصة للجداول
======================================== */
QPushButton[toolTip*="حذف"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f56565, stop:1 #e53e3e);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    min-width: 70px;
    min-height: 35px;
    margin: 2px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
}

QPushButton[toolTip*="حذف"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e53e3e, stop:1 #c53030);
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.6);
}

QPushButton[toolTip*="تعديل"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #ed8936, stop:1 #dd6b20);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    min-width: 70px;
    min-height: 35px;
    margin: 2px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
}

QPushButton[toolTip*="تعديل"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dd6b20, stop:1 #c05621);
    box-shadow: 0 8px 25px rgba(237, 137, 54, 0.6);
}

/* ========================================
   تحسينات عامة للتخطيط
======================================== */
QWidget {
    outline: none;
}

/* فئات الألوان للنصوص */
.success-text {
    color: #48bb78;
    font-weight: 600;
}

.error-text {
    color: #f56565;
    font-weight: 600;
}

.warning-text {
    color: #ed8936;
    font-weight: 600;
}

.info-text {
    color: #667eea;
    font-weight: 600;
}