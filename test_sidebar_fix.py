#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة البار الجانبي - التأكد من أن البار لا يظهر عند تمرير الماوس إذا كان مطفي
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, QTimer

def test_sidebar_fix():
    """اختبار إصلاح البار الجانبي"""
    print("🧪 بدء اختبار إصلاح البار الجانبي...")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        # محاكاة قاعدة البيانات
        from sqlalchemy import create_engine
        engine = create_engine('sqlite:///test_sidebar_fix.db', echo=False)
        
        # إنشاء مستخدم تجريبي
        class TestUser:
            def __init__(self):
                self.full_name = "مستخدم تجريبي"
                self.username = "test"
                self.role = "admin"
        
        test_user = TestUser()
        
        # إنشاء النافذة الرئيسية
        from gui.main_window import MainWindow
        main_window = MainWindow(engine=engine, user=test_user)
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار 1: التحقق من الحالة الافتراضية
        print(f"\n1️⃣ الحالة الافتراضية: البار الجانبي {'مفعل' if main_window.sidebar_enabled else 'مطفي'}")
        
        # اختبار 2: فتح تبويب المبيعات
        print("\n2️⃣ فتح تبويب المبيعات...")
        main_window.show_sales()
        
        # انتظار قصير لتحميل التبويب
        QTimer.singleShot(500, lambda: test_sales_sidebar_behavior(main_window))
        
        # عرض النافذة
        main_window.show()
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        QMessageBox.critical(None, "خطأ في الاختبار", f"حدث خطأ:\n{str(e)}")
        return 1

def test_sales_sidebar_behavior(main_window):
    """اختبار سلوك البار الجانبي في تبويب المبيعات"""
    try:
        print("🔍 اختبار سلوك البار الجانبي في تبويب المبيعات...")
        
        if hasattr(main_window, 'main_tabs'):
            current_tab = main_window.main_tabs.currentWidget()
            if current_tab:
                from PyQt5.QtWidgets import QFrame
                hidden_sidebars = current_tab.findChildren(QFrame, "hiddenSidebar")
                
                if hidden_sidebars:
                    sidebar = hidden_sidebars[0]
                    
                    # اختبار الحالة الحالية
                    is_visible = sidebar.isVisible()
                    width = sidebar.width()
                    
                    print(f"📊 حالة البار الجانبي:")
                    print(f"   - مرئي: {'نعم' if is_visible else 'لا'}")
                    print(f"   - العرض: {width} بكسل")
                    print(f"   - الإعدادات: {'مفعل' if main_window.sidebar_enabled else 'مطفي'}")
                    
                    # اختبار محاكاة تمرير الماوس
                    print("\n🖱️ محاكاة تمرير الماوس على البار...")
                    
                    if main_window.sidebar_enabled:
                        if is_visible and width == 5:
                            print("✅ البار مخفي بشكل صحيح (5 بكسل)")
                            
                            # محاكاة دخول الماوس
                            if hasattr(sidebar, 'enterEvent'):
                                print("   محاكاة دخول الماوس...")
                                try:
                                    # إنشاء حدث وهمي
                                    class FakeEvent:
                                        pass
                                    
                                    sidebar.enterEvent(FakeEvent())
                                    
                                    # التحقق من التوسع
                                    QTimer.singleShot(100, lambda: check_expansion(sidebar, main_window))
                                    
                                except Exception as e:
                                    print(f"   ⚠️ خطأ في محاكاة الحدث: {e}")
                            else:
                                print("   ❌ لا توجد أحداث تمرير")
                        else:
                            print(f"   ⚠️ البار في حالة غير متوقعة: مرئي={is_visible}, عرض={width}")
                    else:
                        if not is_visible or width == 0:
                            print("✅ البار مخفي تماماً كما هو متوقع (مطفي)")
                            
                            # محاكاة تمرير الماوس (يجب ألا يحدث شيء)
                            if hasattr(sidebar, 'enterEvent'):
                                print("   محاكاة تمرير الماوس (يجب ألا يحدث شيء)...")
                                try:
                                    class FakeEvent:
                                        pass
                                    
                                    sidebar.enterEvent(FakeEvent())
                                    
                                    # التحقق من عدم التوسع
                                    QTimer.singleShot(100, lambda: check_no_expansion(sidebar, main_window))
                                    
                                except Exception as e:
                                    print(f"   ✅ لا توجد استجابة للماوس (كما هو متوقع)")
                            else:
                                print("   ✅ لا توجد أحداث تمرير (كما هو متوقع)")
                        else:
                            print(f"   ❌ البار لا يزال مرئي رغم أنه مطفي! مرئي={is_visible}, عرض={width}")
                else:
                    print("⚠️ لم يتم العثور على البار الجانبي في تبويب المبيعات")
            else:
                print("⚠️ لا يوجد تبويب حالي")
        
        # اختبار تغيير الإعدادات
        QTimer.singleShot(1000, lambda: test_settings_change(main_window))
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سلوك البار: {e}")

def check_expansion(sidebar, main_window):
    """التحقق من توسع البار عند تمرير الماوس"""
    try:
        width = sidebar.width()
        if width > 5:
            print(f"   ✅ البار توسع بشكل صحيح إلى {width} بكسل")
        else:
            print(f"   ⚠️ البار لم يتوسع: {width} بكسل")
    except Exception as e:
        print(f"   ❌ خطأ في فحص التوسع: {e}")

def check_no_expansion(sidebar, main_window):
    """التحقق من عدم توسع البار عند تمرير الماوس (عندما يكون مطفي)"""
    try:
        width = sidebar.width()
        is_visible = sidebar.isVisible()
        
        if not is_visible or width <= 5:
            print(f"   ✅ البار لم يتوسع (كما هو متوقع): مرئي={is_visible}, عرض={width}")
        else:
            print(f"   ❌ البار توسع رغم أنه مطفي! مرئي={is_visible}, عرض={width}")
    except Exception as e:
        print(f"   ❌ خطأ في فحص عدم التوسع: {e}")

def test_settings_change(main_window):
    """اختبار تغيير الإعدادات"""
    try:
        print("\n3️⃣ اختبار تغيير الإعدادات...")
        
        # تغيير الإعدادات
        original_setting = main_window.sidebar_enabled
        new_setting = not original_setting
        
        print(f"   تغيير الإعدادات من {'مفعل' if original_setting else 'مطفي'} إلى {'مفعل' if new_setting else 'مطفي'}")
        
        # تطبيق الإعدادات الجديدة
        main_window.sidebar_enabled = new_setting
        main_window.save_sidebar_setting(new_setting)
        main_window.update_sidebar_visibility()
        
        # إعادة تطبيق الإعدادات بقوة
        if hasattr(main_window, 'force_update_all_sidebars'):
            main_window.force_update_all_sidebars()
        
        # التحقق من التطبيق
        QTimer.singleShot(300, lambda: verify_settings_applied(main_window, new_setting))
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تغيير الإعدادات: {e}")

def verify_settings_applied(main_window, expected_setting):
    """التحقق من تطبيق الإعدادات الجديدة"""
    try:
        print(f"🔍 التحقق من تطبيق الإعدادات الجديدة...")
        
        if hasattr(main_window, 'main_tabs'):
            current_tab = main_window.main_tabs.currentWidget()
            if current_tab:
                from PyQt5.QtWidgets import QFrame
                hidden_sidebars = current_tab.findChildren(QFrame, "hiddenSidebar")
                
                for sidebar in hidden_sidebars:
                    is_visible = sidebar.isVisible()
                    width = sidebar.width()
                    
                    if expected_setting:
                        if is_visible and width == 5:
                            print("   ✅ البار مفعل ومخفي بشكل صحيح")
                        else:
                            print(f"   ❌ البار مفعل لكن في حالة خاطئة: مرئي={is_visible}, عرض={width}")
                    else:
                        if not is_visible or width == 0:
                            print("   ✅ البار مطفي ومخفي تماماً")
                        else:
                            print(f"   ❌ البار مطفي لكن لا يزال مرئي: مرئي={is_visible}, عرض={width}")
        
        # عرض النتائج النهائية
        show_final_results(main_window)
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من الإعدادات: {e}")

def show_final_results(main_window):
    """عرض النتائج النهائية"""
    print("\n🎉 انتهاء الاختبار!")
    
    QMessageBox.information(
        main_window,
        "نتائج اختبار إصلاح البار الجانبي",
        "✅ تم اختبار إصلاح البار الجانبي!\n\n"
        "🔧 ما تم اختباره:\n"
        "• سلوك البار عند تمرير الماوس\n"
        "• تطبيق الإعدادات على التبويبات\n"
        "• تغيير الإعدادات وتطبيقها فوراً\n"
        "• التأكد من عدم ظهور البار عند إطفاؤه\n\n"
        "🎯 جرب الآن:\n"
        "1. اذهب لإعدادات النظام وغير إعداد البار\n"
        "2. انتقل بين التبويبات المختلفة\n"
        "3. مرر الماوس على البار الجانبي\n"
        "4. تأكد من أن البار يتصرف حسب الإعدادات"
    )

if __name__ == "__main__":
    sys.exit(test_sidebar_fix())
