#!/usr/bin/env python3
"""
اختبار التطبيق مع إعدادات DPI مختلفة
محاكاة الأجهزة المختلفة على نفس الجهاز
"""

import os
import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


def test_dpi_scaling(scale_factor):
    """اختبار التطبيق مع عامل تكبير محدد"""
    print(f"🧪 اختبار عامل التكبير: {scale_factor}")
    
    # تعيين متغيرات البيئة لمحاكاة DPI مختلف
    os.environ['QT_SCALE_FACTOR'] = str(scale_factor)
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
    
    try:
        # إنشاء تطبيق اختبار
        app = QApplication(sys.argv)
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # اختبار مدير DPI
        from utils.dpi_manager import dpi_manager
        
        print(f"📊 معلومات الاختبار:")
        print(f"   • عامل التكبير المحاكي: {scale_factor}")
        print(f"   • عامل DPI المكتشف: {dpi_manager.dpi_scale:.2f}")
        print(f"   • عامل تكبير الخط: {dpi_manager.font_scale:.2f}")
        print(f"   • شاشة عالية الدقة: {'نعم' if dpi_manager.is_high_dpi else 'لا'}")
        
        # اختبار حجم العناصر المنبثقة
        popup_size = dpi_manager.get_safe_popup_size()
        print(f"   • حجم العنصر المنبثق: {popup_size.width()}x{popup_size.height()}")
        
        # اختبار حجم الخط
        font_size = dpi_manager.get_scaled_font_size(14)
        print(f"   • حجم الخط (14 أساسي): {font_size}px")
        
        # إنشاء رسالة اختبار
        msg = QMessageBox()
        msg.setWindowTitle(f"اختبار DPI - عامل التكبير {scale_factor}")
        msg.setText(f"""
🧪 اختبار عامل التكبير: {scale_factor}

📊 النتائج:
• عامل DPI: {dpi_manager.dpi_scale:.2f}
• تكبير الخط: {dpi_manager.font_scale:.2f}
• حجم العنصر المنبثق: {popup_size.width()}x{popup_size.height()}
• حجم الخط: {font_size}px

هل تبدو الأحجام مناسبة؟
        """)
        
        # تطبيق DPI على الرسالة
        dpi_manager.setup_widget_dpi(msg)
        
        result = msg.exec_()
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False


def test_all_dpi_scenarios():
    """اختبار جميع سيناريوهات DPI المختلفة"""
    print("🚀 بدء اختبار جميع سيناريوهات DPI...")
    print("=" * 50)
    
    # سيناريوهات DPI مختلفة (محاكاة أجهزة مختلفة)
    test_scenarios = [
        (1.0, "شاشة عادية 96 DPI (1920x1080)"),
        (1.25, "شاشة متوسطة 120 DPI (1920x1080 مع تكبير 125%)"),
        (1.5, "شاشة عالية الدقة 144 DPI (2560x1440)"),
        (2.0, "شاشة 4K 192 DPI (3840x2160)"),
        (0.75, "شاشة صغيرة 72 DPI (1366x768)")
    ]
    
    results = []
    
    for scale_factor, description in test_scenarios:
        print(f"\n🖥️ {description}")
        print("-" * 30)
        
        try:
            success = test_dpi_scaling(scale_factor)
            results.append((scale_factor, description, success))
            
            if success:
                print(f"✅ نجح الاختبار")
            else:
                print(f"❌ فشل الاختبار")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((scale_factor, description, False))
    
    # تقرير النتائج
    print("\n" + "=" * 50)
    print("📊 تقرير نتائج الاختبار:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for scale_factor, description, success in results:
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{status} | {scale_factor:4.2f}x | {description}")
        
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 الملخص:")
    print(f"   • نجح: {passed} اختبار")
    print(f"   • فشل: {failed} اختبار")
    print(f"   • معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ممتاز! جميع الاختبارات نجحت")
        print("   التطبيق سيعمل بشكل صحيح على جميع الأجهزة")
    else:
        print(f"\n⚠️ تحذير: {failed} اختبار فشل")
        print("   قد تحتاج لتحسينات إضافية")


def create_dpi_test_report():
    """إنشاء تقرير اختبار DPI"""
    import json
    from datetime import datetime
    
    report = {
        "test_date": datetime.now().isoformat(),
        "test_type": "DPI Compatibility Test",
        "current_system": {
            "dpi": 144,
            "scale_factor": 1.5,
            "resolution": "2560x1600"
        },
        "test_scenarios": [
            {"scale": 1.0, "description": "Standard 96 DPI", "typical_devices": ["Desktop 1920x1080", "Laptop 1366x768"]},
            {"scale": 1.25, "description": "Medium 120 DPI", "typical_devices": ["Laptop 1920x1080 125%", "Desktop 1440p 125%"]},
            {"scale": 1.5, "description": "High 144 DPI", "typical_devices": ["Laptop 2560x1440", "Desktop 1440p 150%"]},
            {"scale": 2.0, "description": "Ultra High 192 DPI", "typical_devices": ["4K Laptop", "4K Desktop 200%"]},
            {"scale": 0.75, "description": "Low 72 DPI", "typical_devices": ["Old monitors", "Small laptops"]}
        ],
        "fixes_applied": [
            "DPI awareness enabled",
            "Responsive popup sizing",
            "Font scaling",
            "Safe positioning",
            "Screen-aware calculations"
        ]
    }
    
    with open("dpi_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("📄 تم إنشاء تقرير الاختبار: dpi_test_report.json")


if __name__ == "__main__":
    print("🧪 أداة اختبار توافق DPI")
    print("تحاكي أجهزة مختلفة لاختبار الإصلاحات")
    print("=" * 50)
    
    # إنشاء تقرير الاختبار
    create_dpi_test_report()
    
    # تشغيل الاختبارات
    test_all_dpi_scenarios()
    
    print("\n💡 نصائح للاختبار الإضافي:")
    print("   1. اختبر على أجهزة حقيقية مختلفة")
    print("   2. غير إعدادات Windows Scale")
    print("   3. اختبر على دقات شاشة مختلفة")
    print("   4. اطلب من المستخدمين اختبار التطبيق")
