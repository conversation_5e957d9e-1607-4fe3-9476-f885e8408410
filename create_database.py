#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إنشاء قاعدة البيانات وتهيئتها
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

def create_database():
    """إنشاء قاعدة البيانات وجميع الجداول"""
    print("🗄️ بدء إنشاء قاعدة البيانات...")
    
    try:
        # استيراد النماذج
        from database.models import Base
        from database.users import User
        from database.settings_models import AppSetting
        
        # إنشاء محرك قاعدة البيانات
        engine = create_engine('sqlite:///accounting.db', echo=True)
        
        print("📋 إنشاء جميع الجداول...")
        
        # إنشاء جميع الجداول
        Base.metadata.create_all(engine)
        
        print("✅ تم إنشاء جميع الجداول بنجاح!")
        
        # إنشاء جلسة
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # إضافة المستخدم الافتراضي
        print("👤 إضافة المستخدم الافتراضي...")
        
        # التحقق من وجود المستخدم
        existing_user = session.query(User).filter_by(username='sicoo').first()
        if not existing_user:
            default_user = User(
                username='sicoo',
                full_name='مدير النظام',
                role='admin'
            )
            default_user.set_password('sicoo123')
            session.add(default_user)
            print("✅ تم إضافة المستخدم الافتراضي: sicoo / sicoo123")
        else:
            print("ℹ️ المستخدم الافتراضي موجود مسبقاً")
        
        # إضافة الإعدادات الافتراضية
        print("⚙️ إضافة الإعدادات الافتراضية...")
        
        default_settings = [
            ('sidebar_enabled', 'false', 'boolean', 'appearance', 'تفعيل البار الجانبي'),
            ('theme', 'modern', 'string', 'appearance', 'ثيم التطبيق'),
            ('language', 'ar', 'string', 'general', 'لغة التطبيق'),
            ('auto_backup', 'true', 'boolean', 'general', 'النسخ الاحتياطي التلقائي'),
            ('window_maximized', 'true', 'boolean', 'appearance', 'فتح النوافذ بحجم كامل'),
        ]
        
        for key, value, setting_type, category, description in default_settings:
            existing_setting = session.query(AppSetting).filter_by(setting_key=key).first()
            if not existing_setting:
                setting = AppSetting(
                    setting_key=key,
                    setting_value=value,
                    setting_type=setting_type,
                    category=category,
                    description=description
                )
                session.add(setting)
                print(f"✅ تم إضافة إعداد: {key} = {value}")
            else:
                print(f"ℹ️ الإعداد موجود مسبقاً: {key}")
        
        # إضافة بيانات تجريبية للمنتجات
        print("📦 إضافة منتجات تجريبية...")
        
        from database.models import Product
        
        sample_products = [
            {
                'code': 'P001',
                'name': 'منتج تجريبي 1',
                'description': 'وصف المنتج التجريبي الأول',
                'category': 'فئة تجريبية',
                'purchase_price': 100.0,
                'sale_price': 150.0,
                'min_sale_price': 120.0,
                'quantity': 50,
                'min_quantity': 10,
                'unit': 'قطعة',
                'barcode': '1234567890123'
            },
            {
                'code': 'P002',
                'name': 'منتج تجريبي 2',
                'description': 'وصف المنتج التجريبي الثاني',
                'category': 'فئة تجريبية',
                'purchase_price': 200.0,
                'sale_price': 300.0,
                'min_sale_price': 250.0,
                'quantity': 30,
                'min_quantity': 5,
                'unit': 'كيلو',
                'barcode': '1234567890124'
            },
            {
                'code': 'P003',
                'name': 'منتج تجريبي 3',
                'description': 'وصف المنتج التجريبي الثالث',
                'category': 'فئة أخرى',
                'purchase_price': 50.0,
                'sale_price': 80.0,
                'min_sale_price': 60.0,
                'quantity': 100,
                'min_quantity': 20,
                'unit': 'علبة',
                'barcode': '1234567890125'
            }
        ]
        
        for product_data in sample_products:
            existing_product = session.query(Product).filter_by(code=product_data['code']).first()
            if not existing_product:
                product = Product(**product_data)
                session.add(product)
                print(f"✅ تم إضافة منتج: {product_data['name']}")
            else:
                print(f"ℹ️ المنتج موجود مسبقاً: {product_data['name']}")
        
        # حفظ التغييرات
        session.commit()
        session.close()
        
        print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
        print("📋 ملخص ما تم إنشاؤه:")
        print("✅ جميع الجداول")
        print("✅ المستخدم الافتراضي: sicoo / sicoo123")
        print("✅ الإعدادات الافتراضية")
        print("✅ منتجات تجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def check_database():
    """فحص قاعدة البيانات والتحقق من الجداول"""
    print("🔍 فحص قاعدة البيانات...")
    
    try:
        from sqlalchemy import create_engine, inspect
        
        engine = create_engine('sqlite:///accounting.db')
        inspector = inspect(engine)
        
        tables = inspector.get_table_names()
        print(f"📋 عدد الجداول الموجودة: {len(tables)}")
        
        required_tables = [
            'products', 'customers', 'suppliers', 'transactions', 
            'transaction_items', 'users', 'app_settings'
        ]
        
        missing_tables = []
        for table in required_tables:
            if table in tables:
                print(f"✅ جدول {table}: موجود")
            else:
                print(f"❌ جدول {table}: مفقود")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"\n⚠️ جداول مفقودة: {missing_tables}")
            return False
        else:
            print("\n✅ جميع الجداول المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🗄️ أداة إنشاء وفحص قاعدة البيانات")
    print("=" * 50)
    
    # فحص وجود قاعدة البيانات
    if os.path.exists('accounting.db'):
        print("📁 قاعدة البيانات موجودة، فحص الجداول...")
        if check_database():
            print("✅ قاعدة البيانات سليمة!")
            return
        else:
            print("⚠️ قاعدة البيانات تحتاج إعادة إنشاء...")
    else:
        print("📁 قاعدة البيانات غير موجودة")
    
    # إنشاء قاعدة البيانات
    if create_database():
        print("\n🔍 فحص نهائي...")
        if check_database():
            print("🎉 قاعدة البيانات جاهزة للاستخدام!")
        else:
            print("❌ مشكلة في قاعدة البيانات")
    else:
        print("❌ فشل في إنشاء قاعدة البيانات")

if __name__ == "__main__":
    main()
