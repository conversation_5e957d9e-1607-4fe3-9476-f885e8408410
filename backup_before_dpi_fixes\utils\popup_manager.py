"""
مدير العناصر المنبثقة المتجاوب
يدير أحجام ومواضع العناصر المنبثقة لتجنب التداخل على الشاشات المختلفة
"""

from PyQt5.QtWidgets import QApplication, QCompleter, QListView, QWidget
from PyQt5.QtCore import QRect, QPoint, QSize
from PyQt5.QtGui import QScreen


class PopupManager:
    """مدير العناصر المنبثقة المتجاوب"""
    
    def __init__(self):
        self.screen_info = self._get_screen_info()
        
    def _get_screen_info(self):
        """الحصول على معلومات الشاشة"""
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                geometry = screen.geometry()
                return {
                    'width': geometry.width(),
                    'height': geometry.height(),
                    'dpi': screen.logicalDotsPerInch(),
                    'scale_factor': screen.devicePixelRatio()
                }
        except:
            pass
        
        # قيم افتراضية آمنة
        return {
            'width': 1366,
            'height': 768,
            'dpi': 96,
            'scale_factor': 1.0
        }
    
    def get_responsive_popup_size(self, item_count=5, base_width=300, base_height=200):
        """حساب حجم العنصر المنبثق المناسب للشاشة"""
        screen_width = self.screen_info['width']
        screen_height = self.screen_info['height']
        scale_factor = self.screen_info['scale_factor']
        
        # حساب الحد الأقصى الآمن (لا يتجاوز 40% من الشاشة)
        max_width = int(screen_width * 0.4)
        max_height = int(screen_height * 0.3)
        
        # حساب الحد الأدنى المناسب
        min_width = max(250, int(screen_width * 0.15))
        min_height = max(150, int(screen_height * 0.1))
        
        # تطبيق عامل التكبير
        adjusted_base_width = int(base_width * scale_factor)
        adjusted_base_height = int(base_height * scale_factor)
        
        # حساب الحجم النهائي
        final_width = max(min_width, min(adjusted_base_width, max_width))
        final_height = max(min_height, min(adjusted_base_height, max_height))
        
        # تعديل الارتفاع حسب عدد العناصر
        if item_count > 0:
            item_height = max(25, int(20 * scale_factor))
            calculated_height = min_height + (min(item_count, 8) * item_height)
            final_height = min(calculated_height, max_height)
        
        return QSize(final_width, final_height)
    
    def get_responsive_font_size(self, base_size=14):
        """حساب حجم الخط المناسب للشاشة"""
        screen_width = self.screen_info['width']
        scale_factor = self.screen_info['scale_factor']
        
        # حساب حجم الخط المتجاوب
        if screen_width <= 1366:
            font_size = max(10, int(base_size * 0.85))
        elif screen_width <= 1920:
            font_size = base_size
        else:
            font_size = min(18, int(base_size * 1.2))
        
        return int(font_size * scale_factor)
    
    def setup_completer_popup(self, completer, max_items=6):
        """إعداد العنصر المنبثق للـ QCompleter"""
        if not isinstance(completer, QCompleter):
            return
        
        popup = completer.popup()
        if not popup:
            return
        
        # حساب الأحجام المتجاوبة
        popup_size = self.get_responsive_popup_size(max_items)
        font_size = self.get_responsive_font_size()
        
        # تطبيق الأحجام
        popup.setMinimumSize(popup_size.width() - 50, popup_size.height() - 50)
        popup.setMaximumSize(popup_size.width() + 50, popup_size.height() + 50)
        
        # تحديد عدد العناصر المرئية
        completer.setMaxVisibleItems(min(max_items, 8))
        
        # تطبيق الأنماط المتجاوبة
        popup.setStyleSheet(f"""
            QListView {{
                background-color: white;
                border: 2px solid #3498DB;
                border-radius: 8px;
                font-size: {font_size}px;
                padding: 4px;
                selection-background-color: #3498DB;
                selection-color: white;
                outline: none;
            }}
            QListView::item {{
                padding: 6px 10px;
                border-bottom: 1px solid #ECF0F1;
                min-height: {max(20, font_size + 8)}px;
                max-height: {max(30, font_size + 15)}px;
            }}
            QListView::item:hover {{
                background-color: #EBF3FD;
                border-radius: 4px;
            }}
            QListView::item:selected {{
                background-color: #3498DB;
                color: white;
                border-radius: 4px;
            }}
        """)
    
    def position_popup_safely(self, popup_widget, parent_widget):
        """وضع العنصر المنبثق في موضع آمن لتجنب التداخل"""
        if not popup_widget or not parent_widget:
            return
        
        try:
            # الحصول على موضع وحجم الوالد
            parent_rect = parent_widget.geometry()
            parent_global_pos = parent_widget.mapToGlobal(QPoint(0, 0))
            
            # حساب الموضع المقترح (تحت الوالد)
            suggested_x = parent_global_pos.x()
            suggested_y = parent_global_pos.y() + parent_rect.height()
            
            # الحصول على حجم العنصر المنبثق
            popup_size = popup_widget.size()
            
            # التحقق من حدود الشاشة
            screen_rect = QApplication.desktop().screenGeometry()
            
            # تعديل الموضع إذا كان يتجاوز حدود الشاشة
            if suggested_x + popup_size.width() > screen_rect.right():
                suggested_x = screen_rect.right() - popup_size.width() - 10
            
            if suggested_y + popup_size.height() > screen_rect.bottom():
                # عرض العنصر فوق الوالد بدلاً من تحته
                suggested_y = parent_global_pos.y() - popup_size.height()
            
            # التأكد من أن الموضع داخل حدود الشاشة
            suggested_x = max(10, min(suggested_x, screen_rect.right() - popup_size.width() - 10))
            suggested_y = max(10, min(suggested_y, screen_rect.bottom() - popup_size.height() - 10))
            
            # تطبيق الموضع الجديد
            popup_widget.move(suggested_x, suggested_y)
            
        except Exception as e:
            print(f"خطأ في وضع العنصر المنبثق: {e}")
    
    def get_safe_popup_geometry(self, parent_widget, popup_size):
        """حساب الموضع والحجم الآمن للعنصر المنبثق"""
        try:
            if not parent_widget:
                return QRect(100, 100, popup_size.width(), popup_size.height())
            
            # الحصول على موضع الوالد
            parent_global_pos = parent_widget.mapToGlobal(QPoint(0, 0))
            parent_rect = parent_widget.geometry()
            
            # حساب الموضع المقترح
            x = parent_global_pos.x()
            y = parent_global_pos.y() + parent_rect.height()
            
            # الحصول على حدود الشاشة
            screen_rect = QApplication.desktop().screenGeometry()
            
            # تعديل الحجم إذا كان كبيراً جداً
            max_width = screen_rect.width() - 100
            max_height = screen_rect.height() - 100
            
            width = min(popup_size.width(), max_width)
            height = min(popup_size.height(), max_height)
            
            # تعديل الموضع إذا كان يتجاوز الحدود
            if x + width > screen_rect.right():
                x = screen_rect.right() - width - 20
            
            if y + height > screen_rect.bottom():
                y = parent_global_pos.y() - height
            
            # التأكد من أن الموضع داخل الحدود
            x = max(20, min(x, screen_rect.right() - width - 20))
            y = max(20, min(y, screen_rect.bottom() - height - 20))
            
            return QRect(x, y, width, height)
            
        except Exception as e:
            print(f"خطأ في حساب موضع العنصر المنبثق: {e}")
            return QRect(100, 100, popup_size.width(), popup_size.height())


# إنشاء مثيل عام للاستخدام
popup_manager = PopupManager()
