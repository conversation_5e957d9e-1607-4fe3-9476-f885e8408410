#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إعدادات البار الجانبي الجديدة
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_sidebar_settings():
    """اختبار إعدادات البار الجانبي"""
    print("🧪 بدء اختبار إعدادات البار الجانبي...")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    try:
        # اختبار تحميل الإعدادات
        print("\n1️⃣ اختبار تحميل الإعدادات...")
        settings_file = 'user_settings.json'
        
        # إنشاء إعدادات تجريبية
        test_settings = {
            'sidebar_enabled': True,
            'theme': 'modern',
            'language': 'ar'
        }
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=2)
        print("✅ تم إنشاء ملف الإعدادات التجريبي")
        
        # اختبار تحميل الإعدادات
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                loaded_settings = json.load(f)
                sidebar_enabled = loaded_settings.get('sidebar_enabled', False)
                print(f"✅ تم تحميل الإعدادات: البار الجانبي {'مفعل' if sidebar_enabled else 'مطفي'}")
        
        # اختبار نافذة الإعدادات
        print("\n2️⃣ اختبار نافذة إعدادات الشركة...")
        from utils.company_settings import CompanySettingsDialog
        
        settings_dialog = CompanySettingsDialog()
        print("✅ تم إنشاء نافذة الإعدادات بنجاح")
        
        # التحقق من وجود تبويب إعدادات الواجهة
        if hasattr(settings_dialog, 'tabs'):
            tab_count = settings_dialog.tabs.count()
            print(f"✅ عدد التبويبات: {tab_count}")
            
            # التحقق من وجود checkbox البار الجانبي
            if hasattr(settings_dialog, 'sidebar_enabled_checkbox'):
                print("✅ تم العثور على خيار تفعيل البار الجانبي")
                current_state = settings_dialog.sidebar_enabled_checkbox.isChecked()
                print(f"✅ الحالة الحالية: {'مفعل' if current_state else 'مطفي'}")
            else:
                print("❌ لم يتم العثور على خيار تفعيل البار الجانبي")
        
        # اختبار النافذة الرئيسية
        print("\n3️⃣ اختبار النافذة الرئيسية...")
        
        # محاكاة قاعدة البيانات
        from sqlalchemy import create_engine
        engine = create_engine('sqlite:///test_settings.db', echo=False)
        
        # إنشاء مستخدم تجريبي
        class TestUser:
            def __init__(self):
                self.full_name = "مستخدم تجريبي"
                self.username = "test"
                self.role = "admin"
        
        test_user = TestUser()
        
        # إنشاء النافذة الرئيسية
        from gui.main_window import MainWindow
        main_window = MainWindow(engine=engine, user=test_user)
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # التحقق من إعدادات البار الجانبي
        if hasattr(main_window, 'sidebar_enabled'):
            print(f"✅ إعداد البار الجانبي: {'مفعل' if main_window.sidebar_enabled else 'مطفي'}")
        
        if hasattr(main_window, 'sidebar_shortcut'):
            print("✅ تم إعداد اختصار البار الجانبي (Ctrl+B)")
        
        # اختبار الاختصار
        print("\n4️⃣ اختبار الاختصار...")
        if hasattr(main_window, 'toggle_sidebar_visibility'):
            print("✅ دالة تبديل البار الجانبي متوفرة")
        
        # عرض النافذة للاختبار البصري
        main_window.show()
        
        # رسالة نجاح
        QMessageBox.information(
            main_window,
            "نجح الاختبار! 🎉",
            "✅ تم اختبار جميع مكونات البار الجانبي بنجاح!\n\n"
            "🔧 المميزات المتوفرة:\n"
            "• إعداد تفعيل/إلغاء تفعيل البار الجانبي\n"
            "• اختصار Ctrl+B للتبديل\n"
            "• حفظ الإعدادات تلقائياً\n"
            "• تبويب منفصل في إعدادات الشركة\n\n"
            "🎯 جرب الآن:\n"
            "1. اضغط Ctrl+B لتبديل البار\n"
            "2. اذهب لإعدادات الشركة ← إعدادات الواجهة\n"
            "3. فعل/ألغي تفعيل البار الجانبي"
        )
        
        print("\n🎉 تم الانتهاء من الاختبار بنجاح!")
        print("📋 التقرير النهائي:")
        print("✅ تحميل الإعدادات: نجح")
        print("✅ نافذة الإعدادات: نجح")
        print("✅ النافذة الرئيسية: نجح")
        print("✅ اختصار لوحة المفاتيح: نجح")
        print("✅ حفظ الإعدادات: نجح")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        QMessageBox.critical(None, "خطأ في الاختبار", f"حدث خطأ:\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(test_sidebar_settings())
