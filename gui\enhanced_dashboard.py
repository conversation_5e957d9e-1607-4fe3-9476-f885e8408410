#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة المعلومات المحسنة - بدون matplotlib
Enhanced Dashboard - Without matplotlib
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFrame,                           QLabel, QPushButton, QGridLayout, QMessageBox,
                           QScrollArea, QGroupBox)
from utils.dpi_manager import dpi_manager
from utils.responsive_layout_manager import responsive_manager
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QPainter, QColor, QFont, QLinearGradient
from PyQt5.QtChart import (QChart, QChartView, QPieSeries, QBarSeries, QBarSet, 
                          QBarCategoryAxis, QValueAxis, QLineSeries, QAreaSeries)
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from datetime import datetime, timedelta
from database.models import Transaction, Product, Customer, Supplier, TransactionType, TransactionItem
from utils.theme_manager import theme_manager
from utils.currency_formatter import format_currency, format_number


class DashboardDataLoader(QThread):
    """فئة لتحميل بيانات لوحة المعلومات في خيط منفصل"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, engine):
        super().__init__()
        # إعداد DPI للواجهة
        try:
            dpi_manager.setup_widget_dpi(self)
        except:
            pass
        self.engine = engine
        
    def run(self):
        try:
            data = self.load_dashboard_data()
            self.data_loaded.emit(data)
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def load_dashboard_data(self):
        """تحميل جميع بيانات لوحة المعلومات"""
        with Session(self.engine) as session:
            today = datetime.now().date()
            week_ago = today - timedelta(days=7)
            
            # المبيعات اليوم
            today_sales = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.SALE,
                func.date(Transaction.date) == today
            ).scalar() or 0
            
            # المشتريات اليوم
            today_purchases = session.query(func.sum(Transaction.total_amount)).filter(
                Transaction.type == TransactionType.PURCHASE,
                func.date(Transaction.date) == today
            ).scalar() or 0
            
            # عدد العملاء
            customers_count = session.query(func.count(Customer.id)).filter(
                Customer.is_active == True
            ).scalar() or 0
            
            # عدد المنتجات
            products_count = session.query(func.count(Product.id)).filter(
                Product.is_active == True
            ).scalar() or 0
            
            # المنتجات منخفضة المخزون
            low_stock_count = session.query(func.count(Product.id)).filter(
                Product.quantity <= Product.min_quantity,
                Product.is_active == True
            ).scalar() or 0
            
            # المبيعات آخر 7 أيام
            weekly_sales = []
            for i in range(7):
                date = today - timedelta(days=i)
                sales = session.query(func.sum(Transaction.total_amount)).filter(
                    Transaction.type == TransactionType.SALE,
                    func.date(Transaction.date) == date
                ).scalar() or 0
                weekly_sales.append((date.strftime('%m-%d'), float(sales)))
            
            # أفضل المنتجات مبيعاً
            top_products = session.query(
                Product.name,
                func.sum(TransactionItem.quantity).label('total_quantity')
            ).join(TransactionItem).join(Transaction).filter(
                Transaction.type == TransactionType.SALE,
                Transaction.date >= week_ago
            ).group_by(Product.id).order_by(desc('total_quantity')).limit(5).all()
            
            return {
                'today_sales': float(today_sales),
                'today_purchases': float(today_purchases),
                'customers_count': customers_count,
                'products_count': products_count,
                'low_stock_count': low_stock_count,
                'weekly_sales': weekly_sales,
                'top_products': [(p.name, float(p.total_quantity)) for p in top_products]
            }


class StatCard(QFrame):
    """بطاقة إحصائية محسنة"""
    
    def __init__(self, title, value, icon, color):
        super().__init__()
        # إعداد DPI للواجهة
        try:
            dpi_manager.setup_widget_dpi(self)
        except:
            pass
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFixedSize(200, 120)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setStyleSheet("color: white; font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        header_layout.addStretch()
        
        title_label = QLabel(self.title)
        title_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignRight)
        header_layout.addWidget(title_label)
        
        layout.addLayout(header_layout)
        
        # القيمة
        self.value_label = QLabel(str(self.value))
        self.value_label.setStyleSheet("color: white; font-size: 28px; font-weight: bold;")
        self.value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.value_label)
        
        layout.addStretch()
    
    def update_value(self, new_value):
        """تحديث القيمة"""
        self.value = new_value
        if isinstance(new_value, float) and new_value > 1000:
            self.value_label.setText(format_currency(new_value))
        else:
            self.value_label.setText(str(new_value))
    
    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#28a745": "#1e7e34",
            "#dc3545": "#bd2130",
            "#007bff": "#0056b3",
            "#ffc107": "#e0a800",
            "#17a2b8": "#117a8b"
        }
        return color_map.get(color, color)


class EnhancedDashboardWidget(QWidget):
    """لوحة المعلومات المحسنة"""
    
    def __init__(self, engine):
        super().__init__()
        # إعداد DPI للواجهة
        try:
            dpi_manager.setup_widget_dpi(self)
        except:
            pass
        self.engine = engine
        self.data_loader = None
        self.stat_cards = {}
        self.charts = {}
        self.setup_ui()

        # تطبيق التخطيط المتجاوب بعد إنشاء الواجهة
        responsive_manager.setup_responsive_dashboard(self)

        self.start_auto_refresh()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # العنوان الرئيسي
        title_frame = self.create_title_frame()
        layout.addWidget(title_frame)

        # منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_widget.setLayout(scroll_layout)

        # بطاقات الإحصائيات
        stats_frame = self.create_stats_frame()
        scroll_layout.addWidget(stats_frame)

        # الرسوم البيانية
        charts_frame = self.create_charts_frame()
        scroll_layout.addWidget(charts_frame)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        # تحميل البيانات الأولي
        QTimer.singleShot(100, self.load_data)

    def create_title_frame(self):
        """إنشاء إطار العنوان"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #2C3E50, stop:1 #3498DB);
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        
        layout = QHBoxLayout()
        frame.setLayout(layout)

        welcome_label = QLabel("📊 لوحة المعلومات")
        welcome_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold;")
        
        date_label = QLabel(datetime.now().strftime("%Y-%m-%d %H:%M"))
        date_label.setStyleSheet("color: white; font-size: 16px;")

        layout.addWidget(welcome_label)
        layout.addStretch()
        layout.addWidget(date_label)

        return frame

    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout()
        frame.setLayout(layout)
        
        title = QLabel("📈 الإحصائيات السريعة")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # شبكة البطاقات
        cards_layout = QGridLayout()
        
        # إنشاء البطاقات
        self.stat_cards['sales'] = StatCard("مبيعات اليوم", "0", "💰", "#28a745")
        self.stat_cards['purchases'] = StatCard("مشتريات اليوم", "0", "🛒", "#dc3545")
        self.stat_cards['customers'] = StatCard("العملاء", "0", "👥", "#007bff")
        self.stat_cards['products'] = StatCard("المنتجات", "0", "📦", "#ffc107")
        self.stat_cards['low_stock'] = StatCard("تحذيرات المخزون", "0", "⚠️", "#17a2b8")

        # ترتيب البطاقات
        cards = list(self.stat_cards.values())
        for i, card in enumerate(cards):
            row = i // 3
            col = i % 3
            cards_layout.addWidget(card, row, col)

        layout.addLayout(cards_layout)
        return frame

    def create_charts_frame(self):
        """إنشاء إطار الرسوم البيانية"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout()
        frame.setLayout(layout)
        
        title = QLabel("📊 الرسوم البيانية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تخطيط الرسوم البيانية
        charts_layout = QHBoxLayout()
        
        # رسم المبيعات الأسبوعية
        self.weekly_sales_chart = self.create_weekly_sales_chart()
        charts_layout.addWidget(self.weekly_sales_chart)
        
        # رسم أفضل المنتجات
        self.top_products_chart = self.create_top_products_chart()
        charts_layout.addWidget(self.top_products_chart)

        layout.addLayout(charts_layout)
        return frame

    def create_weekly_sales_chart(self):
        """إنشاء رسم المبيعات الأسبوعية"""
        chart = QChart()
        chart.setTitle("📈 المبيعات آخر 7 أيام")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة خطية
        series = QLineSeries()
        series.setName("المبيعات")

        chart.addSeries(series)

        # إعداد المحاور
        axis_x = QBarCategoryAxis()
        axis_y = QValueAxis()

        chart.addAxis(axis_x, Qt.AlignBottom)
        chart.addAxis(axis_y, Qt.AlignLeft)

        series.attachAxis(axis_x)
        series.attachAxis(axis_y)

        # حفظ المراجع
        self.charts['weekly_sales'] = {
            'chart': chart,
            'series': series,
            'axis_x': axis_x,
            'axis_y': axis_y
        }

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        chart_view.setMinimumHeight(300)
        return chart_view

    def create_top_products_chart(self):
        """إنشاء رسم أفضل المنتجات"""
        chart = QChart()
        chart.setTitle("🏆 أفضل المنتجات مبيعاً")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # سلسلة دائرية
        series = QPieSeries()
        chart.addSeries(series)

        # حفظ المراجع
        self.charts['top_products'] = {
            'chart': chart,
            'series': series
        }

        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        chart_view.setMinimumHeight(300)
        return chart_view

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_data)
        self.refresh_timer.start(300000)  # كل 5 دقائق

    def load_data(self):
        """تحميل البيانات"""
        if self.data_loader and self.data_loader.isRunning():
            return

        self.data_loader = DashboardDataLoader(self.engine)
        self.data_loader.data_loaded.connect(self.on_data_loaded)
        self.data_loader.error_occurred.connect(self.on_data_error)
        self.data_loader.start()

    def on_data_loaded(self, data):
        """عند تحميل البيانات بنجاح"""
        self.update_stat_cards(data)
        self.update_charts(data)

    def on_data_error(self, error_message):
        """عند حدوث خطأ في تحميل البيانات"""
        print(f"خطأ في تحميل بيانات لوحة المعلومات: {error_message}")

    def update_stat_cards(self, data):
        """تحديث بطاقات الإحصائيات"""
        self.stat_cards['sales'].update_value(data.get('today_sales', 0))
        self.stat_cards['purchases'].update_value(data.get('today_purchases', 0))
        self.stat_cards['customers'].update_value(data.get('customers_count', 0))
        self.stat_cards['products'].update_value(data.get('products_count', 0))
        self.stat_cards['low_stock'].update_value(data.get('low_stock_count', 0))

    def update_charts(self, data):
        """تحديث الرسوم البيانية"""
        self.update_weekly_sales_chart(data)
        self.update_top_products_chart(data)

    def update_weekly_sales_chart(self, data):
        """تحديث رسم المبيعات الأسبوعية"""
        if 'weekly_sales' not in self.charts:
            return

        chart_data = self.charts['weekly_sales']
        series = chart_data['series']

        # مسح البيانات السابقة
        series.clear()

        # إضافة البيانات الجديدة
        weekly_sales = data.get('weekly_sales', [])
        dates = []
        values = []

        for date, value in weekly_sales:
            dates.append(date)
            values.append(value)
            series.append(len(values) - 1, value)

        # تحديث المحاور
        chart_data['axis_x'].clear()
        chart_data['axis_x'].append(dates)

        if values:
            max_value = max(values)
            chart_data['axis_y'].setRange(0, max_value * 1.1)

    def update_top_products_chart(self, data):
        """تحديث رسم أفضل المنتجات"""
        if 'top_products' not in self.charts:
            return

        chart_data = self.charts['top_products']
        series = chart_data['series']

        # مسح البيانات السابقة
        series.clear()

        # إضافة البيانات الجديدة
        top_products = data.get('top_products', [])
        colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7"]

        for i, (name, quantity) in enumerate(top_products):
            slice_obj = series.append(name, quantity)
            if i < len(colors):
                slice_obj.setColor(QColor(colors[i]))

    def refresh_data(self):
        """تحديث البيانات يدوياً"""
        self.load_data()

    def closeEvent(self, event):
        """عند إغلاق الودجت"""
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()

        if self.data_loader and self.data_loader.isRunning():
            self.data_loader.quit()
            self.data_loader.wait()

        event.accept()
