"""
مدير DPI والتكبير الذكي
يحل مشاكل التداخل والأحجام على جميع الأجهزة والشاشات
"""

import sys
import os
from PyQt5.QtWidgets import QApp<PERSON>, QWidget, QCompleter, QDialog
from PyQt5.QtCore import Qt, QSize, QRect, QPoint
from PyQt5.QtGui import QFont, QFontMetrics, QScreen
import ctypes
from ctypes import wintypes


class DPIManager:
    """مدير DPI والتكبير الذكي للتطبيق"""
    
    def __init__(self):
        self.dpi_scale = 1.0
        self.screen_info = {}
        self.font_scale = 1.0
        self.is_high_dpi = False
        self._detect_system_settings()
    
    def _detect_system_settings(self):
        """اكتشاف إعدادات النظام"""
        try:
            # اكتشاف Windows DPI Scaling
            if sys.platform == "win32":
                self._detect_windows_dpi()
            
            # اكتشاف معلومات الشاشة
            self._detect_screen_info()
            
            # حساب عوامل التكبير
            self._calculate_scaling_factors()
            
        except Exception as e:
            print(f"تحذير: فشل في اكتشاف إعدادات النظام: {e}")
            self._set_default_values()
    
    def _detect_windows_dpi(self):
        """اكتشاف إعدادات DPI في Windows"""
        try:
            # الحصول على DPI الحالي
            user32 = ctypes.windll.user32
            user32.SetProcessDPIAware()
            
            # الحصول على DPI للشاشة الرئيسية
            hdc = user32.GetDC(0)
            dpi_x = ctypes.windll.gdi32.GetDeviceCaps(hdc, 88)  # LOGPIXELSX
            dpi_y = ctypes.windll.gdi32.GetDeviceCaps(hdc, 90)  # LOGPIXELSY
            user32.ReleaseDC(0, hdc)
            
            # حساب عامل التكبير (96 DPI = 100%)
            self.dpi_scale = max(dpi_x, dpi_y) / 96.0
            self.is_high_dpi = self.dpi_scale > 1.25
            
            print(f"🖥️ DPI المكتشف: {max(dpi_x, dpi_y)}, عامل التكبير: {self.dpi_scale:.2f}")
            
        except Exception as e:
            print(f"تحذير: فشل في اكتشاف DPI: {e}")
            self.dpi_scale = 1.0
    
    def _detect_screen_info(self):
        """اكتشاف معلومات الشاشة"""
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                geometry = screen.geometry()
                
                self.screen_info = {
                    'width': geometry.width(),
                    'height': geometry.height(),
                    'dpi': screen.logicalDotsPerInch(),
                    'physical_dpi': screen.physicalDotsPerInch(),
                    'device_pixel_ratio': screen.devicePixelRatio(),
                    'scale_factor': screen.logicalDotsPerInch() / 96.0
                }
                
                print(f"📱 معلومات الشاشة: {self.screen_info['width']}x{self.screen_info['height']}")
                print(f"📏 DPI: {self.screen_info['dpi']}, نسبة البكسل: {self.screen_info['device_pixel_ratio']}")
                
        except Exception as e:
            print(f"تحذير: فشل في اكتشاف معلومات الشاشة: {e}")
            self._set_default_screen_info()
    
    def _calculate_scaling_factors(self):
        """حساب عوامل التكبير"""
        try:
            # عامل التكبير الأساسي
            base_scale = max(self.dpi_scale, self.screen_info.get('scale_factor', 1.0))
            
            # تعديل حسب حجم الشاشة
            screen_width = self.screen_info.get('width', 1920)
            screen_height = self.screen_info.get('height', 1080)
            
            # عامل تكبير الخط
            if screen_width <= 1366:
                self.font_scale = base_scale * 0.9  # تصغير للشاشات الصغيرة
            elif screen_width >= 2560:
                self.font_scale = base_scale * 1.1  # تكبير للشاشات الكبيرة
            else:
                self.font_scale = base_scale
            
            print(f"⚖️ عامل تكبير الخط: {self.font_scale:.2f}")
            
        except Exception as e:
            print(f"تحذير: فشل في حساب عوامل التكبير: {e}")
            self.font_scale = 1.0
    
    def _set_default_values(self):
        """تعيين القيم الافتراضية"""
        self.dpi_scale = 1.0
        self.font_scale = 1.0
        self.is_high_dpi = False
        self._set_default_screen_info()
    
    def _set_default_screen_info(self):
        """تعيين معلومات الشاشة الافتراضية"""
        self.screen_info = {
            'width': 1920,
            'height': 1080,
            'dpi': 96,
            'physical_dpi': 96,
            'device_pixel_ratio': 1.0,
            'scale_factor': 1.0
        }
    
    def get_scaled_size(self, base_size):
        """حساب الحجم المكبر"""
        if isinstance(base_size, (int, float)):
            return int(base_size * self.dpi_scale)
        elif isinstance(base_size, QSize):
            return QSize(
                int(base_size.width() * self.dpi_scale),
                int(base_size.height() * self.dpi_scale)
            )
        return base_size
    
    def get_scaled_font_size(self, base_font_size):
        """حساب حجم الخط المكبر"""
        scaled_size = int(base_font_size * self.font_scale)
        # حدود آمنة
        return max(8, min(scaled_size, 24))
    
    def get_safe_popup_size(self, base_width=300, base_height=200, max_items=6):
        """حساب حجم آمن للعناصر المنبثقة"""
        screen_width = self.screen_info['width']
        screen_height = self.screen_info['height']
        
        # حساب الحد الأقصى الآمن
        max_width = int(screen_width * 0.35)  # 35% من عرض الشاشة
        max_height = int(screen_height * 0.25)  # 25% من ارتفاع الشاشة
        
        # تطبيق التكبير
        scaled_width = self.get_scaled_size(base_width)
        scaled_height = self.get_scaled_size(base_height)
        
        # تعديل حسب عدد العناصر
        item_height = self.get_scaled_size(30)
        calculated_height = scaled_height + (min(max_items, 6) * item_height)
        
        # تطبيق الحدود
        final_width = min(scaled_width, max_width)
        final_height = min(calculated_height, max_height)
        
        return QSize(max(200, final_width), max(150, final_height))
    
    def setup_widget_dpi(self, widget):
        """إعداد DPI للعنصر"""
        if not isinstance(widget, QWidget):
            return
        
        try:
            # تطبيق الخط المكبر
            font = widget.font()
            current_size = font.pointSize()
            if current_size > 0:
                new_size = self.get_scaled_font_size(current_size)
                font.setPointSize(new_size)
                widget.setFont(font)
            
            # تطبيق الحجم المكبر للعناصر المنبثقة
            if isinstance(widget, QCompleter):
                self.setup_completer_dpi(widget)
            elif isinstance(widget, QDialog):
                self.setup_dialog_dpi(widget)
                
        except Exception as e:
            print(f"تحذير: فشل في إعداد DPI للعنصر: {e}")
    
    def setup_completer_dpi(self, completer):
        """إعداد DPI للـ QCompleter"""
        try:
            popup = completer.popup()
            if popup:
                # حساب الحجم الآمن
                safe_size = self.get_safe_popup_size()
                
                # تطبيق الحجم
                popup.setMinimumSize(safe_size.width() - 50, safe_size.height() - 50)
                popup.setMaximumSize(safe_size.width() + 50, safe_size.height() + 50)
                
                # تطبيق الخط
                font = popup.font()
                font_size = self.get_scaled_font_size(12)
                font.setPointSize(font_size)
                popup.setFont(font)
                
                # تحديد عدد العناصر المرئية
                max_items = 6 if self.is_high_dpi else 8
                completer.setMaxVisibleItems(max_items)
                
        except Exception as e:
            print(f"تحذير: فشل في إعداد DPI للـ Completer: {e}")
    
    def setup_dialog_dpi(self, dialog):
        """إعداد DPI للحوار"""
        try:
            # تكبير حجم الحوار
            current_size = dialog.size()
            new_size = self.get_scaled_size(current_size)
            dialog.resize(new_size)
            
            # تطبيق الخط على جميع العناصر الفرعية
            self._apply_font_to_children(dialog)
            
        except Exception as e:
            print(f"تحذير: فشل في إعداد DPI للحوار: {e}")
    
    def _apply_font_to_children(self, parent):
        """تطبيق الخط على جميع العناصر الفرعية"""
        try:
            for child in parent.findChildren(QWidget):
                font = child.font()
                current_size = font.pointSize()
                if current_size > 0:
                    new_size = self.get_scaled_font_size(current_size)
                    font.setPointSize(new_size)
                    child.setFont(font)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق الخط على العناصر الفرعية: {e}")
    
    def get_responsive_stylesheet(self):
        """إنشاء stylesheet متجاوب"""
        font_size = self.get_scaled_font_size(14)
        small_font = self.get_scaled_font_size(12)
        large_font = self.get_scaled_font_size(16)
        
        padding = self.get_scaled_size(8)
        margin = self.get_scaled_size(5)
        border_radius = self.get_scaled_size(6)
        
        return f"""
        /* أنماط متجاوبة للـ DPI */
        QWidget {{
            font-size: {font_size}px;
        }}
        
        QCompleter QAbstractItemView {{
            font-size: {small_font}px;
            padding: {padding//2}px;
            border-radius: {border_radius}px;
            max-height: {self.get_scaled_size(250)}px;
            min-width: {self.get_scaled_size(200)}px;
        }}
        
        QCompleter QAbstractItemView::item {{
            padding: {padding}px {padding*2}px;
            min-height: {self.get_scaled_size(25)}px;
        }}
        
        QPushButton {{
            font-size: {font_size}px;
            padding: {padding}px {padding*2}px;
            border-radius: {border_radius}px;
            min-height: {self.get_scaled_size(30)}px;
        }}
        
        QLineEdit, QComboBox {{
            font-size: {font_size}px;
            padding: {padding//2}px;
            min-height: {self.get_scaled_size(25)}px;
        }}
        
        QLabel {{
            font-size: {font_size}px;
        }}
        
        QTableWidget {{
            font-size: {small_font}px;
        }}
        
        QHeaderView::section {{
            font-size: {font_size}px;
            padding: {padding}px;
            min-height: {self.get_scaled_size(30)}px;
        }}
        """
    
    def print_system_info(self):
        """طباعة معلومات النظام للتشخيص"""
        print("\n" + "="*50)
        print("🔍 معلومات النظام والشاشة")
        print("="*50)
        print(f"💻 نظام التشغيل: {sys.platform}")
        print(f"🖥️ عامل تكبير DPI: {self.dpi_scale:.2f}")
        print(f"📏 عامل تكبير الخط: {self.font_scale:.2f}")
        print(f"🔍 شاشة عالية الدقة: {'نعم' if self.is_high_dpi else 'لا'}")
        print(f"📱 دقة الشاشة: {self.screen_info['width']}x{self.screen_info['height']}")
        print(f"📊 DPI: {self.screen_info['dpi']}")
        print(f"⚖️ نسبة البكسل: {self.screen_info['device_pixel_ratio']}")
        print("="*50)

    def get_dpi_info(self):
        """الحصول على معلومات DPI"""
        return {
            'dpi': self.screen_info.get('dpi', 96),
            'scale_factor': self.dpi_scale,
            'font_scale': self.font_scale,
            'is_high_dpi': self.is_high_dpi,
            'screen_size': f"{self.screen_info.get('width', 1920)}x{self.screen_info.get('height', 1080)}"
        }

    def get_screen_size(self):
        """الحصول على حجم الشاشة"""
        try:
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                if screen:
                    return screen.size()

            # قيم افتراضية
            from PyQt5.QtCore import QSize
            return QSize(self.screen_info.get('width', 1280), self.screen_info.get('height', 1024))

        except Exception as e:
            print(f"خطأ في الحصول على حجم الشاشة: {e}")
            from PyQt5.QtCore import QSize
            return QSize(1280, 1024)

    def update_dpi_settings(self):
        """تحديث إعدادات DPI"""
        try:
            self._detect_system_settings()
            print("✅ تم تحديث إعدادات DPI")
        except Exception as e:
            print(f"❌ خطأ في تحديث إعدادات DPI: {e}")


# إنشاء مثيل عام
dpi_manager = DPIManager()
