from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QComboBox, QLabel, QTableWidget, QTableWidgetItem,
    QDateEdit, QMessageBox, QFrame, QHeaderView, QGridLayout
)
from utils.dpi_manager import dpi_manager
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QColor, QFont
from sqlalchemy.orm import Session
from sqlalchemy import func
from database.models import Transaction, TransactionType, Product, Customer, Supplier
from utils.theme_manager import theme_manager

class AccountingWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        # إعداد DPI للواجهة
        try:
            dpi_manager.setup_widget_dpi(self)
        except:
            pass
        self.engine = engine
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # إطار التحكم
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
            }
            QLabel {
                padding: 5px;
            }
        """)
        controls_layout = QGridLayout()
        control_frame.setLayout(controls_layout)
        
        # اختيار نوع التقرير والفترة
        self.report_type = QComboBox()
        self.report_type.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 250px;
            }
        """)
        self.report_type.addItems([
            "قائمة الدخل",
            "الميزانية العمومية",
            "دفتر الأستاذ العام"
        ])
        
        self.start_date = QDateEdit()
        self.end_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.end_date.setDate(QDate.currentDate())
        
        date_style = """
            QDateEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
        """
        self.start_date.setStyleSheet(date_style)
        self.end_date.setStyleSheet(date_style)
        
        generate_btn = QPushButton("عرض التقرير")
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
        """)
        
        controls_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        controls_layout.addWidget(self.report_type, 0, 1)
        controls_layout.addWidget(QLabel("من:"), 0, 2)
        controls_layout.addWidget(self.start_date, 0, 3)
        controls_layout.addWidget(QLabel("إلى:"), 0, 4)
        controls_layout.addWidget(self.end_date, 0, 5)
        controls_layout.addWidget(generate_btn, 0, 6)
        
        layout.addWidget(control_frame)
        
        # جدول التقرير
        report_frame = QFrame()
        report_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        report_layout = QVBoxLayout()
        report_frame.setLayout(report_layout)
        
        self.report_table = QTableWidget()
        self.report_table.setStyleSheet("""
            QTableWidget {
                border: none;
            }
            QTableWidget::item {
                padding: 10px;
            }
            QHeaderView::section {
                padding: 10px;
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
            }
        """)
        report_layout.addWidget(self.report_table)
        layout.addWidget(report_frame)
        
        # ربط الأحداث
        generate_btn.clicked.connect(self.generate_report)
        self.report_type.currentTextChanged.connect(self.setup_table_headers)
        
        # إعداد الجدول الافتراضي
        self.setup_table_headers()
        
    def setup_table_headers(self):
        report_type = self.report_type.currentText()
        if report_type == "قائمة الدخل":
            headers = ["البند", "مدين", "دائن", "الرصيد"]
        elif report_type == "الميزانية العمومية":
            headers = ["البند", "الأصول", "الخصوم وحقوق الملكية"]
        else:  # دفتر الأستاذ
            headers = ["التاريخ", "البيان", "مدين", "دائن", "الرصيد"]
            
        self.report_table.setColumnCount(len(headers))
        self.report_table.setHorizontalHeaderLabels(headers)
        
        # تنسيق عرض الجدول
        header = self.report_table.horizontalHeader()
        header.setStretchLastSection(False)
        for i in range(len(headers)):
            if i == 1:  # جعل عمود البيان مرناً
                header.setSectionResizeMode(i, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.Fixed)
                header.resizeSection(i, 120)
        
    def create_table_item(self, text, bg_color=None, is_bold=False, alignment=Qt.AlignCenter):
        item = QTableWidgetItem(str(text))
        if bg_color:
            item.setBackground(bg_color)
        if is_bold:
            font = QFont()
            font.setBold(True)
            item.setFont(font)
        item.setTextAlignment(alignment)
        # إضافة حشو للعنصر عن طريق تعيين حد أدنى للعرض
        from PyQt5.QtCore import QSize
        current_size = item.sizeHint()
        new_size = QSize(current_size.width() + 20, current_size.height() + 10)
        item.setSizeHint(new_size)
        return item
        
    def generate_report(self):
        report_type = self.report_type.currentText()
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()

        try:
            print(f"🔍 توليد تقرير: {report_type} من {start_date} إلى {end_date}")

            if report_type == "قائمة الدخل":
                self.generate_income_statement(start_date, end_date)
            elif report_type == "الميزانية العمومية":
                self.generate_balance_sheet()
            else:
                self.generate_general_ledger(start_date, end_date)

            print("✅ تم توليد التقرير بنجاح")

        except ImportError as e:
            error_msg = f"خطأ في استيراد المكتبات المطلوبة:\n{str(e)}\n\nتأكد من تثبيت جميع المكتبات المطلوبة"
            print(f"❌ خطأ استيراد: {e}")
            QMessageBox.critical(self, "خطأ في الاستيراد", error_msg)

        except Exception as e:
            error_msg = f"حدث خطأ أثناء توليد التقرير:\n{str(e)}\n\nتفاصيل الخطأ:\n{type(e).__name__}"
            print(f"❌ خطأ في التقرير: {e}")
            print(f"نوع الخطأ: {type(e).__name__}")
            QMessageBox.critical(self, "خطأ في التقرير", error_msg)
            
    def generate_income_statement(self, start_date, end_date):
        try:
            print(f"🔍 بدء توليد قائمة الدخل...")

            with Session(self.engine) as session:
                print(f"✅ تم الاتصال بقاعدة البيانات")

                # المبيعات
                print(f"🔍 جلب بيانات المبيعات...")
                sales = session.query(func.sum(Transaction.total_amount)).filter(
                    Transaction.type == TransactionType.SALE,
                    Transaction.date.between(start_date, end_date)
                ).scalar() or 0
                print(f"✅ المبيعات: {sales}")

                # تكلفة المبيعات
                print(f"🔍 جلب بيانات المشتريات...")
                purchases = session.query(func.sum(Transaction.total_amount)).filter(
                    Transaction.type == TransactionType.PURCHASE,
                    Transaction.date.between(start_date, end_date)
                ).scalar() or 0
                print(f"✅ المشتريات: {purchases}")

                # الإيرادات الأخرى
                print(f"🔍 جلب بيانات الإيرادات الأخرى...")
                other_income = session.query(func.sum(Transaction.total_amount)).filter(
                    Transaction.type == TransactionType.INCOME,
                    Transaction.date.between(start_date, end_date)
                ).scalar() or 0
                print(f"✅ الإيرادات الأخرى: {other_income}")

                # المصروفات
                print(f"🔍 جلب بيانات المصروفات...")
                expenses = session.query(func.sum(Transaction.total_amount)).filter(
                    Transaction.type == TransactionType.EXPENSE,
                    Transaction.date.between(start_date, end_date)
                ).scalar() or 0
                print(f"✅ المصروفات: {expenses}")

                # عرض النتائج
                print(f"🔍 بدء عرض النتائج في الجدول...")
                self.report_table.setRowCount(6)

                for row in range(6):
                    bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")

                    if row == 0:  # المبيعات
                        self.report_table.setItem(row, 0, self.create_table_item("المبيعات", bg_color))
                        self.report_table.setItem(row, 2, self.create_table_item(f"{sales:,.2f}", bg_color))

                    elif row == 1:  # تكلفة المبيعات
                        self.report_table.setItem(row, 0, self.create_table_item("تكلفة المبيعات", bg_color))
                        self.report_table.setItem(row, 1, self.create_table_item(f"{purchases:,.2f}", bg_color))

                    elif row == 2:  # مجمل الربح
                        gross_profit = sales - purchases
                        self.report_table.setItem(row, 0, self.create_table_item("مجمل الربح", bg_color, True))
                        self.report_table.setItem(row, 3, self.create_table_item(f"{gross_profit:,.2f}", bg_color, True))

                    elif row == 3:  # الإيرادات الأخرى
                        self.report_table.setItem(row, 0, self.create_table_item("إيرادات أخرى", bg_color))
                        self.report_table.setItem(row, 2, self.create_table_item(f"{other_income:,.2f}", bg_color))

                    elif row == 4:  # المصروفات
                        self.report_table.setItem(row, 0, self.create_table_item("المصروفات", bg_color))
                        self.report_table.setItem(row, 1, self.create_table_item(f"{expenses:,.2f}", bg_color))

                    else:  # صافي الربح
                        net_profit = gross_profit + other_income - expenses
                        self.report_table.setItem(row, 0, self.create_table_item("صافي الربح", QColor("#E2E6EA"), True))
                        self.report_table.setItem(row, 3, self.create_table_item(f"{net_profit:,.2f}", QColor("#E2E6EA"), True))

                print(f"✅ تم عرض قائمة الدخل بنجاح")

        except Exception as e:
            print(f"❌ خطأ في توليد قائمة الدخل: {e}")
            raise
            
    def generate_balance_sheet(self):
        try:
            print(f"🔍 بدء توليد الميزانية العمومية...")

            with Session(self.engine) as session:
                print(f"✅ تم الاتصال بقاعدة البيانات")

                # الأصول
                # المخزون
                print(f"🔍 حساب قيمة المخزون...")
                inventory_value = session.query(
                    func.sum(Product.quantity * Product.purchase_price)
                ).scalar() or 0
                print(f"✅ قيمة المخزون: {inventory_value}")

                # أرصدة العملاء
                print(f"🔍 حساب أرصدة العملاء...")
                accounts_receivable = session.query(
                    func.sum(Customer.balance)
                ).scalar() or 0
                print(f"✅ أرصدة العملاء: {accounts_receivable}")

                # الخصوم
                # أرصدة الموردين
                print(f"🔍 حساب أرصدة الموردين...")
                accounts_payable = session.query(
                    func.sum(Supplier.balance)
                ).scalar() or 0
                print(f"✅ أرصدة الموردين: {accounts_payable}")

                # رأس المال
                capital = inventory_value + accounts_receivable - accounts_payable
                print(f"✅ رأس المال: {capital}")

                # عرض النتائج
                print(f"🔍 بدء عرض الميزانية العمومية في الجدول...")
                self.report_table.setRowCount(7)

                for row in range(7):
                    bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")

                    if row == 0:  # عنوان الأصول
                        self.report_table.setItem(row, 0, self.create_table_item("الأصول", bg_color, True))

                    elif row == 1:  # المخزون
                        self.report_table.setItem(row, 0, self.create_table_item("المخزون", bg_color))
                        self.report_table.setItem(row, 1, self.create_table_item(f"{inventory_value:,.2f}", bg_color))

                    elif row == 2:  # أرصدة العملاء
                        self.report_table.setItem(row, 0, self.create_table_item("أرصدة العملاء", bg_color))
                        self.report_table.setItem(row, 1, self.create_table_item(f"{accounts_receivable:,.2f}", bg_color))

                    elif row == 3:  # إجمالي الأصول
                        total_assets = inventory_value + accounts_receivable
                        self.report_table.setItem(row, 0, self.create_table_item("إجمالي الأصول", bg_color, True))
                        self.report_table.setItem(row, 1, self.create_table_item(f"{total_assets:,.2f}", bg_color, True))

                    elif row == 4:  # عنوان الخصوم
                        self.report_table.setItem(row, 0, self.create_table_item("الخصوم", bg_color, True))

                    elif row == 5:  # أرصدة الموردين
                        self.report_table.setItem(row, 0, self.create_table_item("أرصدة الموردين", bg_color))
                        self.report_table.setItem(row, 2, self.create_table_item(f"{accounts_payable:,.2f}", bg_color))

                    else:  # رأس المال
                        self.report_table.setItem(row, 0, self.create_table_item("رأس المال", QColor("#E2E6EA"), True))
                        self.report_table.setItem(row, 2, self.create_table_item(f"{capital:,.2f}", QColor("#E2E6EA"), True))

                print(f"✅ تم عرض الميزانية العمومية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في توليد الميزانية العمومية: {e}")
            raise
            
    def generate_general_ledger(self, start_date, end_date):
        try:
            print(f"🔍 بدء توليد دفتر الأستاذ العام...")

            with Session(self.engine) as session:
                print(f"✅ تم الاتصال بقاعدة البيانات")

                transactions = session.query(Transaction).filter(
                    Transaction.date.between(start_date, end_date)
                ).order_by(Transaction.date).all()

                print(f"✅ تم جلب {len(transactions)} معاملة")

                self.report_table.setRowCount(len(transactions))
                balance = 0

                for row, trans in enumerate(transactions):
                    bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")

                    self.report_table.setItem(row, 0,
                        self.create_table_item(trans.date.strftime("%Y-%m-%d"), bg_color))

                    description = ""
                    if trans.type == TransactionType.SALE:
                        description = f"مبيعات - {trans.customer.name if trans.customer else ''}"
                    elif trans.type == TransactionType.PURCHASE:
                        description = f"مشتريات - {trans.supplier.name if trans.supplier else ''}"
                    else:
                        description = trans.notes or str(trans.type.value)

                    self.report_table.setItem(row, 1, self.create_table_item(description, bg_color))

                    if trans.type in [TransactionType.SALE, TransactionType.INCOME]:
                        self.report_table.setItem(row, 2, self.create_table_item("", bg_color))
                        self.report_table.setItem(row, 3, self.create_table_item(f"{trans.total_amount:,.2f}", bg_color))
                        balance += trans.total_amount
                    else:
                        self.report_table.setItem(row, 2, self.create_table_item(f"{trans.total_amount:,.2f}", bg_color))
                        self.report_table.setItem(row, 3, self.create_table_item("", bg_color))
                        balance -= trans.total_amount

                    self.report_table.setItem(row, 4, self.create_table_item(f"{balance:,.2f}", bg_color))

                print(f"✅ تم عرض دفتر الأستاذ العام بنجاح")

        except Exception as e:
            print(f"❌ خطأ في توليد دفتر الأستاذ العام: {e}")
            raise

    def update_theme(self):
        """تحديث ألوان الثيم"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث الألوان
        colors = theme_manager.get_colors()

        # تحديث إطار التحكم
        control_style = f"""
            QFrame {{
                background-color: {colors['card_bg']};
                border-radius: 5px;
                padding: 15px;
                border: 1px solid {colors['border_color']};
            }}
            QLabel {{
                padding: 5px;
                color: {colors['primary_text']};
            }}
        """

        # تحديث القوائم المنسدلة والتواريخ
        combo_style = f"""
            QComboBox, QDateEdit {{
                padding: 8px;
                border: 1px solid {colors['border_color']};
                border-radius: 4px;
                min-width: 150px;
                background-color: {colors['input_bg']};
                color: {colors['input_text']};
            }}
            QComboBox:focus, QDateEdit:focus {{
                border-color: {colors['input_focus']};
            }}
        """

        # تحديث الأزرار
        button_style = theme_manager.get_stylesheet("button")

        # تحديث إطار التقرير
        report_style = f"""
            QFrame {{
                background-color: {colors['card_bg']};
                border: 1px solid {colors['border_color']};
                border-radius: 5px;
                margin-top: 10px;
            }}
        """

        # تحديث الجدول
        self.report_table.setStyleSheet(theme_manager.get_stylesheet("table"))

        # تطبيق الأنماط
        if hasattr(self, 'report_type'):
            self.report_type.setStyleSheet(combo_style)
        if hasattr(self, 'start_date'):
            self.start_date.setStyleSheet(combo_style)
        if hasattr(self, 'end_date'):
            self.end_date.setStyleSheet(combo_style)

        # إعادة تحديث التقرير لتطبيق الألوان الجديدة
        if hasattr(self, 'report_table') and self.report_table.rowCount() > 0:
            self.generate_report()