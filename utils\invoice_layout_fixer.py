"""
مصحح تخطيط الفواتير
يصلح مشاكل التخطيط في شاشات الفواتير للشاشات الصغيرة
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from utils.dpi_manager import dpi_manager
from utils.responsive_style_applier import responsive_style_applier


class InvoiceLayoutFixer:
    """مصحح تخطيط الفواتير"""
    
    def __init__(self):
        self.fixed_widgets = set()
    
    def fix_invoice_layout(self, widget):
        """إصلاح تخطيط الفاتورة"""
        if id(widget) in self.fixed_widgets:
            return  # تم إصلاحه مسبقاً
            
        try:
            # 1. إصلاح الرأس
            self._fix_header_section(widget)
            
            # 2. إصلاح منطقة البحث
            self._fix_search_section(widget)
            
            # 3. إصلاح الجدول
            self._fix_table_section(widget)
            
            # 4. إصلاح منطقة الأزرار
            self._fix_buttons_section(widget)
            
            # 5. إصلاح المسافات العامة
            self._fix_general_spacing(widget)
            
            # تسجيل أن هذا العنصر تم إصلاحه
            self.fixed_widgets.add(id(widget))
            
            print(f"✅ تم إصلاح تخطيط الفاتورة للشاشة الصغيرة")
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح تخطيط الفاتورة: {e}")
    
    def _fix_header_section(self, widget):
        """إصلاح قسم الرأس"""
        # البحث عن عناصر الرأس
        header_frames = []
        
        for child in widget.findChildren(QFrame):
            if self._is_header_frame(child):
                header_frames.append(child)
        
        for frame in header_frames:
            # تقليل ارتفاع الرأس
            frame.setMaximumHeight(responsive_style_applier.get_responsive_button_height(80))
            
            # تحسين التخطيط الداخلي
            if frame.layout():
                layout = frame.layout()
                layout.setSpacing(responsive_style_applier.get_responsive_spacing(3))
                layout.setContentsMargins(5, 3, 5, 3)
                
                # تقليل حجم العناصر الفرعية
                for i in range(layout.count()):
                    item = layout.itemAt(i)
                    if item and item.widget():
                        self._make_widget_compact(item.widget())
    
    def _fix_search_section(self, widget):
        """إصلاح قسم البحث"""
        # البحث عن حقول البحث
        search_inputs = widget.findChildren(QLineEdit)
        
        for input_field in search_inputs:
            if "بحث" in input_field.placeholderText() or "البحث" in input_field.placeholderText():
                # تحسين حقل البحث
                input_field.setMaximumHeight(responsive_style_applier.get_responsive_input_height())
                input_field.setMinimumWidth(150)
                
                # تحسين الإطار المحيط
                parent_frame = input_field.parent()
                if isinstance(parent_frame, QFrame) and parent_frame.layout():
                    layout = parent_frame.layout()
                    
                    # تحويل إلى تخطيط أفقي إذا كان عمودي
                    if isinstance(layout, QVBoxLayout):
                        self._convert_to_horizontal_layout(parent_frame)
                    
                    # تقليل المسافات
                    layout.setSpacing(responsive_style_applier.get_responsive_spacing(5))
    
    def _fix_table_section(self, widget):
        """إصلاح قسم الجدول"""
        tables = widget.findChildren(QTableWidget)
        
        for table in tables:
            # تحسين ارتفاع الصفوف
            row_height = responsive_style_applier.get_responsive_table_row_height(25)
            table.verticalHeader().setDefaultSectionSize(row_height)
            
            # تحسين عرض الأعمدة
            header = table.horizontalHeader()
            header.setStretchLastSection(True)
            
            # تقليل عرض الأعمدة للشاشات الصغيرة
            if responsive_style_applier.is_small_screen:
                for col in range(table.columnCount()):
                    current_width = table.columnWidth(col)
                    new_width = min(current_width, 120)
                    table.setColumnWidth(col, new_width)
            
            # تطبيق تنسيق متجاوب
            responsive_style_applier.apply_responsive_widget_style(table, "table")
    
    def _fix_buttons_section(self, widget):
        """إصلاح قسم الأزرار"""
        buttons = widget.findChildren(QPushButton)
        
        for button in buttons:
            # تحسين حجم الأزرار
            responsive_style_applier.apply_responsive_widget_style(button, "button")
            
            # تقليل عرض الأزرار للشاشات الصغيرة
            if responsive_style_applier.is_small_screen:
                button.setMaximumWidth(120)
                button.setMinimumWidth(80)
        
        # البحث عن إطارات الأزرار وتحسينها
        button_frames = []
        for child in widget.findChildren(QFrame):
            if self._is_button_frame(child):
                button_frames.append(child)
        
        for frame in button_frames:
            frame.setMaximumHeight(responsive_style_applier.get_responsive_button_height(50))
            
            if frame.layout():
                layout = frame.layout()
                layout.setSpacing(responsive_style_applier.get_responsive_spacing(5))
    
    def _fix_general_spacing(self, widget):
        """إصلاح المسافات العامة"""
        if widget.layout():
            layout = widget.layout()
            
            # تقليل المسافات
            spacing = responsive_style_applier.get_responsive_spacing(5)
            layout.setSpacing(spacing)
            
            # تقليل الهوامش
            margins = responsive_style_applier.get_responsive_padding(5)
            layout.setContentsMargins(margins, margins, margins, margins)
        
        # تطبيق على التخطيطات الفرعية
        for child in widget.findChildren(QWidget):
            if child.layout():
                child_layout = child.layout()
                child_layout.setSpacing(responsive_style_applier.get_responsive_spacing(3))
                child_layout.setContentsMargins(3, 3, 3, 3)
    
    def _is_header_frame(self, frame):
        """فحص إذا كان الإطار جزء من الرأس"""
        # فحص إذا كان يحتوي على عناصر رأس نموذجية
        if not frame.layout():
            return False
            
        has_date = False
        has_number = False
        has_customer = False
        
        for i in range(frame.layout().count()):
            item = frame.layout().itemAt(i)
            if item and item.widget():
                widget = item.widget()
                
                if isinstance(widget, QDateEdit):
                    has_date = True
                elif isinstance(widget, QLineEdit):
                    placeholder = widget.placeholderText().lower()
                    if "رقم" in placeholder or "number" in placeholder:
                        has_number = True
                elif isinstance(widget, QComboBox):
                    has_customer = True
        
        return has_date or has_number or has_customer
    
    def _is_button_frame(self, frame):
        """فحص إذا كان الإطار يحتوي على أزرار"""
        if not frame.layout():
            return False
            
        button_count = 0
        for i in range(frame.layout().count()):
            item = frame.layout().itemAt(i)
            if item and item.widget():
                if isinstance(item.widget(), QPushButton):
                    button_count += 1
        
        return button_count >= 2
    
    def _make_widget_compact(self, widget):
        """جعل العنصر مضغوط"""
        if isinstance(widget, QPushButton):
            widget.setMaximumHeight(responsive_style_applier.get_responsive_button_height(30))
        elif isinstance(widget, (QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox)):
            widget.setMaximumHeight(responsive_style_applier.get_responsive_input_height(25))
        elif isinstance(widget, QLabel):
            font = widget.font()
            font.setPointSize(responsive_style_applier.get_responsive_font_size(9))
            widget.setFont(font)
    
    def _convert_to_horizontal_layout(self, frame):
        """تحويل التخطيط إلى أفقي"""
        old_layout = frame.layout()
        if not isinstance(old_layout, QVBoxLayout):
            return
            
        # جمع العناصر
        widgets = []
        while old_layout.count() > 0:
            item = old_layout.takeAt(0)
            if item.widget():
                widgets.append(item.widget())
        
        # إنشاء تخطيط أفقي جديد
        new_layout = QHBoxLayout()
        new_layout.setSpacing(responsive_style_applier.get_responsive_spacing(5))
        
        for widget in widgets:
            new_layout.addWidget(widget)
        
        # استبدال التخطيط
        QWidget().setLayout(old_layout)  # حذف التخطيط القديم
        frame.setLayout(new_layout)
    
    def fix_all_invoice_widgets(self, main_window):
        """إصلاح جميع واجهات الفواتير في النافذة الرئيسية"""
        try:
            # البحث عن جميع واجهات الفواتير
            invoice_widgets = []
            
            # البحث في التبويبات
            if hasattr(main_window, 'main_tabs'):
                for i in range(main_window.main_tabs.count()):
                    tab_widget = main_window.main_tabs.widget(i)
                    if tab_widget:
                        # البحث عن واجهات الفواتير
                        sales_widgets = tab_widget.findChildren(QWidget)
                        for widget in sales_widgets:
                            class_name = widget.__class__.__name__
                            if 'sales' in class_name.lower() or 'purchase' in class_name.lower() or 'invoice' in class_name.lower():
                                invoice_widgets.append(widget)
            
            # إصلاح كل واجهة
            for widget in invoice_widgets:
                self.fix_invoice_layout(widget)
                
            print(f"✅ تم إصلاح {len(invoice_widgets)} واجهة فاتورة")
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح واجهات الفواتير: {e}")


# إنشاء مثيل عام
invoice_layout_fixer = InvoiceLayoutFixer()
